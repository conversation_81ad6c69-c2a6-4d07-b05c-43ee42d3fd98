<%@ page language="java" pageEncoding="UTF-8" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
    String params = null == request.getParameter("params") ? "" : request.getParameter("params");
    String allVerFlg = request.getParameter("allVerFlg");
    String ver = "201712251008";
%>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>产品线指标分析</title>
    <base href="<%=basePath%>">

    <link href="../../03images/ABP/logo.png?ver=<%=ver%>" rel="shortcut icon" type="image/x-icon"/>

    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <!-- 本地样式 -->
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
    <!-- jQuery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link href="../../00scripts/00lib/iview/4.4.0/styles/iview.css" rel="stylesheet" type="text/css"/>

    <!-- 本地不知道啥 -->
    <link rel="stylesheet" href="../../01css-portal/icomoon/style.css" rel="stylesheet" type="text/css"/>

    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>

    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>
    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <script type="text/javascript" src="../../common/filterTable.js"></script>
    <script type="text/javascript" src="../../common/defConst.js?ver=<%=ver%>"></script>

    <style>
        .ivu-tabs-content {
            height: calc(100% - 45px);
        }

        .ic-tag-span {
            height: 50px;
            line-height: 50px;
            font-size: 16px;
        }

        .ivu-tag-text {
            font-size: 14px;
        }

        .differ-cell {
            background: #ff9900;
        }

        .mini-grid-filterCell {
            border-right: 0;
            padding: 2px;
            height: 26px;
        }

        .ivu-select-selection {
            box-sizing: border-box;
            outline: 0;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            cursor: pointer;
            position: relative;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #dddee1;
            transition: all .2s ease-in-out;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 此样式为filterTable.js使用,隐藏其表头的水平滚动条 */
        .headClass .ivu-table-body {
            overflow: hidden !important;
        }

        /* 此样式为filterTable.js使用,表头过滤列添加背景色 */
        .headClass .ivu-table-body .ivu-table-row td {
            background-color: #f8f8f9;
        }

        /*强制表格文本两行超出后打点表示 */
        .lineClamp2 .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .lineClamp2 .ivu-tooltip {
            vertical-align: middle;
        }

        .report_warp_height .filter_one .data_area {
            height: calc(100% - 102px);
        }

        .cursor{
            cursor: pointer;
        }
    </style>
</head>
<body style="width:100%;height:100%;">
<div id="main" class="main" style="height: 100%; overflow: hidden;" v-cloak>
    <div class="layout-content layout-container report_warp_height" style="min-width: 1190px; width: 100%; height: 100%; position: relative">
        <div class="dmp_report_warp filter_one no_title">
            <div class="filter_warp filter_col_4">
                <div class="filter_col">
                    <label>年份</label>
                    <div class="filter_component">
                        <i-Select v-model="bpYearFlt" >
                            <i-Option v-for="bpYearMap in bpYearList" :value="bpYearMap.value" :key="bpYearMap.value">{{ bpYearMap.text }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>版本</label>
                    <div class="filter_component">
                        <i-Select v-model="bpVerIdFlt" filterable placeholder="请选择" label-in-value="true" @on-change="querySelectedProv()">
                            <i-Option v-for="bpVersion in bpVersionList" :value="bpVersion.cid" :key="bpVersion.cid">{{  'v' + parseFloat(bpVersion.verNo).toFixed(1)  }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>层级</label>
                    <div class="filter_component">
                        <Cascader v-model="proGradeValue" :data="proGradeTree" change-on-select @on-change="onGradeChange"></Cascader>
                    </div>
                </div>

                <div class="filter_col">
                    <label>产品线</label>
                    <div class="filter_component">
                        <i-Select v-model="plIds" multiple placeholder="请选择产品线"  @on-open-change = "queryPlData" @on-change="queryProvListByRoleAndBpVerId" multiple :max-tag-count="3" style="width: calc(100% - 60px);">
                            <i-Option v-for="m in plList" :value="m.id" :key="m.id">{{ m.defName }}</i-Option>
                        </i-Select>
                        <label>全选
                            <Checkbox v-model="checkBoxValue" @on-change="selectAllPlData"></Checkbox>
                        </label>
                    </div>
                </div>

                <div class="view_button oprts" style="display: inline-block;">
                    <i-button type="primary" @click="queryTableList">查询</i-button>
                    <i-button type="primary" @click="exportReportData" icon="ios-cloud-download-outline" :disabled="!exportAble">导出</i-button>
                </div>
            </div>

            <div class="data_area abp_new_table" ref="dataArea">
                <i-table border
                         :columns="columns"
                         :data="queryData"
                         class="table-noborder lineClamp2 dataClass"
                         :height="tableHeight"
                         :loading="tableLoading"
                         :disable-dblclick="true">
                </i-table>
            </div>
        </div>

        <!-- 导出 -->
        <iframe id="excelData" style="display:none;"></iframe>
    </div>
</div>

</body>
</html>

<iframe width="0" height="0" style="display:none;" id="exportExcelFrame"></iframe>

<script>
    var nowDate = new Date();
    var nowYear = nowDate.getFullYear();

    var vm =
        new Vue({
            el: '#main',
            data: function () {
                var sf = this;
                return {
                    year: '',
                    bpYearFlt: '',
                    bpYearList: [],

                    bpVerIdFlt: '',
                    bpVersionList: [],

                    areaList: [],
                    provList: [],
                    provCheckedList: [],

                    proGradeId: '',
                    proGradeValue: [],
                    proGradeTree: [],

                    plIds: [],
                    plList: [],
                    checkBoxValue: false,

                    columns: [],
                    columnsBase: [],

                    queryData: [],
                    tableHeight: '',
                    tableLoading: false,
                    exportAble: false,

                    isBSC: false,
                }
            },
            created: function () {
                var sf = this;
            },
            mounted: function () {
                var sf = this;

                sf.tableHeight = sf.$refs.dataArea.offsetHeight - 30;
                // sf.queryVerList();
                // sf.queryNewestVer();
                // sf.getCurrentYear();
                sf.listPlanVer();
                sf.loadCurrentUser();
                sf.queryProvListByRoleAndBpVerId();
                sf.queryPlData(true);
            },
            watch: {
                bpYearFlt: function (newVal, oldVal) {
                    var sf = this;
                    sf.findVerByYear();
                },
            },
            methods: {
                //获取url参数
                getQueryString: function (name) {
                    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                    var r = window.location.search.substr(1).match(reg);
                    if (r != null) return unescape(r[2]);
                    return null;
                },
                // 查询版本列表信息
                queryVerList: function () {
                    var sf = this;
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + '/v1/findVersionByBuCode',
                        contentType: "application/json;charset=utf-8",
                        success: function (text) {
                            if (!!text && !!text.data) {
                                sf.bpVersionList = text.data;
                                sf.bpVerIdFlt = sf.bpVersionList[0].cid;
                                sf.querySelectedProv();
                                // sf.getPlData();
                            }
                        },
                        error: function (text) {
                            sf.$Modal.error({
                                title: '查询错误',
                                content: '查询出错，请联系管理员。'
                            });
                        }
                    });
                },
                // 8853 只取最新版本
                // 8990 BU最大年度下的所有列表
                queryNewestVer: function () {
                    var sf = this;
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + '/v1/findVersion',
                        contentType: "application/json;charset=utf-8",
                        success: function (text) {
                            if (!!text && !!text.data) {
                                var data = text.data;
                                sf.bpVerIdFlt = data[0].cid;
                                var maxBUVer = data[0].name; // BU + 年份
                                for (var key in data) {
                                    var value = data[key];
                                    if (value.name == maxBUVer) {
                                        sf.bpVersionList.push(value);
                                    }
                                }
                                sf.querySelectedProv();
                            }
                        },
                        error: function (text) {
                            sf.$Modal.error({
                                title: '查询错误',
                                content: '查询最新经营计划版本数据错误，请联系管理员。'
                            });
                        }
                    });
                },
                getCurrentYear: function () {
                    var sf = this;
                    $.ajax({
                        type: 'get',
                        async: false,
                        url: linkus.location.abp + '/index/queryServerTime',
                        success: function (res) {
                            if (res.success) {
                                sf.year = res.data.currentYear;
                                sf.bpYearFlt = Number(res.data.currentYear);

                                sf.bpYearList.push({
                                    value: sf.bpYearFlt,
                                    text: sf.bpYearFlt,
                                })

                                // 超过12月1号 展示下一年
                                if (sf.isAfterDecemberFirst()) {
                                    sf.bpYearList.push({
                                        value: sf.bpYearFlt + 1,
                                        text: sf.bpYearFlt + 1,
                                    })
                                }

                            } else {
                                sf.$Message.warning(res.message);
                            }
                        },
                        error: function (res) {

                        }
                    });
                },
                listPlanVer: function () {
                    var sf = this;
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + '/planVer/list',
                        contentType: "application/json;charset=utf-8",
                        success: function (text) {

                            if (!!text && !!text.data && text.data.length > 0) {
                                var yearList = [];
                                text.data.forEach(function (item) {
                                    if (!yearList.includes(item.year)) {
                                        yearList.push(item.year);
                                    }
                                });
                                sf.bpYearFlt = yearList[0];
                                yearList.forEach(function (item) {
                                    sf.bpYearList.push({
                                        value: item,
                                        text: item,
                                    })
                                });

                            }
                        },
                        error: function (text) {
                            sf.$Modal.error({
                                title: '查询错误',
                                content: '查询出错，请联系管理员。'
                            });
                        }
                    });
                },
                isAfterDecemberFirst: function() {
                    var date = new Date();
                    var month = date.getMonth() + 1;
                    return month === 12 && date.getDate() >= 1;
                },
                //查询版本
                findVerByYear: function () {
                    var sf = this;
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + '/v1/findVerByYear',
                        data: {
                            year: sf.bpYearFlt,
                        },
                        contentType: "application/json;charset=utf-8",
                        success: function (text) {
                            if (!!text && !!text.data) {
                                sf.bpVersionList = [];
                                var data = text.data;
                                sf.bpVerIdFlt = data.length > 0 ? data[0].cid : '';
                                var maxBUVer = data.length > 0 ? data[0].name : '';
                                for (var key in data) {
                                    var value = data[key];
                                    if (value.name == maxBUVer) {
                                        sf.bpVersionList.push(value);
                                    }
                                }
                                //sf.querySelectedProv();
                            }
                        },
                        error: function (text) {
                            sf.$Modal.error({
                                title: '查询错误',
                                content: '查询经营分析版本数据错误，请联系管理员。'
                            });
                        }
                    });
                },
                // 根据版本id查出所选省份list
                querySelectedProv: function () {
                    var sf = this;
                    sf.provCheckedList = [];
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + "/v1/queryNeedForecastProvListByBpVerId",
                        async: false,
                        data: {
                            planVerId: sf.bpVerIdFlt
                        },
                        success: function (text) {
                            sf.areaList = text.data;
                            if (!sf.areaList) {
                                return;
                            }
                            //迭代区域
                            for (var i = 0; i < sf.areaList.length; i++) {
                                var areaMap = sf.areaList[i];
                                //选中个数
                                var checkedCnt = 0;
                                var provList = areaMap.provList;
                                //迭代省份
                                for (var j = 0; j < provList.length; j++) {
                                    if (provList[j].isSelected) {
                                        sf.provCheckedList.push(provList[j].provId);
                                    }
                                }
                            }
                        },
                        error: function (text) {
                            sf.$Message.warning('加载层级出错');
                        }
                    });
                },
                //查询当前登录人
                loadCurrentUser: function () {
                    var sf = this;
                    $.ajax({
                        url: linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
                        type: 'post',
                        dataType: 'JSON',
                    }).done(function (teSysUser) {
                        if ("BSC" === teSysUser.sbuName || "185" === teSysUser.sbuId) {
                            sf.isBSC = true;
                        }
                    });
                },
                // 查询省份
                queryProvListByRoleAndBpVerId: function () {
                    var sf = this;
                    var plIdList = [];
                    if (!!sf.plIds) {
                        plIdList = sf.plIds;
                    }
                    $.ajax({
                        type: "get",
                        data: {
                            resDeptOrPlId : plIdList
                        },
                        url: linkus.location.abp + '/v1/queryGradeTreeByGradeAndPlPower',
                        success: function (text) {
                            sf.provList = text.data;
                            // sf.submitCheckedProvList = text.data;
                            sf.transToProvTreeData();
                        },
                        error: function (text) {
                            sf.$Message.warning('加载省份出错');
                        }
                    });
                },
                // 将省份信息转换成树形结构 多加BU级
                transToProvTreeData: function () {
                    var sf = this;
                    var provList = sf.provList;

                    // 删除所有的children,以防止多次调用
                    provList.forEach(function (item) {
                        delete item.children;
                    });
                    let map = {}; //构建map
                    provList.forEach(prov => {
                        // bu 层级
                        map[prov.id] = {
                            'value': prov.id,
                            'label': prov.defName
                        };
                        map[prov.cndtItems[0].cid] = {
                            'value': prov.cndtItems[0].cid,
                            'label': prov.cndtItems[0].name,
                            'children': []
                        };
                        map[prov.cndtItems[1].cid] = {
                            'value': prov.cndtItems[1].cid,
                            'label': prov.cndtItems[1].name,
                            'children': []
                        };
                        map[prov.srcDef.srcDefId] = {
                            'value': prov.srcDef.srcDefId,
                            'label': prov.srcDef.srcDefName,
                            'children': []
                        };
                    });
                    let treeData = [];
                    provList.forEach(child => {
                        var areaId = child.cndtItems[1].cid;
                        var bigAreaId = child.cndtItems[0].cid;
                        var buId = child.srcDef.srcDefId;

                        const prov = map[child.id];
                        const area = map[areaId];
                        const bigArea = map[bigAreaId];
                        const bu = map[buId];
                        // 去重
                        if (!JSON.stringify(area.children).includes(JSON.stringify(prov))) {
                            area.children.push(prov);
                        }
                        if (!JSON.stringify(bigArea.children).includes(JSON.stringify(area))) {
                            bigArea.children.push(area);
                        }
                        if (!JSON.stringify(bu.children).includes(JSON.stringify(bigArea))) {
                            bu.children.push(bigArea);
                        }
                        if (!JSON.stringify(treeData).includes(JSON.stringify(bu))) {
                            treeData.push(bu);
                        }
                    })
                    sf.proGradeTree = treeData;
                },
                // 层级改变
                onGradeChange: function (selectedValue) {
                    var sf = this;
                    sf.proGradeId = selectedValue[selectedValue.length - 1];
                    sf.queryPlData(true);
                },
                // 查询有权限的产品线列表
                queryPlData: function (value) {
                    if (!value) {
                        return;
                    }
                    var _this = this;
                    var areaId = null;
                    if (!!_this.proGradeId) {
                        areaId = _this.proGradeId;
                    }
                    $.ajax({
                        type: "get",
                        url: linkus.location.abp + '/v1/queryPermiPl',
                        data: {
                            areaId: areaId
                        },
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        success: function (text) {
                            if (!text.success) {
                                _this.$Message.warning(text.message);
                                return;
                            }
                            _this.plList = text.data;
                        },
                        error: function (text) {
                            // _this.$Message.warning('查询产品线出错,请联系管理员');
                            return;
                        }
                    });
                },
                // 全选
                selectAllPlData: function () {
                    var sf = this;
                    if (sf.checkBoxValue) {
                        sf.plList.map((item) => {
                            sf.plIds.push(item.id)
                        })
                    } else {
                        sf.plIds = [];
                    }
                },
                // 查询表格数据
                queryTableList: function () {
                    var sf = this;

                    if (sf.bpVerIdFlt == '' || sf.bpVerIdFlt == null) {
                        return;
                    }
                    if (sf.proGradeId == '' || sf.proGradeId == null) {
                        sf.$Message.error({
                            content: '请选择层级！',
                            duration: 3
                        });
                        return;
                    }
                    if (sf.plIds.length === 0) {
                        sf.$Message.error({
                            content: '请选择产品线！',
                            duration: 3
                        });
                        return;
                    }


                    var param = {
                        planVerId: sf.bpVerIdFlt,
                        areaIds: [sf.proGradeId],
                        plIds: sf.plIds
                    }

                    sf.tableLoading = true;
                    sf.queryData = [];

                    $.ajax({
                        type: "post",
                        url: linkus.location.abp + '/v1/queryPlIndicators',
                        data: JSON.stringify(param),
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        success: function (res) {
                            sf.tableLoading = false;
                            if(res.success){
                                if(res.data.length > 0){
                                    sf.queryData = sf.handleData(res.data);
                                    sf.columns = sf.generateColumns(res.data[0].dataMap);
                                    sf.exportAble = true;
                                }
                            }else{
                                sf.$Message.error({
                                    content: res.message,
                                    duration: 3
                                });
                            }
                        },
                        error: function (res) {
                            sf.tableLoading = false;
                            sf.$Message.error({
                                content: '查询产品线指标错误，请联系管理员!',
                                duration: 3
                            });
                        }
                    });
                },
                handleData: function(data){
                    var sf = this;

                    var list = [];
                    data.forEach(function(item){
                        var item0 = {};
                        item0.indicator = item.indicator;
                        let i = 0;
                        for(const key in item.dataMap){
                            let col = 'col_' + i;
                            item0[col] = item.dataMap[key];
                            i++;
                        }
                        list.push(item0);
                    });

                    return list;
                },
                generateColumns: function(data){
                    var sf = this;

                    var columns = [];
                    var indicatorItem = {
                        title: '科目',
                        key: 'indicator',
                        width: 200,
                    }
                    columns.push(indicatorItem);

                    let i = 0;
                    for(const key in data){
                        let col = 'col_' + i;
                        let item = {
                            title: key,
                            key: col,
                            width: 100,
                        }
                        columns.push(item);
                        i++;
                    }
                    return columns;
                },
                //  导出
                exportReportData: function () {
                    var sf = this;

                    if (sf.bpVerIdFlt == '' || sf.bpVerIdFlt == null) {
                        return;
                    }
                    if (sf.proGradeId == '' || sf.proGradeId == null) {
                        sf.$Message.error({
                            content: '请选择层级！',
                            duration: 3
                        });
                        return;
                    }
                    if (sf.plIds.length === 0) {
                        sf.$Message.error({
                            content: '请选择产品线！',
                            duration: 3
                        });
                        return;
                    }

                    sf.exportAble = false;
                    sf.$Message.warning({
                        content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                        duration: 3
                    });

                    var param = {
                        planVerId: sf.bpVerIdFlt,
                        areaIds: [sf.proGradeId],
                        plIds: sf.plIds
                    }

                    function blobDataExport(downRes,fileName) {
                        var blob = new Blob([downRes])
                        var url = window.URL.createObjectURL(blob);
                        var aLink = document.createElement("a");
                        aLink.href = url;
                        aLink.style.display = "none";
                        aLink.setAttribute('download',fileName);
                        document.body.appendChild(aLink);
                        aLink.click();
                        document.body.removeChild(aLink);
                        window.URL.revokeObjectURL(url);
                    }

                    $.ajax({
                        url: linkus.location.abp + '/v1/exportPlIndicators',
                        type: 'post',
                        xhrFields: { responseType: "blob" },
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(param),
                        success: function (res) {
                            blobDataExport(res,'产品线指标分析.xlsx');
                            setTimeout(function() {
                                sf.exportAble = true;
                            },3000);
                        },
                        error: function (res) {
                            sf.$Message.error({
                                content: res,
                                duration: 3
                            });
                        }
                    });
                },
            }
        });
</script>
