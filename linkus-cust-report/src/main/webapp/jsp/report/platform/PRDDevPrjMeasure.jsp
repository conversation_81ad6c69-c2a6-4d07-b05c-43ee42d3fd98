<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    String ver = "20190219";
%>
<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>PRD研发项目度量报表</title>
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <!-- 本地样式 -->
    <link href="../../01css/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css" />
	<link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/iview/iview-cpnt.css" rel="stylesheet" type="text/css" />
    <!-- jQuery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <!-- vue -->
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>
    <!-- iview -->
    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css" />

    <link href="../../01css-portal/font/iconfont.css" rel="stylesheet" type="text/css" />
    <script src="../../01css-portal/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <script type="text/javascript" src="../../common/defConst.js"></script>

    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .ivu-table-fixed-right::before, .ivu-table-fixed::before {
            content: '';
            width: 100%;
            height: 1px;
            background-color: transparent;
            position: absolute;
            left: 0;
            bottom: 0;
            z-index: 4;
        }
        /*  查看/下载气泡提示样式 */
        .check:hover{
            color: #f90;
        }
        .member-list-box li div:hover{
            color: #f90;
        }
        .icon-xiazaide {
            /*  margin-left: 1em; */
            font-size: 12px;
            display: inline-flex;
        }
        .icon-xiazaide:hover{
            color: #f90;
        }
        .member-list-box li{
            margin: 0;
        }
        .ivu-table .ivu-poptip-popper{
            max-width: 400px;
        }
        .ivu-table .ivu-poptip-popper .ivu-poptip-body{
            padding: 0;
        }
        .ivu-poptip-body-content{
            overflow: unset !important;
        }
        .member-list-box{
            height: auto;
            max-height: 300px;
        }
        .api{
            height: auto !important;
        }
        /* 此样式为filterTable.js使用,隐藏其表头的水平滚动条 */
        .headClass .ivu-table-body {
            overflow: hidden !important;
        }
        .ivu-table .ivu-table-header{
            overflow: hidden;
        }
        .ivu-table{
            font-size: 12px;
        }
        /*强制表格文本两行超出后打点表示 */
        .member-list-box{
            margin: 0;
        }
        .member-list-box li{
            display: flex;
        }
        .member-list-box .fileName{
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block !important;
        }
        .member-list-box .icon-xiazaide{
            vertical-align: top;
        }
        .ivu-icon-ios-close-circle:before{
            content: "\F128";
        }
        /*强制表格文本一行超出后打点表示 */
        .lineClamp1 .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .view_table.prdDevTable {
            border: none !important;
        }

        .view_table.prdDevTable .ivu-table td, .view_table.prdDevTable .ivu-table th {
            border: 1px solid #e9eaec !important;
            border-right: none !important;
            border-bottom: none !important;
        }
        .details_modal .ivu-table td, .details_modal .ivu-table th {
            height: 40px !important;
        }
        .details_table .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }
    </style>
</head>

<body>
<%@include file="../../menu/headMenu.jsp" %>
<%@include file="../../menu/cnptPage.jsp"%>
<div class="main bg-light-grey report_body_height" id="main" style="overflow: hidden;" v-cloak>
    <div class="layout-content layout-container bg-white report_warp_height" style="min-width: 1190px; width: 100%; position: relative">
        <div class="blank-name" style="display: flex; align-items: center;">
            <div style="width: 184px;height:100%">
                <h1 style="height:100%">PRD研发项目度量报表</h1>
            </div>
            <span @click="openHelpDoc" class="iconfont icon-qbzuocesvg02 mr-rg10" style="color: #ff9900;cursor: pointer;font-size:16px;line-height: inherit;margin-left: 4px;"></span>
        </div>

        <div class="dmp_report_warp filter_one">
            <div class="filter_warp filter_col_3">
                <div class="filter_col">
                    <label>统计时间</label>
                    <div class="filter_component">
                        <Date-Picker type="daterange" v-model="date" placeholder="请选择统计时间" clearable @on-change="dateChange"
                                     style="width: 100%; display: inline-block;">
                        </Date-Picker>
                    </div>
                </div>
                <div class="view_button">
                    <i-button type="primary" @click="queryTableData">查询</i-button>
                    <i-button type="primary" @click="exportData">导出</i-button>
                </div>
            </div>
            <div class="data_area" ref="dataArea">
                <i-table
                    :data="queryData"
                    :columns="columns"
                    :height="tableHeight"
                    class="table-noborder lineClamp1 view_table prdDevTable"
                    :loading="loading" :span-method="handleTableSpan">
                </i-table>
                <%--<Page
                        :total="queryDataTotal" placement="top" style="margin-top: 10px;"
                        @on-change="onPageNumChange"
                        @on-page-size-change="onPageSizeChange"
                        :page-size="pageSize"
                        :page-size-opts="pageSizeOpts"
                        show-sizer show-total>
                </Page>--%>
            </div>

        </div>
    </div>

    <Modal v-model="showDetailsModal" :title="detailsTitle" :width="800" class-name="vertical-center-modal details_modal" footer-hide @on-cancel="closeDetails">
        <i-table :data="detailsData" :columns="detailsColumns" class="details_table" :loading="detailsTableLoading" stripe></i-table>
    </Modal>
</div>

</body>
<script>
    //控制登录失效后HTML页面跳转登录页
    verifyLogin();
    Date.prototype.format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1,                 //月份
            "d+": this.getDate(),                    //日
            "h+": this.getHours(),                   //小时
            "m+": this.getMinutes(),                 //分
            "s+": this.getSeconds(),                 //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds()             //毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };
    var vue = new Vue({
        el: '#main',
        data: function () {
            var sf = this;
            return {
                loading: false,
                tableHeight: 250,
                columns: [
                    {
                        title: '序',
                        type: 'index',
                        width: 60,
                    },
                    {
                        title: '产研中心',
                        key: 'prdResearchCenter',
                        width: 160,
                        render: function(h, params) {
                            var name = params.row.prdResearchCenter;
                            if(name){
                                return h('Tooltip', {
                                    props: { placement: 'top',transfer:true }
                                }, [
                                    name,
                                    h('span', { slot: 'content', style: { whiteSpace: 'normal', wordBreak: 'break-all' } },
                                    name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '产品名称',
                        key: 'prdName',
                        width: 200,
                        render: function(h, params) {
                            var name = params.row.prdName;
                            if(name){
                                return h('Tooltip', {
                                    props: { placement: 'top',transfer:true }
                                }, [
                                    name,
                                    h('span', { slot: 'content', style: { whiteSpace: 'normal', wordBreak: 'break-all' } },
                                    name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '年中版本号',
                        key: 'midYearVersionNumber',
                        width: 120,
                    },
                    {
                        title: 'Feature数量',
                        key: 'featureCount',
                        width: 90,
                        render: function(h,params){
                            var featureCount = params.row.featureCount;
                            if(featureCount > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },featureCount);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '需求总数',
                        key: 'totalReqs',
                        width: 90,
                        render: function(h,params){
                            var totalReqs = params.row.totalReqs;
                            if(totalReqs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },totalReqs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '需求新增数',
                        key: 'newReqs',
                        width: 90,
                        render: function(h,params){
                            var newReqs = params.row.newReqs;
                            if(newReqs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },newReqs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '需求关闭数',
                        key: 'closedReqs',
                        width: 90,
                        render: function(h,params){
                            var closedReqs = params.row.closedReqs;
                            if(closedReqs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },closedReqs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '派发任务数',
                        key: 'dispatchedTaskCount',
                        width: 90,
                        render: function(h,params){
                            var dispatchedTaskCount = params.row.dispatchedTaskCount;
                            if(dispatchedTaskCount > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },dispatchedTaskCount);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '任务平均完成天数',
                        key: 'avgCompletionDays',
                        width: 120,
                    },
                    {
                        title: '<=14天数量',
                        key: 'tasksCompletedIn14Days',
                        width: 100,
                        render: function(h,params){
                            var tasksCompletedIn14Days = params.row.tasksCompletedIn14Days;
                            if(tasksCompletedIn14Days > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },tasksCompletedIn14Days);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '平均开发天数',
                        key: 'avgDevelopmentDays',
                        width: 100,
                    },
                    {
                        title: '平均测试天数',
                        key: 'avgTestingDays',
                        width: 100,
                    },
                    {
                        title: '测试用例数',
                        key: 'testCases',
                        width: 90,
                        render: function(h,params){
                            var testCases = params.row.testCases;
                            if(testCases > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },testCases);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '通过数',
                        key: 'passedTestCases',
                        width: 90,
                        render: function(h,params){
                            var passedTestCases = params.row.passedTestCases;
                            if(passedTestCases > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },passedTestCases);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '测试用例覆盖率',
                        key: 'testCaseCoverage',
                        width: 110,
                        render: function(h, params) {
                            var value = params.row.testCaseCoverage;
                            if(!!value){
                                return h('span', (value * 100).toFixed(2) + '%');
                            }
                            return value;
                        },
                    },
                    {
                        title: '缺陷新增数',
                        key: 'newBugs',
                        width: 90,
                        render: function(h,params){
                            var newBugs = params.row.newBugs;
                            if(newBugs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },newBugs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '缺陷累计数',
                        key: 'cumulativeBugs',
                        width: 90,
                        render: function(h,params){
                            var cumulativeBugs = params.row.cumulativeBugs;
                            if(cumulativeBugs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },cumulativeBugs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '缺陷关闭数',
                        key: 'closedBugs',
                        width: 90,
                        render: function(h,params){
                            var closedBugs = params.row.closedBugs;
                            if(closedBugs > 0){
                                return h('a',{
                                    on: {
                                        click: ()=>{
                                            sf.detailsTitle = params.column.title + '明细';
                                            sf.showDetailsModal = true;
                                            sf.queryDetails(params.column.key,params.row.dmpVer,params.row.prdCtlgId);
                                        }
                                    }
                                },closedBugs);
                            }else{
                                return h('span',0);
                            }
                        }
                    },
                    {
                        title: '缺陷关闭率',
                        key: 'bugClosureRate',
                        width: 90,
                        render: function(h, params) {
                            var value = params.row.bugClosureRate;
                            if(!!value){
                                return h('span', (value * 100).toFixed(2) + '%');
                            }
                            return value;
                        },
                    },
                ],
                queryData: [],
                queryDataTotal: 0,
                pageNum: 0,
                pageSize: 10,
                pageSizeOpts: [10,20,50],
                date: [],
                tableDataCol: [],

                uploadDesc: 'PRD研发项目度量报表',
                showDetailsModal: false,
                detailsTitle: '',
                detailsData: [],
                detailsColumns: [
                    {
                        title: '序',
                        type: 'index',
                        width: 60
                    },
                    {
                        title: '编号',
                        key: 'code',
                        width: 160,
                        render: function (h, params) {
                            return sf.getLinkRender(h, params.row['bizId'], params.row['code']);
                        },
                    },
                    {
                        title: '名称',
                        key: 'name',
                        width: 160,
                        render: function(h,params){
                            return h('Tooltip', {
                                props: { placement: 'top',transfer:true }
                            }, [
                                params.row.name,
                                h('span', { slot: 'content', style: { whiteSpace: 'normal', wordBreak: 'break-all' } },
                                    params.row.name)
                            ]);
                        }
                    },
                    {
                        title: '业务状态',
                        key: 'status',
                        width: 100,
                        render: function(h,params){
                            var status = params.row.status;
                            if(!!status && status.name){
                                return h('span',status.name);
                            }else{
                                return h('span',null);
                            }
                        }
                    },
                    {
                        title: '当前责任人',
                        key: 'resp',
                        width: 140,
                        render: function(h,params){
                            var resp = params.row.resp;
                            if(!!resp && resp.userName){
                                return h('span',resp.userName);
                            }else{
                                return h('span',null);
                            }
                        }
                    },
                    {
                        title: '计划完成时间',
                        key: 'planEndDate',
                        width: 120,
                        render: function(h,params){
                            if(!!params.row.planEndDate){
                                var planEndDate = new Date(params.row.planEndDate);
                                return h('span',planEndDate.format('yyyy-MM-dd'));
                            }else{
                                return h('span',null)
                            }
                        }
                    },
                ],
                detailsTableLoading: false,
            }
        },

        watch: {
            showDetailsModal: function(n,o){
                var sf = this;
                if(!n){
                    sf.detailsData = [];
                }
            }
        },

        created: function () {
            var sf = this;
            if(!Vue.evtHub){
                Vue.evtHub = new Vue();
            }
        },

        mounted: function () {
            var sf = this;
            sf.tableHeight = document.body.clientHeight - 234;
            sf.tableHeight = sf.tableHeight > 250 ? sf.tableHeight : 250;
        },

        methods: {

            //合并表格行
            handleTableSpan: function(row) {
                var sf = this;
                var rowIndex = row.rowIndex;
                var param = sf.tableDataCol.filter(function(item) {
                    return item.rowIndex === rowIndex
                });
                var rowSpan = param[0].rowSpan;
                var index = param[0].rowIndex;

                if(index === rowIndex) {
                    if(row.column.key == 'prdResearchCenter') {
                        if(rowSpan > 0) {
                            return [rowSpan, 1];
                        }else {
                            return  [0, 0];
                        }
                    }else {
                        return [1, 1];
                    }
                }
            },

            //翻页
            onPageNumChange: function(pageNum) {
                var sf = this;
                sf.pageNum = pageNum - 1;
                sf.queryTableData();
            },
            //选择每页条数
            onPageSizeChange: function(pageSize) {
                var sf = this;
                sf.pageNum = 0;
                sf.pageSize = pageSize;
                sf.queryTableData();
            },

            //选择统计时间
            dateChange: function() {
                var sf = this;
                sf.queryDataTotal = 0;
                sf.pageNum = 0;
                sf.queryTableData();
            },

            // 查询上线总结报告明细
            queryTableData: function () {
                var sf = this;
                if(!sf.date || sf.date.length == 0 || !sf.date[0] || !sf.date[1]) {
                    sf.$Message.info({
                        content: "请先选择统计时间！",
                        duration: 3,
                    });
                    return;
                }
                sf.loading = true;
                sf.queryData = [];
                sf.tableDataCol = [];
                $.ajax({
                    url: linkus.location.report + '/prd/prjDevMeasure.action',
                    type: 'post',
                    dataType: "json",
                    data: {
                        startDate: new Date(sf.date[0]).format("yyyy-MM-dd"),
                        endDate: new Date(sf.date[1]).format("yyyy-MM-dd"),
                    },
                    success: function(data) {
                        sf.loading = false;
                        if(!!data.success) {
                            sf.queryData = data.data || [];
                            if(!!sf.queryData && sf.queryData.length > 0) {
                                var prdResearchCenterList = [];
                                for(var i = 0; i < sf.queryData.length; i++) {
                                    var item = sf.queryData[i];
                                    var spanRow = {
                                        rowIndex: i, rowSpan: 0
                                    };
                                    if(!!prdResearchCenterList.includes(item.prdResearchCenter)){
                                        sf.tableDataCol.push(spanRow);
                                    }else{
                                        var length = sf.queryData.filter(function (filterItem){
                                            return item.prdResearchCenter === filterItem.prdResearchCenter;
                                        }).length;
                                        spanRow.rowSpan = length;
                                        sf.tableDataCol.push(spanRow);
                                        prdResearchCenterList.push(item.prdResearchCenter);
                                    }
                                }
                            }
                        }else {
                            sf.$Message.error({
                                content: data.message || '获取列表数据失败，请联系管理员！',
                                duration: 3
                            });
                        }
                    },
                    error: function(data) {
                        sf.loading = false;
                        if (!!data && !!data.responseText && !!JSON.parse(data.responseText)) {
                            sf.$Message.error({
                                content: JSON.parse(data.responseText).errorMessage || '获取列表数据失败，请联系管理员！',
                                duration: 3
                            });
                        } else {
                            sf.$Message.error({
                                content: '获取列表数据失败，请联系管理员！',
                                duration: 3
                            });
                        }
                    },
                });
            },
            // 导出
            exportData: function(){
                var sf = this;
                if(!sf.date || sf.date.length == 0 || !sf.date[0] || !sf.date[1]) {
                    sf.$Message.info({
                        content: "请先选择统计时间！",
                        duration: 3,
                    });
                    return;
                }
                sf.$Message.warning({
                    content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                    duration: 3
                });
                var url = linkus.location.report + "/prd/exportPrjDevMeasure.action";
                // 创建表单部分
                var form = document.createElement('form');
                form.style.display = 'none';
                form.action = url;
                form.method = 'get';
                form.enctype="application/json";
                document.body.appendChild(form);

                var date = document.createElement('input');
                date.type = 'hidden';
                date.name = 'startDate';
                date.value = new Date(sf.date[0]).format("yyyy-MM-dd");
                form.appendChild(date);

                var date = document.createElement('input');
                date.type = 'hidden';
                date.name = 'endDate';
                date.value = new Date(sf.date[1]).format("yyyy-MM-dd");
                form.appendChild(date);

                form.submit();
                form.remove();
            },

            getLinkRender: function (h, bizId, bizCode) {
                var url = linkus.location.prjbiz + "/forward.action?t=biz/prdBizView&bizId=" + bizId;
                return h('a', {
                    attrs: {
                        href: url,
                        target: '_blank',
                        rel: 'noopener'
                    }
                }, bizCode);
            },
            // 跳转帮助文档
            openHelpDoc: function(){
                var sf = this;
                window.open(linkus.location.prjbiz + '/forward.action?t=biz/bizHelpDoc&pageName='+sf.uploadDesc);
            },
            closeDetails: function(){
                var sf = this;
                sf.showDetailsModal = false;
                sf.detailsData = [];
            },
            queryDetails: function(colName,dmpVer,prdCtlgId){
                var sf = this;

                if(!sf.date || sf.date.length == 0 || !sf.date[0] || !sf.date[1]) {
                    sf.$Message.info({
                        content: "请先选择统计时间！",
                        duration: 3,
                    });
                    return;
                }
                sf.detailsTableLoading = true;

                var param = {
                    startDate: new Date(sf.date[0]).format("yyyy-MM-dd"),
                    endDate: new Date(sf.date[1]).format("yyyy-MM-dd"),
                    dmpVer: dmpVer,
                    prdCtlgId: prdCtlgId,
                    column: colName
                }
                $.ajax({
                    url: linkus.location.report + "/prd/prjDevMeasureDetail.action",
                    type: 'post',
                    data: param,
                    success: function(res){
                        sf.detailsTableLoading = false;
                        sf.detailsData = res.data || [];
                    },
                    error: function(res){
                        sf.detailsTableLoading = false;
                    }
                })
            }
        },

    });

</script>

</html>