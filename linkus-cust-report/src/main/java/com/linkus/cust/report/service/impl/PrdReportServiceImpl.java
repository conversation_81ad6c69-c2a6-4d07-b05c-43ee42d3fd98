package com.linkus.cust.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_LT;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeIdNameCnBt;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.TeTestBizCase;
import com.linkus.biz.db.model.dao.IBizDao;
import com.linkus.biz.db.model.dao.ITestBizCaseDao;
import com.linkus.cust.report.platformPrd.dao.IFlowTaskDao;
import com.linkus.cust.report.platformPrd.model.TeFlowTask;
import com.linkus.cust.report.platformPrd.model.vo.PrdPrjDevMeasureDetailVo;
import com.linkus.cust.report.platformPrd.model.vo.PrdPrjDevMeasureVo;
import com.linkus.cust.report.service.IPrdReportService;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysCal;
import com.linkus.sys.service.ISysDefService;
import com.linkus.utils.excel.ListToStringConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("PrdReportServiceImpl")
public class PrdReportServiceImpl implements IPrdReportService {
    @Autowired
    private IBizDao bizDao;
    @Autowired
    private IFlowTaskDao flowTaskDao;
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private ISysCalDao sysCalDao;
    @Autowired
    private ITestBizCaseDao testBizCaseDao;

    @Override
    public List<PrdPrjDevMeasureVo> prjDevMeasure(Date startDate, Date endDate) {
        List<PrdPrjDevMeasureVo> result = new ArrayList<>();
        List<SysDef> prdCtlg = sysDefService.getStdSysDefsByDefType(SysDefTypeCodeName.BIZ_PRD_CTLG);
        List<ObjectId> parentIds = prdCtlg.stream().map(SysDef::getParentDefId).collect(Collectors.toList());
        List<SysDef> parentDefs = sysDefService.getSysDefsByIds(parentIds);
        Map<ObjectId, String> prdResearchCenterMap = parentDefs.stream()
                .collect(Collectors.toMap(SysDef::getId, SysDef::getDefName));

        List<String> verCodes = new ArrayList<>();
        prdCtlg.stream().map(SysDef::getValue)
                .filter(StringUtil::isNotNull)
                .forEach(value-> {
                    JSONObject jsonObject = JSONObject.parseObject(value);

                    String dmpVer = jsonObject.getString("dmpVer");
                    String[] split = dmpVer.split(",");
                    for (String s : split) {
                        if(StringUtil.isNotNull(s)){
                            verCodes.add(s);
                        }
                    }
                });

        if (CollectionUtils.isNotEmpty(verCodes)){
            List<TeBiz> verBizs = bizDao.getBizsByCodes(verCodes);
            List<ObjectId> p0Ids = verBizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull)
                    .flatMap(List::stream).map(TeIdNameCnBt::getCid).collect(Collectors.toList());
            List<TeBiz> p0Bizs = bizDao.getBizsByIds(p0Ids);
            Map<ObjectId, List<TeBiz>> p0Map = p0Bizs.stream().filter(biz -> biz.getBizType().getCid()
                            .equals(SysDefConstants.DEF_ID_BIZ_TYPE_P0_FEATURE))
                    .collect(Collectors.groupingBy(biz -> biz.getPrdCtlg().getCid()));

            List<ObjectId> reqIds = p0Bizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                    .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_REQ))
                    .map(TeIdNameCnBt::getCid).collect(Collectors.toList());
            List<TeBiz> reqBizs = bizDao.getBizsByIds(reqIds);
            Map<ObjectId, List<TeBiz>> reqMap = reqBizs.stream().filter(biz -> biz.getBizType().getCid()
                            .equals(SysDefConstants.DEF_ID_BIZ_TYPE_REQ))
                    .collect(Collectors.groupingBy(biz -> biz.getPrdCtlg().getCid()));

            List<ObjectId> testCaseIds = reqBizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                    .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT))
                    .map(TeIdNameCnBt::getCid).collect(Collectors.toList());

            testCaseIds.addAll(reqIds);
            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DFN.common_isValid, true));
            conds.add(new DC_E(DFN.biz_bizType.dot(DFN.common_cid), SysDefConstants.DEF_ID_BIZ_TYPE_BUG));
            conds.add(new DC_I<>(DFN.biz_linkedBizs.dot(DFN.common_cid), testCaseIds));
            List<TeBiz> bugBizs = bizDao.findByConds(conds, null);

            Map<ObjectId, List<TeBiz>> bugMap = bugBizs.stream()
                    .collect(Collectors.groupingBy(biz -> biz.getPrdCtlg().getCid()));

            for (SysDef def : prdCtlg) {
                PrdPrjDevMeasureVo vo = new PrdPrjDevMeasureVo();
                result.add(vo);
                vo.setPrdResearchCenter(prdResearchCenterMap.get(def.getParentDefId()));
                vo.setPrdName(def.getDefName());
                if (StringUtil.isNotNull(def.getValue()) && CollectionUtils.isNotEmpty(def.getCndtItems())){
                    JSONObject jsonObject = JSONObject.parseObject(def.getValue());

                    String prdVer = jsonObject.getString("prdVer");
                    String dmpVer = jsonObject.getString("dmpVer");

                    vo.setDmpVer(dmpVer);
                    vo.setMidYearVersionNumber(prdVer);

                    ObjectId prdCtlgId = def.getCndtItems().get(0).getCid();
                    vo.setPrdCtlgId(prdCtlgId);
                    List<TeBiz> featureBizs = p0Map.get(prdCtlgId);
                    if (CollectionUtils.isNotEmpty(featureBizs)){
                        vo.setFeatureCount(featureBizs.size());
                        List<TeBiz> reqList = reqMap.get(prdCtlgId);
                        List<ObjectId> reqIdList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(reqList)){
                            reqIdList = reqList.stream().map(TeBiz::getId).collect(Collectors.toList());

                            vo.setTotalReqs(reqList.size());
                            long newCount = reqList.stream().filter(biz -> biz.getAddTime().before(endDate)
                                    && biz.getAddTime().after(startDate)).count();
                            vo.setNewReqs((int) newCount);

                            long closeCount = reqList.stream().filter(biz -> null != biz.getActualCloseTime()
                                    && biz.getActualCloseTime().before(endDate)).count();
                            vo.setClosedReqs((int) closeCount);

                            List<ObjectId> testCaseList = reqList.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                                    .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT)
                                    && biz.getAddTime().before(endDate)).map(TeIdNameCnBt::getCid).distinct().collect(Collectors.toList());
                            List<TeBiz> testCases = bizDao.getBizsByIds(testCaseList);
                            vo.setTestCases(testCases.size());

                            if (CollectionUtils.isNotEmpty(testCaseList)){
                                conds.clear();
                                conds.add(new DC_E(DFN.common_isValid, true));
                                conds.add(new DC_I<>(DFN.testBizCase_case.dot(DFN.common_cid), testCaseList));
                                conds.add(new DC_LT(DFN.testBizCase_runTime, endDate,true));
                                List<TeTestBizCase> testBizs = testBizCaseDao.findByConds(conds, null);
                                if (CollectionUtils.isNotEmpty(testBizs)){
                                    List<ObjectId> testIds = testBizs.stream().map(TeTestBizCase::getTestCase).map(TeIdNameCn::getCid).collect(Collectors.toList());
                                    List<TeBiz> passCases = bizDao.getBizsByIds(testIds);
                                    vo.setPassedTestCases(passCases.size());
                                    long linkedCount = reqList.stream().filter(req -> req.getLinkedBizs().stream()
                                            .map(TeIdNameCnBt::getBizTypeId).anyMatch(id ->
                                                    id.equals(SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT))).count();
                                    vo.setTestCaseCoverage(BigDecimalUtils.divideDouble((double) linkedCount, (double) reqList.size(),2));
                                }
                            }

                            List<TeBiz> bugBizList = bugMap.get(prdCtlgId);
                            if (CollectionUtils.isNotEmpty(bugBizList)){
                                vo.setCumulativeBugs(bugBizList.size());

                                long newBugCount = bugBizList.stream().filter(biz -> null != biz.getAddTime()
                                        && biz.getAddTime().before(endDate) && biz.getAddTime().after(startDate) ).count();
                                vo.setNewBugs((int) newBugCount);

                                long closeBugCount = bugBizList.stream().filter(biz -> null != biz.getStatus()
                                        && biz.getStatus().getCid().equals(SysDefConstants.DEF_ID_BIZ_STATUS_CLOSED)).count();
                                vo.setClosedBugs((int) closeBugCount);

                                vo.setBugClosureRate(BigDecimalUtils.divideDouble((double) closeBugCount,bugBizList.size(),2));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(reqIdList)){
                            List<TeFlowTask> devNodes = getNodes(reqIdList,SysDefConstants.DEF_ID_NODE_DEV);
                            Map<ObjectId, TeFlowTask> devMap = devNodes.stream().collect(Collectors.toMap(TeFlowTask::getId, Function.identity()));
                            vo.setDispatchedTaskCount(devNodes.size());
                            if (CollectionUtils.isNotEmpty(devNodes)){
                                Map<ObjectId, List<TeFlowTask>> taskMap = devNodes.stream()
                                        .collect(Collectors.groupingBy(teFlowTask -> teFlowTask.getBiz().getCid()));

                                for (ObjectId bizId : taskMap.keySet()) {
                                    List<TeFlowTask> teFlowTasks = taskMap.get(bizId);
                                    // 获取 actualEndTime 最大的 TeFlowTask 对象
                                    Optional<Date> max = teFlowTasks.stream().map(TeFlowTask::getActualEndTime)
                                            .filter(Objects::nonNull).max(Comparator.comparing(Function.identity()));
                                    if (max.isPresent()){
                                        Date actualEnterTime = teFlowTasks.get(0).getActualEnterTime();
                                        Date actualEndTime = max.get();
                                        List<TeSysCal> workDays = sysCalDao.getWorkDays(DateUtil.formatDate2Str(DateUtil.toNextDay(actualEnterTime, 1)
                                                , DateUtil.DATE_FORMAT), DateUtil.formatDate2Str(actualEndTime, DateUtil.DATE_FORMAT));
                                        vo.setAvgDevelopmentDays(BigDecimalUtils.divideDouble((double) workDays.size(),teFlowTasks.size(),2));
                                    }
                                }

                            }

                            List<TeFlowTask> testNodes = getNodes(reqIdList,SysDefConstants.DEF_ID_NODE_TEST);
                            if (CollectionUtils.isNotEmpty(testNodes)){
                                Map<ObjectId, List<TeFlowTask>> taskMap = testNodes.stream()
                                        .collect(Collectors.groupingBy(teFlowTask -> teFlowTask.getBiz().getCid()));

                                for (ObjectId bizId : taskMap.keySet()) {
                                    List<TeFlowTask> teFlowTasks = taskMap.get(bizId);
                                    // 获取 actualEndTime 最大的 TeFlowTask 对象
                                    Optional<Date> max = teFlowTasks.stream().map(TeFlowTask::getActualEndTime)
                                            .filter(Objects::nonNull).max(Comparator.comparing(Function.identity()));
                                    if (max.isPresent()){
                                        Date actualEnterTime = teFlowTasks.get(0).getActualEnterTime();
                                        Date actualEndTime = max.get();
                                        List<TeSysCal> workDays = sysCalDao.getWorkDays(DateUtil.formatDate2Str(DateUtil.toNextDay(actualEnterTime, 1)
                                                , DateUtil.DATE_FORMAT), DateUtil.formatDate2Str(actualEndTime, DateUtil.DATE_FORMAT));
                                        vo.setAvgTestingDays(BigDecimalUtils.divideDouble((double) workDays.size(),teFlowTasks.size(),2));

                                    }
                                }
                            }
                            int count = 0;
                            for (TeFlowTask testNode : testNodes) {
                                ObjectId devId = testNode.getPreTasks().get(0).getCid();
                                Date testStartTime = testNode.getActualEnterTime();
                                Date testEndTime = testNode.getActualEndTime();
                                TeFlowTask devNode = devMap.get(devId);
                                if (null == devNode || null == testStartTime || null == testEndTime){
                                    continue;
                                }
                                Date devEnterTime = devNode.getActualEnterTime();
                                Date devEndTime = devNode.getActualEndTime();
                                int i = DateUtil.differentDaysByMillisecond(testStartTime, testEndTime)+1;
                                int j = DateUtil.differentDaysByMillisecond(devEnterTime, devEndTime)+1;
                                if (i+j <= 14){
                                    count++;
                                }
                            }
                            vo.setTasksCompletedIn14Days(count);
                            if (0 != vo.getAvgDevelopmentDays() && 0 != vo.getAvgTestingDays()){
                                vo.setAvgCompletionDays(vo.getAvgDevelopmentDays()+vo.getAvgTestingDays());
                            }
                        }
                    }
                }
            }

        }
        return result;
    }

    @Override
    public List<PrdPrjDevMeasureDetailVo> prjDevMeasureDetail(Date startDate, Date endDate, String dmpVer
            , ObjectId prdCtlgId, String column) {
        List<PrdPrjDevMeasureDetailVo> result = new ArrayList<>();
        List<String> verCodes = new ArrayList<>();
        String[] split = dmpVer.split(",");
        for (String s : split) {
            if(StringUtil.isNotNull(s)){
                verCodes.add(s);
            }
        }
        List<TeBiz> verBizs = bizDao.getBizsByCodes(verCodes);
        List<ObjectId> p0Ids = verBizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull)
                .flatMap(List::stream).map(TeIdNameCnBt::getCid).collect(Collectors.toList());
        List<TeBiz> p0Bizs = bizDao.getBizsByIds(p0Ids);

        List<ObjectId> reqIds = p0Bizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_REQ))
                .map(TeIdNameCnBt::getCid).collect(Collectors.toList());
        List<TeBiz> reqBizs = bizDao.getBizsByIds(reqIds);
        reqBizs = reqBizs.stream().filter(biz -> biz.getPrdCtlg().getCid().equals(prdCtlgId)).collect(Collectors.toList());

        List<ObjectId> testCaseList = reqBizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT)
                        && biz.getAddTime().before(endDate)).map(TeIdNameCnBt::getCid).distinct().collect(Collectors.toList());

        List<ObjectId> testCaseIds = reqBizs.stream().map(TeBiz::getLinkedBizs).filter(Objects::nonNull).flatMap(List::stream)
                .filter(biz -> biz.getBizTypeId().equals(SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT))
                .map(TeIdNameCnBt::getCid).collect(Collectors.toList());

        testCaseIds.addAll(reqIds);
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.biz_bizType.dot(DFN.common_cid), SysDefConstants.DEF_ID_BIZ_TYPE_BUG));
        conds.add(new DC_I<>(DFN.biz_linkedBizs.dot(DFN.common_cid), testCaseIds));
        List<TeBiz> bugBizs = bizDao.findByConds(conds, null);

        if (column.equals("featureCount")){
            if (CollectionUtils.isNotEmpty(p0Bizs)){
                setVo(p0Ids,p0Bizs, result);
            }
        } else if (column.equals("totalReqs")){
            if (CollectionUtils.isNotEmpty(reqBizs)){
                setVo(reqIds,reqBizs, result);
            }
        } else if (column.equals("newReqs")){
            if (CollectionUtils.isNotEmpty(reqBizs)){
                List<TeBiz> newReqs = reqBizs.stream().filter(biz -> biz.getAddTime().before(endDate)
                        && biz.getAddTime().after(startDate)).collect(Collectors.toList());
                setVo(reqIds,newReqs, result);
            }
        } else if (column.equals("closedReqs")){
            if (CollectionUtils.isNotEmpty(reqBizs)){
                List<TeBiz> closeReqs = reqBizs.stream().filter(biz -> null != biz.getActualCloseTime()
                        && biz.getActualCloseTime().before(endDate)).collect(Collectors.toList());
                setVo(reqIds,closeReqs, result);
            }
        } else if (column.equals("dispatchedTaskCount")){
            if (CollectionUtils.isNotEmpty(reqBizs)){
                List<ObjectId> reqIdList = reqBizs.stream().map(TeBiz::getId).collect(Collectors.toList());
                List<TeFlowTask> devNodes = getNodes(reqIdList,SysDefConstants.DEF_ID_NODE_DEV);
                setTaskVo(devNodes,reqBizs,result);
            }
        } else if (column.equals("testCases")){
            if (CollectionUtils.isNotEmpty(testCaseList)){
                List<TeBiz> testCases = bizDao.getBizsByIds(testCaseList);
                setVo(testCaseList,testCases, result);
            }
        } else if (column.equals("passedTestCases")){
            if (CollectionUtils.isNotEmpty(testCaseList)){
                conds.clear();
                conds.add(new DC_E(DFN.common_isValid, true));
                conds.add(new DC_I<>(DFN.testBizCase_case.dot(DFN.common_cid), testCaseList));
                conds.add(new DC_LT(DFN.testBizCase_runTime, endDate,true));
                conds.add(new DC_E(DFN.testBizCase_result,"succeeded"));
                List<TeTestBizCase> testBizs = testBizCaseDao.findByConds(conds, null);
                if (CollectionUtils.isNotEmpty(testBizs)){
                    List<ObjectId> testIds = testBizs.stream().map(TeTestBizCase::getTestCase).map(TeIdNameCn::getCid).collect(Collectors.toList());
                    List<TeBiz> testCases = bizDao.getBizsByIds(testIds);
                    setVo(testIds,testCases, result);
                }
            }
        } else if (column.equals("newBugs")){
            if (CollectionUtils.isNotEmpty(bugBizs)){
                List<TeBiz> bug = bugBizs.stream().filter(biz -> null != biz.getAddTime()
                        && biz.getAddTime().before(endDate) && biz.getAddTime().after(startDate)).collect(Collectors.toList());
                List<ObjectId> ids = bug.stream().map(TeBiz::getId).collect(Collectors.toList());
                setVo(ids,bug, result);
            }
        } else if (column.equals("cumulativeBugs")){
            if (CollectionUtils.isNotEmpty(bugBizs)){
                List<ObjectId> ids = bugBizs.stream().map(TeBiz::getId).collect(Collectors.toList());
                setVo(ids,bugBizs, result);
            }
        } else if (column.equals("closedBugs")){
            List<TeBiz> bizs = bugBizs.stream().filter(biz -> null != biz.getStatus()
                    && biz.getStatus().getCid().equals(SysDefConstants.DEF_ID_BIZ_STATUS_CLOSED)).collect(Collectors.toList());
            List<ObjectId> ids = bugBizs.stream().map(TeBiz::getId).collect(Collectors.toList());
            setVo(ids,bizs, result);
        } else if (column.equals("tasksCompletedIn14Days")){
            List<ObjectId> bizIds = new ArrayList<>();
            List<TeFlowTask> devNodes = getNodes(reqIds,SysDefConstants.DEF_ID_NODE_DEV);
            Map<ObjectId, TeFlowTask> devMap = devNodes.stream().collect(Collectors.toMap(TeFlowTask::getId, Function.identity()));
            if (CollectionUtils.isNotEmpty(devNodes)){
                Map<ObjectId, List<TeFlowTask>> taskMap = devNodes.stream()
                        .collect(Collectors.groupingBy(teFlowTask -> teFlowTask.getBiz().getCid()));
            }

            List<TeFlowTask> testNodes = getNodes(reqIds,SysDefConstants.DEF_ID_NODE_TEST);
            for (TeFlowTask testNode : testNodes) {
                ObjectId devId = testNode.getPreTasks().get(0).getCid();
                Date testStartTime = testNode.getActualEnterTime();
                Date testEndTime = testNode.getActualEndTime();
                TeFlowTask devNode = devMap.get(devId);
                if (null == devNode || null == testStartTime || null == testEndTime){
                    continue;
                }
                Date devEnterTime = devNode.getActualEnterTime();
                Date devEndTime = devNode.getActualEndTime();
                int i = DateUtil.differentDaysByMillisecond(testStartTime, testEndTime)+1;
                int j = DateUtil.differentDaysByMillisecond(devEnterTime, devEndTime)+1;
                if (i+j <= 14){
                    bizIds.add(testNode.getBiz().getCid());
                }
            }
            if (CollectionUtils.isNotEmpty(bizIds)){
                List<TeBiz> bizs = bizDao.getBizsByIds(bizIds);
                setVo(bizIds,bizs, result);
            }
        }
        return result;
    }

    private void setVo(List<ObjectId> bizIds,List<TeBiz> bizs, List<PrdPrjDevMeasureDetailVo> result) {
        List<TeFlowTask> tasks = getTasks(bizIds);
        Map<ObjectId, Date> dateMap = tasks.stream().collect(Collectors.toMap(task -> task.getBiz().getCid()
                , TeFlowTask::getPlanEndDate, (oldValue, newValue) -> newValue));
        for (TeBiz biz : bizs) {
            PrdPrjDevMeasureDetailVo vo = new PrdPrjDevMeasureDetailVo();
            vo.setBizId(biz.getId());
            vo.setName(biz.getName());
            vo.setCode(biz.getCode());
            TeIdNameCn status = biz.getStatus();
            if (status != null) {
                vo.setStatus(status);
            }
            List<TeUser> curResps = biz.getCurResps();
            if (CollectionUtils.isNotEmpty(curResps)) {
                TeUser teUser = curResps.get(0);
                vo.setResp(teUser);
            }
            Date date = dateMap.get(biz.getId());
            if (date != null) {
                vo.setPlanEndDate(date);
            }
            result.add(vo);
        }
    }

    private void setTaskVo(List<TeFlowTask> tasks,List<TeBiz> bizs, List<PrdPrjDevMeasureDetailVo> result) {
        Map<ObjectId, TeBiz> bizMap = bizs.stream().collect(Collectors.toMap(TeBiz::getFlowId, Function.identity()));
        for (TeFlowTask task : tasks) {
            TeBiz biz = bizMap.get(task.getFlowId());
            if (biz != null) {
                PrdPrjDevMeasureDetailVo vo = new PrdPrjDevMeasureDetailVo();
                vo.setBizId(biz.getId());
                vo.setName(biz.getName());
                vo.setCode(biz.getCode());
                TeIdNameCn status = biz.getStatus();
                if (status != null) {
                    vo.setStatus(status);
                }
                List<TeUser> curResps = biz.getCurResps();
                if (CollectionUtils.isNotEmpty(curResps)) {
                    TeUser teUser = curResps.get(0);
                    vo.setResp(teUser);
                }
                Date date = task.getPlanEndDate();
                if (date != null) {
                    vo.setPlanEndDate(date);
                }
                result.add(vo);
            }
        }

    }

    @Override
    public void exportPrjDevMeasure(Date startDate, Date endDate, HttpServletResponse response)throws IOException {
        List<PrdPrjDevMeasureVo> list = prjDevMeasure(startDate, endDate);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("PRD研发项目度量报表.xlsx", "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0)
                .registerConverter(new ListToStringConverter())
                .head(PrdPrjDevMeasureVo.class)
                .build();
        excelWriter.write(list, sheet);
        excelWriter.finish();
    }

    private List<TeFlowTask> getNodes(List<ObjectId> reqIds,ObjectId nodeId) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.flowTask_biz.dot(DFN.common_cid), reqIds));
        conds.add(new DC_E(DFN.flowTask_node.dot(DFN.common_cid),nodeId));
        List<TeFlowTask> teFlowTasks = flowTaskDao.findByConds(conds, null);
        return teFlowTasks;
    }

    private List<TeFlowTask> getTasks(List<ObjectId> bizIds) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.flowTask_biz.dot(DFN.common_cid), bizIds));
        conds.add(new DC_E(DFN.flowTask_isActiveTask,true));
        List<TeFlowTask> teFlowTasks = flowTaskDao.findByConds(conds, null);
        return teFlowTasks;
    }
}
