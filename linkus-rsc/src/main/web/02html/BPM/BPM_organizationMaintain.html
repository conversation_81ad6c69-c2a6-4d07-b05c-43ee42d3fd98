<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>组织维护</title>
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <!-- 本地样式 -->
    <link href="../../01css/reset.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css-BPM/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/prjStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>

    <!-- jQuery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

    <!-- VUE-->
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <!-- iview -->
    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/4.4.0/styles/iview.css"/>

    <!-- iconfont字体图标 -->
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <!-- 本页面使用 -->
    <script src="header.js" type="text/javascript"></script>
    <style>
        [v-cloak] {
            display: none;
        }
        .org_wrap{
            width: 100%;
            height: calc(100% - 60px);
            min-height: 400px;
            font-size: 12px;
            padding: 0 16px;
            position: relative;
        }
        .org_wrap .top_wrap{
            width: 100%;
            height: 52px;
        }
        .org_wrap .top_wrap .btn_wrap{
            width: 100%;
            height: 52px;
            padding: 10px 0 0;
            display: flex;
            flex-direction: row-reverse;
        }
        .org_wrap .top_wrap .btn_wrap .ivu-btn{
            font-size: 12px;
            margin-left: 6px;
        }
        .org_wrap .bottom_wrap{
            width: 100%;
            height: calc(100% - 64px);
            background: #fff;
            border-radius: 4px;
            position: relative;
            display: flex;

            border: 1px solid #dbe3eb;
            box-shadow: 0 4px 18px 0 rgba(198, 213, 229, .4);
        }
        .org_wrap .bottom_wrap .tree_wrap{
            width: 300px;
            height: 100%;
            border-right: 1px solid #dbe3eb;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            background: #fff;
            padding: 6px 8px;
            position: relative;
            overflow: hidden;
        }
        .tree_wrap .ivu-tree{
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }
        .tree_wrap .ivu-tree ul li{
            margin: 0 0 0 6px;
        }
        .tree_wrap .ivu-tree .ivu-tree-title {
            min-width: calc(100% - 16px);
            padding: 0;
        }
        .tree_wrap .btn-node{
            display: none;
        }
        .tree_wrap .ivu-tree-title:hover > div > .btn-node{
            display: inline-block;
        }
        .tree_wrap .ivu-tree .ivu-tree-title .tree-div-wrap{
            width: 100%;
            height: 32px;

            display: flex;
            align-items: center;
            padding: 0 4px;
        }
        .tree-div-wrap .title-wrap{
            font-size: 12px;
        }
        .tree-div-wrap .btn-node{
            width: 72px;
        }
        .tree-div-wrap .btn-node .iconfont{
            width: 14px;
            height: 14px;
            font-size: 14px;
        }
        .tree_wrap .ivu-tree .ivu-tree-children{
            position: relative;
        }
        .tree_wrap .ivu-tree .ivu-tree-children:before {
            content: "";
            height: 100%;
            width: 1px;
            position: absolute;
            left: 6px;
            top: 2px;
            border-width: 1px;
            border-left: 1px dashed #a5acc4;
        }
        .tree_wrap .ivu-tree>.ivu-tree-children:last-of-type:before {
            border-left: none;
        }
        .tree_wrap .ivu-tree .ivu-tree-children:last-child:before{
            height: 12px;
        }
        .tree_wrap .ivu-tree .ivu-tree-children:after {
            content: "";
            width: 12px;
            height: 20px;
            position: absolute;
            left: 8px;
            top: 15px;
            border-width: 1px;
            border-top: 1px dashed #a5acc4;
        }
        .tree_wrap .ivu-tree>.ivu-tree-children:after{
            border-top: none;
        }
        .tree_wrap .ivu-tree .ivu-tree-arrow{
            width: auto;
            margin-top: 4px;
        }
        .org_wrap .bottom_wrap .bottom_ct_gap{
            width: 4px;
            height: 100%;
            border-right: 1px solid #dbe3eb;
        }
        .org_wrap .bottom_wrap .table_wrap{
            width: calc(100% - 304px);
            height: 100%;
            padding: 12px;
        }
        .org_wrap .bottom_wrap .table_wrap .page_wrap{
            display: flex;
            justify-content: flex-end;
            margin-bottom: 12px;
        }
        .form_std{
            width: 100%;
            height: 100%;
        }
        .form_std .ivu-form-item{
            width: 100%;
            margin-bottom: 12px;
        }
        .form_std .ivu-form-item .ivu-input-number{
            width: 100%;
        }
        .dept_tree .ivu-poptip{
            width: 100%;
        }
        .dept_tree .ivu-poptip .ivu-poptip-rel{
            width: 100%;
        }
        .ivu-page-item-active a{
            color: #fff !important;
        }
        .record_wrap{
            width: 100%;
            height: 100%;
        }
        .record_header{
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        .record_header .search_wrap{
            width: 33.3%;
            display: flex;
            align-items: center;
        }
        .record_header .search_wrap label{
            font-size: 12px;
            margin-right: 8px;
            white-space: nowrap;
        }
        .record_table{
            margin-bottom: 16px;
        }
        .left_tree .ivu-icon-ios-arrow-forward:before {
            content: "";
            font-family: iconfont;
            color: #a5acc4;
        }
        .left_tree .ivu-tree-arrow-open .ivu-icon-ios-arrow-forward:before {
            content: "";
            font-family: iconfont;
            color: #a5acc4;
        }
        .left_tree .ivu-icon {
            -webkit-transform: rotate(0deg);
            transform: rotate(0);
        }
        .left_tree >ul li span i{
            margin-bottom: 2px;
        }
        .left_tree .ivu-tree-title:hover .tree-div-wrap .title-wrap{
            color: #3883e5;
        }
        .left_tree .ivu-tree-title:hover .tree-div-wrap .btn-node{
            color: #3883e5;
        }
        .ivu-select-input::placeholder{
            font-size: 12px;
        }
        .ivu-input::placeholder{
            font-size: 12px;
        }
        .tree_select_box{
            max-height: 284px;
            padding: 4px 0;
            overflow-y: auto;
        }
        .ivu-select-single .ivu-select-selection .ivu-select-placeholder, .ivu-select-single .ivu-select-selection .ivu-select-selected-value{
            font-size: 12px;
        }
        .ivu-input, .ivu-select-input {
            font-size: 12px;
        }
        textarea.ivu-input {
            font-size: 12px;
        }
        .ivu-page-item-active {
            background-color: #f90 !important;
            border-color: #f90 !important;
        }
        .ivu-table{
            border: 1px solid #dbe3eb !important;
        }
        .ivu-table td, .ivu-table th {
            height: 32px;
            font-size: 12px;
        }
        .ivu-table .ivu-table-header{
            border-bottom: 1px solid #dbe3eb !important;
        }
        .ivu-table-header thead tr th{
            border-right: 1px solid #dbe3eb !important;
            background-color: #f1f5f9;
        }
        .ivu-table-stripe .ivu-table-body tr.ivu-table-row-hover td, .ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td{
            background: transparent;
        }
        .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td, .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td{
            background-color: #f5f8fb;
        }
        .ivu-table-body td, .ivu-table-body th {
            border: none;
        }
        .ivu-modal .ivu-modal-header-inner{
            font-size: 14px;
        }
        .ivu-form .ivu-form-item .ivu-form-item-label{
            font-size: 12px;
        }
        .ivu-btn{
            font-size: 12px;
        }
        .ivu-select-dropdown{
            font-size: 12px;
        }
        .dept_poptip .ivu-poptip-arrow{
            display: none;
        }
        .dept_poptip .ivu-tree .ivu-tree-arrow i {
            font-size: 12px;
        }
        .dept_poptip .ivu-tree .ivu-tree-title{
            font-size: 12px;
            margin-top: 3px;
        }
        .ivu-btn-text:hover {
            color: #57a3f3 !important;
        }
        .ivu-modal-confirm-head-title {
            font-size: 14px;
        }
        .ivu-modal-confirm-body{
            font-size: 12px;
        }
        .ivu-page-total, .ivu-page-item{
            font-size: 12px;
        }
        body .ivu-input-number:hover{
            border-color: #57a3f3 !important;
        }
        .ivu-cascader .ivu-cascader-menu-item {
            font-size: 12px !important;
        }
    </style>
</head>
<body style="height: 100%;width: 100%;background-color: #f0f0f0!important;">
    <div id="main">
        <div class="bpm-main-nav" style="height: 100%">
            <bpm-header ref="bpmHeader"></bpm-header>

            <div class="org_wrap">
                <div class="top_wrap">
                    <div class="btn_wrap">
                        <i-button type="warning" @click="showMaintainRecord">维护记录</i-button>
                        <i-button type="warning" @click="exportTableData">导出</i-button>
                    </div>
                </div>
                <div class="bottom_wrap">
                    <div class="tree_wrap">
                        <Tree :data="treeData" :render="renderContent" class="left_tree"></Tree>
                    </div>
                    <div class="bottom_ct_gap"></div>
                    <div class="table_wrap" ref="tableWrap">
                        <i-table class="right_table" :columns="columns" :data="tableData" :loading="tableLoading" :height="tableHeight" stripe></i-table>
                    </div>
                </div>
            </div>
        </div>

        <iframe width="0" height="0" style="display:none;" id="exportExcelFrame"></iframe>
        <iframe width="0" height="0" style="display:none;" id="exportRecordExcelFrame"></iframe>

        <Modal v-model="addOrgShow" title="新增组织" class-name="vertical-center-modal">
            <i-form v-model="newOrg" class="form_std" label-position="left" :label-width="125">
                <form-item label="新增部门上级部门">
                    <span v-show="!isAddRoot" style="font-size: 12px;">{{newOrg.upDeptName}}</span>
                    <div v-show="isAddRoot">
                        <i-select v-model="newOrg.upDeptId" placeholder="请选择新增部门上级部门">
                            <i-option v-for="item in rootDeptUpList" :key="item.id" :value="item.id">{{ item.name }}</i-option>
                        </i-select>
                    </div>
                </form-item>
                <form-item label="新增部门名称">
                    <i-input v-model="newOrg.newDeptName" type="text" placeholder="请填写新增部门名称"></i-input>
                </form-item>
                <form-item label="新增部门负责人">
                    <i-select v-model="newOrg.respId" placeholder="请选择新增部门负责人" filterable clearable :remote-method="queryRespUser" :loading="newOrgRespLoading">
                        <i-option v-for="author in newOrgRespList" :value="author.id" :key="author.id">{{ author.userName + '/' + author.loginName}}</i-option>
                    </i-select>
                </form-item>
                <form-item label="调整说明">
                    <i-input v-model="newOrg.desc" type="textarea" :rows="3" placeholder="请填写调整说明"></i-input>
                </form-item>
                <form-item label="编号">
                    <Input-Number v-model="newOrg.defNo" placeholder="请输入编号"></Input-Number>
                </form-item>
            </i-form>
            <div slot="footer">
                <i-button type="primary" @click="submitAdd">提交</i-button>
            </div>
        </Modal>

        <Modal v-model="editOrgShow" title="修改组织" class-name="vertical-center-modal">
            <i-form v-model="editOrg" class="form_std" label-position="left" :label-width="125">
                <form-item label="原部门上级部门">
                    <span style="font-size: 12px;">{{editOrg.oddUpDeptName}}</span>
                </form-item>
                <form-item label="原部门名称">
                    <span style="font-size: 12px;">{{editOrg.oddDeptName}}</span>
                </form-item>
                <form-item label="原部门负责人">
                    <span style="font-size: 12px;">{{editOrg.oddRespName}}</span>
                </form-item>
                <form-item label="修改后上级部门">
                    <Cascader :data="editTreeData" v-model="editOrg.newUpDeptIds" placeholder="请选择修改后上级部门" @on-change="editDeptSelect" change-on-select></Cascader>
                </form-item>
                <form-item label="修改后部门名称">
                    <i-input v-model="editOrg.newDeptName" type="text" placeholder="请填写修改后部门名称"></i-input>
                </form-item>
                <form-item label="修改后部门负责人">
                    <i-select v-model="editOrg.newResp" placeholder="请选择修改后部门负责人" filterable clearable :remote-method="queryEditRespUser" :loading="editOrgRespLoading">
                        <i-option v-for="author in editOrgRespList" :value="author.id" :key="author.id">{{ author.userName + '/' + author.loginName}}</i-option>
                    </i-select>
                </form-item>
                <form-item label="调整说明">
                    <i-input v-model="editOrg.desc" type="textarea" :rows="3" placeholder="请填写调整说明"></i-input>
                </form-item>
                <form-item label="编号">
                    <Input-Number v-model="editOrg.defNo" placeholder="请输入编号"></Input-Number>
                </form-item>
            </i-form>
            <div slot="footer">
                <i-button type="primary" @click="submitEdit">提交</i-button>
            </div>
        </Modal>

        <Modal v-model="maintainRecordShow" title="维护记录" width="800" class-name="vertical-center-modal" footer-hide>
            <div class="record_wrap">
                <div class="record_header">
                    <div class="search_wrap">
                        <label>时间</label>
                        <Date-Picker type="daterange" v-model="recordSearch.dateRange" placeholder="请选择时间" style="width: 200px; margin-right: 10px;"></Date-Picker>
                    </div>
                    <div class="search_wrap">
                        <label>关键字</label>
                        <i-input v-model="recordSearch.keyword" placeholder="请输入关键字" style="width: 200px; margin-right: 10px;" clearable></i-input>
                    </div>
                    <div class="search_wrap btn_wrap">
                        <i-button type="warning" style="margin-left: auto;" @click="getRecordData">查询</i-button>
                        <i-button type="warning" style="margin-left: 8px;" @click="exportRecordData">导出</i-button>
                    </div>
                </div>
                <div class="record_table">
                    <i-table :columns="recordColumns" :data="recordData" :loading="recordLoading" :height="300" stripe></i-table>
                    <div style="margin-top: 16px;">
                        <Page :total="recodeDataTotal" size="small" :current="recordPageNum+1" :page-size="recodePageSize" :page-size-opts="recodePageSizeOpts"
                            @on-change="onRecordPageNumChange" @on-page-size-change="onRecordPageSizeChange" show-sizer show-total></Page>
                    </div>
                </div>
            </div>
        </Modal>
    </div>
</body>

<script>
    Date.prototype.format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1,                 //月份
            "d+": this.getDate(),                    //日
            "h+": this.getHours(),                   //小时
            "m+": this.getMinutes(),                 //分
            "s+": this.getSeconds(),                 //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds()             //毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };

    //控制登录失效后HTML页面跳转登录页
    verifyLogin();

    var vue = new Vue({
        el: '#main',
        data: function () {
            var sf = this;
            return {
                deptList: [],
                rootDeptUpList: [
                    {
                        id: '5c2dc549900add501a14143c',
                        name: '后台'
                    },
                    {
                        id: '5c2dc596900add501a14143d',
                        name: '中台'
                    },
                    {
                        id: '5c2dc5ab900add501a14143e',
                        name: '虚拟组织'
                    },
                ],
                treeData: [],

                seletNode: {},
                columns: [
                    {
                        title: '序',
                        key: 'no',
                        width: 80,
                    },
                    {
                        title: '事业部/中心',
                        key: 'division',
                    },
                    {
                        title: '虚拟中心',
                        key: 'virtualCenter',
                    },
                    {
                        title: '管理部门',
                        key: 'dept',
                    },
                    {
                        title: '负责人',
                        key: 'resp',
                    },
                ],
                tableData: [],
                tableLoading: false,
                tableHeight: 300,

                // 新增
                addOrgShow: false,
                newOrg: {
                    upDeptName: '',
                    upDeptId: '',
                    respId: '',
                    newDeptName: '',
                    desc: '',
                    defNo: 0,
                },
                isAddRoot: false,
                newOrgRespLoading: false,
                newOrgRespList: [],

                // 编辑
                editOrgShow: false,
                editTreeData: [],
                editOrg: {
                    oddUpDeptName: '',
                    oddDeptName: '',
                    deptId: '',
                    oddRespName: '',
                    oddRespId: '',
                    newUpDeptName: '',
                    newUpDeptId: '',
                    newUpDeptIds: [],
                    newDeptName: '',
                    newResp: '',
                    desc: '',
                    defNo: 0,
                },
                editOrgRespLoading: false,
                editOrgRespList: [],

                // 维护记录
                maintainRecordShow: false,
                recordSearch: {
                    dateRange: [],
                    keyword: '',
                },
                recordColumns: [
                    {
                        title: '序',
                        type: 'index',
                        width: 60,
                    },
                    {
                        title: '操作类型',
                        key: 'optionType',
                        width: 100,
                    },
                    {
                        title: '操作人',
                        key: 'optionUser',
                        width: 150,
                    },
                    {
                        title: '操作时间',
                        key: 'optionTime',
                        width: 160,
                        render: function(h,params){
                            if(!!params.row.optionTime){
                                var optionTime = new Date(params.row.optionTime);
                                return h('span',optionTime.format('yyyy-MM-dd hh:mm:ss'));
                            }else{
                                return h('span',null);
                            }
                        }
                    },
                    {
                        title: '系统更新说明',
                        key: 'updateDesc',
                        width: 180,
                    },
                    {
                        title: '调整说明',
                        key: 'desc',
                    }
                ],
                recordData: [],
                recordLoading: false,
                recodeDataTotal: 0,
                recordPageNum: 0,
                recodePageSize: 20,
                recodePageSizeOpts: [20,50,100],
            }
        },
        watch: {
            addOrgShow: function(n,o){
                var sf = this;
                if(!n){
                    sf.newOrg = {
                        upDeptName: '',
                        upDeptId: '',
                        respId: '',
                        newDeptName: '',
                        desc: '',
                        defNo: 0
                    }
                    sf.isAddRoot = false;
                }
            },
            editOrgShow: function(n,o){
                var sf = this;
                if(!n){
                    sf.editOrg = {
                        oddUpDeptName: '',
                        oddDeptName: '',
                        deptId: '',
                        oddRespName: '',
                        oddRespId: '',
                        newUpDeptName: '',
                        newUpDeptId: '',
                        newUpDeptIds: [],
                        newDeptName: '',
                        newResp: '',
                        desc: '',
                        defNo: 0
                    };
                    sf.editOrgRespList = [];
                }
            },
            maintainRecordShow: function(n,o){
                var sf = this;
                if(!n){
                    sf.recordSearch = {
                        dateRange: [],
                        keyword: '',
                    };
                    sf.recordData = [];
                    sf.recodeDataTotal = 0;
                    sf.recordPageNum = 0;
                    sf.recodePageSize = 20;
                }
            }
        },
        created: function () {
            var sf = this;
            if (!Vue.evtHub) {
                Vue.evtHub = new Vue();
            }

            sf.queryDeptTree();
        },
        mounted: function () {
            var sf = this;

            sf.tableHeight = sf.$refs.tableWrap.offsetHeight - 24;
        },
        methods: {
            renderContent: function(h, { root, node, data }) {
                var sf = this;
                var dataLabelList = data.label.split('_');
                var dataValue = dataLabelList[0];
                var nodeRespName = dataLabelList[1] || '';
                return h('div',{
                    class: "tree-div-wrap",
                    on: {
                        click: function(){
                            sf.seletNode = data;
                            sf.queryTableData();
                        }
                    }
                }, [
                    h('span',{
                        class: "title-wrap",
                        style: {
                            width: 'calc(100% - 72px)',
                            display: 'inline-block',
                            overflow: 'hidden',
                            verticalAlign: 'top',
                        }
                    },[
                        h('span', dataValue)
                    ]),
                    h('span',{
                        class: "btn-node",
                    },[
                        h('span',{
                            attrs: {
                                class: 'iconfont icon-plus'
                            },
                            style: {
                                marginRight: '8px',
                                borderRadius: '50%',
                                width: '1.5rem',
                                verticalAlign: 'top'
                            },
                            on: {
                                click: function () {
                                    if(!data.value){
                                        sf.isAddRoot = true;
                                    }
                                    sf.newOrg.upDeptName = dataValue;
                                    sf.newOrg.upDeptId = data.value;
                                    sf.addOrgShow = true;
                                }
                            }
                        }),
                        h(!data.value ? '' : 'span',{
                            attrs: {
                                class: 'iconfont icon-shuru'
                            },
                            style: {
                                marginRight: '8px',
                                borderRadius: '50%',
                                width: '1.5rem',
                                verticalAlign: 'top'
                            },
                            on: {
                                click: function () {
                                    sf.editOrg.oddUpDeptName = sf.findDeptName(data.parentId).split('_')[0];
                                    sf.editOrg.oddDeptName = sf.findDeptName(data.value).split('_')[0];
                                    sf.editOrg.deptId = data.value;
                                    sf.editOrg.oddRespName = nodeRespName;
                                    sf.editOrg.oddRespId = data.deptRespId;
                                    sf.editOrg.defNo = data.no || 0;
                                    sf.editOrgShow = true;
                                }
                            }
                        }),
                        h(!data.value ? '' : 'span',{
                            attrs: {
                                class: 'iconfont icon-wechaticon13'
                            },
                            style: {
                                marginRight: '8px',
                                borderRadius: '50%',
                                width: '1.5rem',
                                verticalAlign: 'top'
                            },
                            on: {
                                click: function () {
                                    sf.$Modal.confirm({
                                        title: '提示',
                                        content: '确定要删除该组织吗？',
                                        onOk: function () {
                                            sf.deleteOrg(data);
                                        }
                                    });
                                }
                            }
                        }),
                    ])
                ]);
            },
            // 获取部门树
            queryDeptTree: function(){
                var sf = this;

                $.ajax({
                    url: linkus.location.bpm + '/bpmApply/getPermLists.action',
                    type: "get",
                    success: function (res){
                        if(!!res.deptTrees && res.deptTrees.length > 0){
                            let treeData = [];
                            let rootNode = {
                                deptRespId: '',
                                label: '亚信科技_',
                                parentId: '',
                                unSelectable: false,
                                value: '',
                                expand: true,
                                children: []
                            }
                            rootNode.children = res.deptTrees.map(function(item){
                                let item0 = {...item};
                                return item0;
                            });
                            treeData.push(rootNode);
                            sf.treeData = treeData;


                            let deptTrees = res.deptTrees.map(function(item){
                                let item1 = {...item};
                                return item1;
                            });
                            sf.editTreeData = deptTrees;
                        }
                        sf.deptList = res.depts || [];
                    },
                    error: function () {
                        sf.$Message.error("获取部门数据错误");
                    }
                });
            },
            queryTableData: function(){
                var sf = this;

                if(JSON.stringify(sf.seletNode) == '{}'){
                    sf.$Message.error({
                        content: '请选择组织',
                        duration: 3
                    });
                    return ;
                }
                sf.tableLoading = true;

                $.ajax({
                    url: linkus.location.bpm + '/bpmDept/queryDept.action',
                    type: 'post',
                    data: {
                        defId: sf.seletNode.value
                    },
                    success: function (res) {
                        sf.tableData = res.data || [];
                        sf.tableLoading = false;
                    },
                    error: function (res) {
                        sf.tableLoading = false;
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                    },
                });
            },
            exportTableData: function(){
                var sf = this;

                if(JSON.stringify(sf.seletNode) == '{}'){
                    sf.$Message.error({
                        content: '请选择组织',
                        duration: 3
                    });
                    return ;
                }

                sf.$Message.warning({
                    content  : '正在导出数据，请勿重复点击导出按钮，请稍等！',
                    duration : 1
                });
                $("#exportExcelFrame").attr("src",linkus.location.bpm + "/bpmDept/exportDept.action" +
                    "?defId=" + sf.seletNode.value);
            },
            // 查询新增部门负责人
            queryRespUser: function(keyword){
                var sf = this;

                if(!keyword){
                    return;
                }
                sf.newOrgRespLoading = true;
                $.ajax({
                    url: linkus.location.prjuser + "/sysUserCtrl/queryByKeywords.action",
                    data: {
                        keywords: keyword
                    },
                    success: function (users) {
                        sf.newOrgRespLoading = false;
                        sf.newOrgRespList = users || [];
                    },
                    error: function () {
                        sf.newOrgRespLoading = false;
                        sf.$Message.error({
                            content:"查询新增部门负责人失败",
                            duration: 10
                        });
                    }
                });
            },
            // 提交新增部门
            submitAdd: function(){
                var sf = this;

                if(sf.isAddRoot && !sf.newOrg.upDeptId){
                    sf.$Message.error({
                        content: '请选择新增部门上级部门',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.newOrg.newDeptName){
                    sf.$Message.error({
                        content: '请填写新增部门名称',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.newOrg.respId){
                    sf.$Message.error({
                        content: '请选择新增部门负责人',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.newOrg.desc){
                    sf.$Message.error({
                        content: '请填写调整说明',
                        duration: 3
                    });
                    return ;
                }
                if(sf.newOrg.defNo === 0){
                    sf.$Message.error({
                        content: '请填写编号',
                        duration: 3
                    });
                    return ;
                }

                var param = {
                    parentDefId: sf.newOrg.upDeptId,
                    deptName: sf.newOrg.newDeptName,
                    deptRespUserId: sf.newOrg.respId,
                    desc: sf.newOrg.desc,
                    defNo: sf.newOrg.defNo
                }

                $.ajax({
                    url: linkus.location.bpm + '/bpmDept/createDept.action',
                    type: 'post',
                    data: param,
                    success: function (res) {
                        sf.$Message.success({
                            content: '新增组织成功',
                            duration: 3
                        });
                        sf.addOrgShow = false;
                        sf.queryDeptTree();
                    },
                    error: function (res) {
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                        sf.addOrgShow = false;
                    },
                });
            },
            findDeptName: function(id){
                var sf = this;

                let deptName = '';
                sf.deptList.forEach(function(item){
                    if(item.cid == id){
                        deptName = item.name;
                    }
                });
                return deptName;
            },
            editDeptSelect: function(deptIds){
                var sf = this;
                if(null != deptIds && deptIds.length > 0) {
                    sf.editOrg.newUpDeptId = deptIds[deptIds.length - 1];
                }
            },
            // 查询修改后部门责任人
            queryEditRespUser: function(keyword){
                var sf = this;

                if(!keyword){
                    return;
                }
                sf.editOrgRespLoading = true;
                $.ajax({
                    url: linkus.location.prjuser + "/sysUserCtrl/queryByKeywords.action",
                    data: {
                        keywords: keyword
                    },
                    success: function (users) {
                        sf.editOrgRespLoading = false;
                        sf.editOrgRespList = users || [];
                    },
                    error: function () {
                        sf.editOrgRespLoading = false;
                        sf.$Message.error({
                            content:"查询修改后部门负责人失败",
                            duration: 10
                        });
                    }
                });
            },
            // 提交修改部门
            submitEdit: function(){
                var sf = this;

                if(!sf.editOrg.newUpDeptName || !sf.editOrg.newUpDeptId){
                    sf.$Message.error({
                        content: '请选择修改后上级部门',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.editOrg.newDeptName){
                    sf.$Message.error({
                        content: '请填写修改后部门名称',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.editOrg.newResp){
                    sf.$Message.error({
                        content: '请选择修改后部门负责人',
                        duration: 3
                    });
                    return ;
                }
                if(!sf.editOrg.desc){
                    sf.$Message.error({
                        content: '请填写调整说明',
                        duration: 3
                    });
                    return ;
                }
                if(sf.editOrg.defNo === 0){
                    sf.$Message.error({
                        content: '请填写编号',
                        duration: 3
                    });
                    return ;
                }

                var param = {
                    defId: sf.editOrg.deptId,
                    deptRespUserId: sf.editOrg.oddRespId,
                    updateParentDefId: sf.editOrg.newUpDeptId,
                    updateDeptName: sf.editOrg.newDeptName,
                    updateUserId: sf.editOrg.newResp,
                    desc: sf.editOrg.desc,
                    defNo: sf.editOrg.defNo
                }

                $.ajax({
                    url: linkus.location.bpm + '/bpmDept/updateDept.action',
                    type: 'post',
                    data: param,
                    success: function (res) {
                        sf.$Message.success({
                            content: '修改组织成功',
                            duration: 3
                        });
                        sf.editOrgShow = false;
                        sf.queryDeptTree();
                    },
                    error: function (res) {
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                        sf.editOrgShow = false;
                    },
                });
            },
            // 删除部门
            deleteOrg: function(dept){
                var sf = this;

                $.ajax({
                    url: linkus.location.bpm + '/bpmDept/deleteDept.action',
                    type: 'post',
                    data: {
                        defId: dept.value
                    },
                    success: function (res) {
                        if(res.success){
                            sf.$Message.success({
                                content: '删除组织成功',
                                duration: 3
                            });
                            sf.queryDeptTree();
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                    },
                    error: function (res) {
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                    },
                });
            },
            showMaintainRecord: function(){
                var sf = this;

                sf.getRecordData();
                sf.maintainRecordShow = true;
            },
            getRecordData: function(){
                var sf = this;

                var param = {
                    pageNo: sf.recordPageNum,
                    pageSize: sf.recodePageSize
                }
                if(sf.recordSearch.dateRange.length > 0 && sf.recordSearch.dateRange[0] && sf.recordSearch.dateRange[1]){
                    param.startDate = sf.recordSearch.dateRange[0].format('yyyy-MM-dd');
                    param.endDate = sf.recordSearch.dateRange[1].format('yyyy-MM-dd');
                }
                if(!!sf.recordSearch.keyword){
                    param.keyWord = sf.recordSearch.keyword;
                }
                sf.recordLoading = true;

                $.ajax({
                    url: linkus.location.bpm + '/bpmDept/queryRecord.action',
                    type: 'post',
                    data: param,
                    success: function (res) {
                        sf.recordLoading = false;
                        if(res.success){
                            sf.recordData = res.data.objectList || [];
                            sf.recodeDataTotal = res.data.count || 0;
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                    },
                    error: function (res) {
                        sf.recordLoading = false;
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                    },
                });
            },
            onRecordPageNumChange: function(pageNum){
                var sf = this;
                sf.recordPageNum = pageNum - 1;
                sf.getRecordData();
            },
            onRecordPageSizeChange: function(pageSize){
                var sf = this;
                sf.recodePageSize = pageSize;
                sf.getRecordData();
            },
            exportRecordData: function(){
                var sf = this;

                if(!sf.recordSearch.dateRange[0] || !sf.recordSearch.dateRange[1]){
                    sf.$Message.error({
                        content: '请选择时间',
                        duration: 3
                    });
                    return ;
                }
                var param = {
                    startDate: sf.recordSearch.dateRange[0].format('yyyy-MM-dd'),
                    endDate: sf.recordSearch.dateRange[1].format('yyyy-MM-dd'),
                    keyWord: !!sf.recordSearch.keyword ? sf.recordSearch.keyword : '',
                    pageNo: sf.recordPageNum,
                    pageSize: sf.recodePageSize
                }

                sf.$Message.warning({
                    content  : '正在导出数据，请勿重复点击导出按钮，请稍等！',
                    duration : 1
                });
                $("#exportRecordExcelFrame").attr("src",linkus.location.bpm + "/bpmDept/exportRecord.action" +
                    "?startDate=" + param.startDate + "&endDate=" + param.endDate + "&keyWord=" + param.keyWord
                    +"&pageNo=" + param.pageNo + "&pageSize=" + param.pageSize);
            },
        },
    });
</script>
</html>