(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1087eaab"],{"598c":function(t,i,e){t.exports=e.p+"static/img/solution_view_top_bg.eb7fe063.png"},"60ae":function(t,i,e){"use strict";e.r(i);var o=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{width:"100%","max-width":"1920px","min-width":"1280px",height:"100%",margin:"auto"}},[i("div",{staticStyle:{width:"100%",height:"400px",background:"url(../../assets/images/markdown/solution_view_top_bg.png) no-repeat","background-size":"100% 100%",display:"flex","align-items":"center","margin-bottom":"72px"},style:{background:"url("+e("598c")+")"}},[t._m(0)]),t._m(1)])},a=[function(){var t=this,i=t._self._c;return i("div",{staticStyle:{height:"251px",width:"1280px",margin:"auto"}},[i("p",{staticStyle:{"font-size":"16px","line-height":"19px","font-style":"italic",color:"#8C8C8C","margin-bottom":"12px"}},[t._v("Power by PRD")]),i("div",{staticStyle:{"font-size":"40px","line-height":"56px",color:"#1F1F1F","margin-bottom":"12px"}},[t._v("政务数据治理解决方案")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#1F1F1F","margin-bottom":"12px"}},[t._v("描述1（不需要可删除）")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#1F1F1F","margin-bottom":"48px"}},[t._v("产品经理：梅珂夫 13697339119")]),i("div",{staticStyle:{display:"flex","align-items":"center"}},[i("a",{staticStyle:{width:"193px","line-height":"48px",background:"#0080FF","border-radius":"4px",color:"#ffffff","text-align":"center","font-size":"16px","text-decoration":"none"},attrs:{href:"#",target:"_blank"}},[t._v("源代码申请")])])])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{height:"100px",width:"1280px",margin:"auto"}},[i("div",{staticStyle:{"margin-bottom":"96px"},attrs:{id:"title1"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"32px","text-align":"center","margin-bottom":"20px"}},[t._v("政务数据治理解决方案")]),i("p",{staticStyle:{width:"846px",margin:"auto",color:"#8C8C8C","font-size":"16px","text-align":"center","margin-bottom":"48px"}},[t._v("该方案展示了如何采用大数据技术帮助政府管理部门实施政务大数据共享建设。依托亚信科技大数据产品建设政务大数据资源平台，基于亚信多年数据治理体系方法和大数据建设运营经验，在保证数据统一标准化管理和数据安全监管的情况下，推进公共数据共享和开放，使分散、孤立的数据成为汇集、综合的数据，使管理的数据成为应用的数据")]),i("div",{staticStyle:{width:"100%","text-align":"center"}},[i("img",{staticStyle:{"max-width":"100%"},attrs:{src:e("fc9c"),alt:""}})])]),i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title2"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("方案价值")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-bottom":"12px"}},[i("li",{staticStyle:{width:"25%","text-align":"center",padding:"0 12px","margin-bottom":"60px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"24px","margin-bottom":"20px",width:"100%",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[t._v("用户洞察能力")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#8C8C8C"}},[t._v("建立以客户体验感知为核心的用户体验标签体系，分析客户业务特征、行为特征等，获取用户真实情感画像。")])])])]),i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title3"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("方案优势")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","margin-bottom":"12px"}},[i("li",{staticStyle:{width:"25%","text-align":"center",padding:"0 12px","margin-bottom":"60px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"24px","margin-bottom":"20px",width:"100%",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[t._v("更全面的企业服务场景")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#8C8C8C"}},[t._v("覆盖全生命周期全场景服务，从企业创 立—发展—上市及后续发展全生命周期 的场景服务，一站式满足企业所有的需 求。")])])])]),i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title4"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("应用场景/案例")]),i("p",{staticStyle:{width:"846px",margin:"auto",color:"#8C8C8C","font-size":"16px","text-align":"center","margin-bottom":"48px"}},[t._v("当前亚信5G智慧园区已在多个产业园区落地应用，落地场景包括园区大脑、环境管理、能源管理、安 全管理等内容。")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap"}},[i("li",{staticStyle:{width:"411px",height:"332px","margin-right":"24px","margin-bottom":"24px"}},[i("div",{staticStyle:{width:"411px",height:"200px"}},[i("img",{staticStyle:{width:"411px",height:"200px","object-fit":"cover"},attrs:{src:e("f750"),alt:""}})]),i("div",{staticStyle:{width:"100%",height:"132px",padding:"24px 64px","text-align":"center",background:"#FAFAFA"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"24px","margin-bottom":"20px"}},[t._v("安徽某循环化工产业园")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"22px",display:"-webkit-box","text-overflow":"ellipsis",overflow:"hidden","-webkit-line-clamp":"2","-webkit-box-orient":"vertical","max-height":"44px"}},[t._v("环境管理、能源管理、水资源管理、 安全管理、企业管理、运营管理")])])]),i("li",{staticStyle:{width:"411px",height:"332px","margin-right":"24px","margin-bottom":"24px"}},[i("div",{staticStyle:{width:"411px",height:"200px"}},[i("img",{staticStyle:{width:"411px",height:"200px","object-fit":"cover"},attrs:{src:e("f750"),alt:""}})]),i("div",{staticStyle:{width:"100%",height:"132px",padding:"24px 64px","text-align":"center",background:"#FAFAFA"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"24px","margin-bottom":"20px"}},[t._v("安徽某循环化工产业园")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"22px",display:"-webkit-box","text-overflow":"ellipsis",overflow:"hidden","-webkit-line-clamp":"2","-webkit-box-orient":"vertical","max-height":"44px"}},[t._v("环境管理、能源管理、水资源管理、 安全管理、企业管理、运营管理")])])]),i("li",{staticStyle:{width:"411px",height:"332px","margin-bottom":"24px"}},[i("div",{staticStyle:{width:"411px",height:"200px"}},[i("img",{staticStyle:{width:"411px",height:"200px","object-fit":"cover"},attrs:{src:e("f750"),alt:""}})]),i("div",{staticStyle:{width:"100%",height:"132px",padding:"24px 64px","text-align":"center",background:"#FAFAFA"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"24px","margin-bottom":"20px"}},[t._v("安徽某循环化工产业园")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"22px",display:"-webkit-box","text-overflow":"ellipsis",overflow:"hidden","-webkit-line-clamp":"2","-webkit-box-orient":"vertical","max-height":"44px"}},[t._v("环境管理、能源管理、水资源管理、 安全管理、企业管理、运营管理")])])])])]),i("div",{staticStyle:{"margin-bottom":"36px","padding-bottom":"72px"},attrs:{id:"title6"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("解决方案文档下载")]),i("ul",{staticClass:"down_md_list",staticStyle:{display:"flex","flex-wrap":"wrap"}})])])}],l={data(){return{}},created(){},mounted(){},methods:{}},n=l,p=(e("ec80"),e("2877")),x=Object(p["a"])(n,o,a,!1,null,null,null);i["default"]=x.exports},e660:function(t,i,e){},ec80:function(t,i,e){"use strict";e("e660")},f750:function(t,i,e){t.exports=e.p+"static/img/sol_img2.f3e92018.png"},fc9c:function(t,i,e){t.exports=e.p+"static/img/sol_img1.7f977576.png"}}]);