(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-11099e72"],{"038a":function(e,t,n){},"5d4b":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"qua_view_warp"},[t("div",{domProps:{innerHTML:e._s(e.mdValue)}})])},a=[],c=n("40d6"),d=n("8ca4"),o=n("b2d8"),r=n("7c5c"),s={components:{mavonEditor:o["mavonEditor"]},data(){return{mdValue:"",fileId:""}},watch:{$route:{handler(e,t){var n=this;n.fileId=this.$route.query.fileId,n.getViewInfo()}}},created(){var e=this;e.fileId=this.$route.query.fileId,e.getViewInfo()},mounted(){},methods:{getViewInfo(){var e=this;Object(c["m"])({id:e.fileId,knowledge:!0}).then((function(t){e.mdValue=Object(r["marked"])(t.hyperText),Object(d["a"])("5e4b63acc56a424c33470601",e.fileId,t.fileBaseDefId),e.$nextTick((function(){for(var e=document.getElementsByClassName("permissionCheckMdWarp"),t=0;t<e.length;t++){let n=e[t];e[t].onclick=function(){Object(d["e"])(n)}}}))}))}}},u=s,l=(n("dbc6"),n("2877")),f=Object(l["a"])(u,i,a,!1,null,null,null);t["default"]=f.exports},dbc6:function(e,t,n){"use strict";n("038a")}}]);