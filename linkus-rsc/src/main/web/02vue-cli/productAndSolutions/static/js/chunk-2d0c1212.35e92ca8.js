(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c1212"],{4572:function(e,i,t){"use strict";t.r(i);var n=function(){var e=this,i=e._self._c;return i("div",[e.viewSrc?i("div",{staticClass:"iframe-view"},[i("iframe",{staticStyle:{width:"100%"},style:{height:e.iframeHeight},attrs:{id:"viewIframe",src:e.viewSrc,frameborder:"0"}})]):e._e(),e.viewSrc?e._e():i("div",{domProps:{innerHTML:e._s(e.mdValue)}})])},r=[],o=t("40d6"),d=t("8ca4"),c=t("b2d8"),a=t("7c5c"),s={components:{mavonEditor:c["mavonEditor"]},data(){return{panoramaId:"6266c37ce0ee771c52f37c1d",mdValue:"",fileId:"",srcFileId:"",isPageCnfg:!1,viewSrc:"",iframeHeight:"100%"}},watch:{$route:{handler(e,i){var t=this;t.fileId=this.$route.query.fileId,t.getViewInfo()}}},created(){var e=this;if(e.fileId=this.$route.query.fileId,e.srcFileId=this.$route.query.srcFileId,e.isPageCnfg=this.$route.query.isPageCnfg,window.resizeIframeHeight=function(i){e.iframeHeight=i+"px";let t=document.getElementById("viewIframe"),n=t.contentWindow||t.contentDocument,r=n.document,o=r.getElementById("app");o.style.overflow="hidden"},e.isPageCnfg)e.viewSrc=window.location.protocol+"//"+window.location.host+"/02vue-cli/designer-view/index.html#/view?fileId="+e.fileId;else{c["mavonEditor"].getMarkdownIt();e.getViewInfo()}},mounted(){},methods:{getViewInfo(){var e=this;Object(o["m"])({id:e.fileId,knowledge:!0}).then((function(i){e.mdValue=Object(a["marked"])(i.hyperText),Object(d["a"])("5e4b63acc56a424c33470601",e.fileId,i.fileBaseDefId),e.$nextTick((function(){for(var e=document.getElementsByClassName("permissionCheckMdWarp"),i=0;i<e.length;i++){let t=e[i];e[i].onclick=function(){Object(d["e"])(t)}}}))}))}}},l=s,f=t("2877"),m=Object(f["a"])(l,n,r,!1,null,"4c983811",null);i["default"]=m.exports}}]);