(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-47107842"],{"00c6":function(t,e,i){},"73ae":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"terminologyBase_body"},[t._m(0),e("div",{staticClass:"bot_wrap"},[e("div",{staticClass:"search_and_download"},[e("div",{staticClass:"search_wrap"},[e("Input",{attrs:{placeholder:"搜索"},on:{"on-enter":t.searchTerms},model:{value:t.keyWords,callback:function(e){t.keyWords=e},expression:"keyWords"}},[e("Icon",{attrs:{slot:"prefix",type:"ios-search"},slot:"prefix"}),e("Icon",{directives:[{name:"show",rawName:"v-show",value:t.keyWords,expression:"keyWords"}],attrs:{slot:"suffix",type:"ios-close",size:"24"},on:{click:t.searchClear},slot:"suffix"})],1)],1),e("div",{staticClass:"download_wrap"},[e("Button",{on:{click:t.fileDownload}},[t._v("下载全部")])],1)]),e("div",{staticClass:"index_list"},t._l(t.btnIndexList,(function(i){return e("Button",{key:i,class:{active:t.buttonName===i},on:{click:function(e){return t.searchByIndex(i,!1)}}},[t._v(" "+t._s(i)+" ")])})),1),e("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.attributeList.length,expression:"attributeList.length !== 0"}],staticClass:"index_list",staticStyle:{margin:"0 -12px"}},t._l(t.attributeList,(function(i,s){return e("Button",{key:i.id,class:{active:i.isActive},on:{click:function(e){return t.attributeSelect(i,s)}}},[t._v(" "+t._s(i.defName)+" ")])})),1),e("div",{staticClass:"tabs_and_dictionary_wrap"},[e("div",{staticClass:"tabs_wrap"},[e("div",{staticClass:"tabs",class:{active:"缩略语及全称对照"===t.tabName},on:{click:t.toggleTabs}},[t._v("缩略语及全称对照")]),e("div",{staticClass:"tabs",class:{active:"专业术语中英文对照"===t.tabName},on:{click:t.toggleTabs}},[t._v("专业术语中英文对照")])]),e("div",{staticClass:"dictionary_wrap"},[t._l(t.searchByIndexList,(function(i){return"two"===i.entryType||i.entryType===t.tabName?e("div",{key:i.index,staticClass:"dictionary_item",on:{click:function(e){return t.setIndex(i.index)}}},[e("div",{staticClass:"index_word"},[t._v(" "+t._s(i.index)+" ")]),e("div",{staticClass:"card_list_wrap"},t._l(i.children,(function(s){return e("div",{key:s.bizId,staticClass:"card_list"},[t.tabName===s.entryType?e("div",{staticClass:"card_list_ct"},[e("div",{staticClass:"card",class:{active:t.isActive&&s.bizId===t.card.cid},attrs:{id:"referenceDiv"},on:{click:function(e){return t.setCard(s,i.index,e)}}},[e("p",[t._v(t._s(s.english)),"缩略语及全称对照"===t.tabName?e("span",[t._v(t._s(" ("+s.acronym+")"))]):t._e()]),e("p",[t._v(t._s(s.chinese))])])]):t._e()])})),0)]):t._e()})),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isActive&&t.index===t.card.index&&t.card.cid,expression:"isActive && index === card.index && card.cid"}],staticClass:"popup",class:{active:t.isActive},attrs:{id:"moveableDiv"}},[e("div",{staticClass:"popup_icon",on:{click:t.closePop}},[e("Icon",{attrs:{type:"ios-close",size:"30"}})],1),e("p",{staticClass:"en_name"},[t._v(t._s(t.card.enName)),"缩略语及全称对照"===t.tabName?e("span",[t._v(t._s(" ("+t.card.spEnName+")"))]):t._e()]),e("p",{staticClass:"name"},[t._v(t._s(t.card.name))]),e("p",{directives:[{name:"show",rawName:"v-show",value:t.card.explain,expression:"card.explain"}],staticClass:"explain"},[t._v("解释："),e("br"),e("br"),t._v(t._s(t.card.explain))])])],2)])])])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"top_wrap"},[e("p",[t._v("专业术语库")]),e("p",[t._v("Asiainfo Glossary")])])}],c=(i("14d9"),i("88a7"),i("271a"),i("5494"),i("40d6")),n=i("a026"),r={data(){return{keyWords:"",sourceList:[],buttonName:"全部",btnIndexList:["全部","0~9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","#"],tabName:"缩略语及全称对照",isDictionaryOne:!0,searchByIndexList:[],isPop:!1,isActive:!1,outsideIsActive:!1,dictionaryList:[],dictionaryLettersList:[],dictionaryNumObj:{index:"0~9",children:[]},dictionaryOthersObj:{index:"#",children:[]},index:"",card:{index:"",cid:"",isActive:!1,name:"",enName:"",spEnName:"",explain:""},attributeList:[],selectedAttributeList:[]}},watch:{},created(){const t=this;t.searchTerms(),t.queryTerminologyAttribute()},mounted(){window.addEventListener("click",this.handleOutsideClick,!0)},beforeDestroy(){window.removeEventListener("click",this.handleOutsideClick,!0)},methods:{handleOutsideClick(t){const e=document.getElementById("moveableDiv");e&&!e.contains(t.target)&&(this.isActive=!1)},searchTerms(){const t=this;var e={keyWord:t.keyWords,termAttributeIds:t.selectedAttributeList};Object(c["z"])(e).then(e=>{if(e=e||[],t.sourceList=e,t.dictionaryList=[],t.dictionaryLettersList=[],t.dictionaryNumObj={index:"0~9",children:[]},t.dictionaryOthersObj={index:"#",children:[]},t.sourceList.length>0){for(let e=0;e<t.sourceList.length;e++){let i=t.sourceList[e].english;"("===i[0]&&(i=i.slice(1));let s=i.slice(0,1);if(s>="a"&&s<="z"&&(s=s.toUpperCase()),s>="A"&&s<="Z")if(t.dictionaryLettersList.length>0){let i=!1,a=-1;for(let c=0;c<t.dictionaryLettersList.length;c++)if(t.dictionaryLettersList[c].index<s)a=c+1;else if(t.dictionaryLettersList[c].index===s){i=!0,a=-1,t.dictionaryLettersList[c].children=t.dictionaryLettersList[c].children.concat(t.sourceList[e]);break}if(!i){let i={index:s,children:[]};i.children=i.children.concat(t.sourceList[e]),t.dictionaryLettersList.splice(a,0,i)}}else{let i={index:s,children:[]};i.children=i.children.concat(t.sourceList[e]),t.dictionaryLettersList=t.dictionaryLettersList.concat(i)}else s>="0"&&s<="9"?t.dictionaryNumObj.children=t.dictionaryNumObj.children.concat(t.sourceList[e]):t.dictionaryOthersObj.children=t.dictionaryOthersObj.children.concat(t.sourceList[e])}t.dictionaryList=t.dictionaryLettersList,t.dictionaryList=t.dictionaryList.concat(t.dictionaryNumObj),t.dictionaryList=t.dictionaryList.concat(t.dictionaryOthersObj);for(let e=0;e<t.dictionaryList.length;e++)if(t.dictionaryList[e].entryType="",0!==t.dictionaryList[e].children.length){let i=[];i=t.dictionaryList[e].children,t.dictionaryList[e].entryType=i[0].entryType;for(let s=1;s<i.length;s++)if(t.dictionaryList[e].entryType!==i[s].entryType){t.dictionaryList[e].entryType="two";break}}t.searchByIndex(t.buttonName,!0)}})},searchClear(){const t=this;t.keyWords=""},searchByIndex(t,e){const i=this;if(i.buttonName!==t)i.buttonName=t;else if("全部"!==i.buttonName&&(i.searchByIndexList=i.dictionaryList,!1===e))return void(i.buttonName="全部");if("全部"!==t){var s=[];i.searchByIndexList=s;for(let e=0;e<i.dictionaryList.length;e++)if(i.dictionaryList[e].index===t)return void(i.searchByIndexList=s.concat(i.dictionaryList[e]))}else i.searchByIndexList=i.dictionaryList},toggleTabs(){const t=this;"缩略语及全称对照"===t.tabName?t.tabName="专业术语中英文对照":t.tabName="缩略语及全称对照"},setIndex(t){const e=this;e.index=t},setCard(t,e,i){const s=this;if(!1===s.isActive&&(s.isActive=!s.isActive),s.card.cid===t.bizId)return s.isActive=!s.isActive,void(s.card.cid="");s.card.index=e,s.card.cid=t.bizId,s.card.isActive=s.isActive,s.card.name=t.chinese,s.card.enName=t.english,s.card.spEnName=t.acronym,s.card.explain=t.explain;const a=i.currentTarget,c=a.parentElement.parentElement,n=c.parentElement,r=document.getElementById("moveableDiv");r.style.top=a.offsetTop+c.offsetTop+n.offsetTop+"px";const d=a.getBoundingClientRect(),o={right:window.innerWidth-d.right,left:d.left};o.right>o.left?r.style.left=a.offsetLeft+c.offsetLeft+a.clientWidth+5+"px":r.style.left=a.offsetLeft+c.offsetLeft-500-3+"px"},closePop(){const t=this;t.isActive=!t.isActive},fileDownload(){var t="66d6b48243c8c37d3ef61d4c",e="专业术语库.pdf";n["a"].prototype.$Notice.info({title:"提示",name:e,duration:0,desc:"正在下载中！"}),Object(c["c"])({fileFolderDefId:t}).then((function(t){var i=new Blob([t]),s=window.URL.createObjectURL(i),a=document.createElement("a");a.href=s,a.style.display="none",a.setAttribute("download",e),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s),n["a"].prototype.$Notice.close(e)}))},queryTerminologyAttribute(){const t=this;var e="671afb94a26dd16fe54e1baa",i="OPTION";Object(c["x"])({srcDefId:e,codeName:i}).then((function(e){var i=e||[];i.length>0&&(t.attributeList=i.map(t=>{let e={defName:t.defName,id:t.id,isActive:!1};return e}))}))},attributeSelect(t,e){const i=this;i.selectedAttributeList.indexOf(t.id)>-1?(i.selectedAttributeList=i.selectedAttributeList.filter(e=>e!==t.id),i.attributeList[e].isActive=!1):(i.selectedAttributeList.push(t.id),i.attributeList[e].isActive=!0),i.searchTerms()}}},d=r,o=(i("c1f2"),i("2877")),l=Object(o["a"])(d,s,a,!1,null,null,null);e["default"]=l.exports},c1f2:function(t,e,i){"use strict";i("00c6")}}]);