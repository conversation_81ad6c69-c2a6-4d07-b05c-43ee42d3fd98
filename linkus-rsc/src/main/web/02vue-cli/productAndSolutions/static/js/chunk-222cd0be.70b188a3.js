(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-222cd0be"],{"4ac6":function(e,i,t){"use strict";t.r(i);var n=function(){var e=this,i=e._self._c;return i("div",[e.srcFileId?i("div",{staticClass:"iframe-view"},[i("iframe",{staticStyle:{width:"100%"},style:{height:e.iframeHeight},attrs:{id:"viewIframe",src:e.viewSrc,frameborder:"0"}})]):i("div",{staticClass:"no_data size16"},[i("img",{staticClass:"size200",attrs:{src:t("f4f0"),alt:""}}),i("p")])])},a=[],s=t("8ca4"),r={name:"index",data(){return{mapId:"646d6c242b884b4eb705800a",mapMenu:[],submenuId:"",fileId:"",srcFileId:"",viewSrc:"",iframeHeight:"100%"}},created(){var e=this;e.submenuId=e.$route.query.submenuId,window.resizeIframeHeight=function(i){e.iframeHeight=i+"px";let t=document.getElementById("viewIframe"),n=t.contentWindow||t.contentDocument,a=n.document,s=a.getElementById("app");s.style.overflow="hidden"}},mounted(){var e=this;s["c"].$on("getFolderTreeAndFiles",(function(i){i=i||[],i.forEach((function(i){i.value===e.mapId&&(e.mapMenu=i.children||[])}));var t={};e.mapMenu.forEach(i=>{i.value===e.submenuId&&(t=i.files,i.files&&i.files.length>0&&(t=i.files[0]))}),e.fileId=t.id,e.srcFileId=t.srcFileId,e.viewSrc=window.location.protocol+"//"+window.location.host+"/02vue-cli/designer-view/index.html#/view?pageId="+e.srcFileId})),e.$store.subMenuGlobalList&&s["c"].$emit("getFolderTreeAndFiles",e.$store.subMenuGlobalList)},methods:{}},d=r,c=t("2877"),o=Object(c["a"])(d,n,a,!1,null,"f7adb308",null);i["default"]=o.exports},f4f0:function(e,i,t){e.exports=t.p+"static/img/noData.00bb4f88.png"}}]);