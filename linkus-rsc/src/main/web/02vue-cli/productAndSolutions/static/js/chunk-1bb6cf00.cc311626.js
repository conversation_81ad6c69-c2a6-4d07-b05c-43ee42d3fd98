(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1bb6cf00"],{ba16:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"md_file_view_con",style:{height:e.mdHeight+"px"}},[t("div",{staticClass:"md_file_view_left"},[t("Tree",{attrs:{data:e.menuList},on:{"on-select-change":e.treeSelect}})],1),t("div",{ref:"md_view_scroll",staticClass:"md_file_view_right"},[t("div",{staticClass:"markdown-body",domProps:{innerHTML:e._s(e.mdValue)}})])])},i=[],l=(n("14d9"),n("40d6")),a=n("8ca4"),d=n("b2d8"),c=n("7c5c"),o={components:{mavonEditor:d["mavonEditor"]},data(){return{productId:"627a35112e94784d18963e1e",mdValue:"",fileId:"",hyperText:"",menuList:[],mdHeight:document.documentElement.clientHeight}},watch:{$route:{handler(e,t){var n=this;n.fileId=this.$route.query.fileId,n.getViewInfo()}}},created(){var e=this;d["mavonEditor"].getMarkdownIt();e.fileId=this.$route.query.fileId,e.getViewInfo()},mounted(){},methods:{getViewInfo(){var e=this;Object(l["m"])({id:e.fileId,knowledge:!0}).then((function(t){e.hyperText=t.hyperText,e.mdValue=Object(c["marked"])(t.hyperText),e.menuList=e.handleNavTree([1,2,3,4,5,6]),Object(a["a"])("5e4b63acc56a424c33470601",e.fileId,t.fileBaseDefId),e.$nextTick((function(){for(var e=document.getElementsByClassName("permissionCheckMdWarp"),t=0;t<e.length;t++){let n=e[t];e[t].onclick=function(e){Object(a["e"])(n)}}}))}))},treeSelect(e,t){document.getElementById(t.id).scrollIntoView(!0)},handleNavTree(e){let t=this.getTitle(this.mdValue);console.log(t);let n,r=[];return e.forEach(e=>{n=this.find(t,{level:e}),0===r.length?r=r.concat(n):n.forEach(e=>{e=Object.assign(e);let n=this.getParentIndex(t,e.index);return this.appendToParentNav(r,n,e)})}),r},getParentIndex(e,t){for(var n=t-1;n>=0;n--)if(e[t].level>e[n].level)return e[n].index},appendToParentNav(e,t,n){var r=this;let i=this.findIndex(e,{index:t});-1===i?e.forEach((function(e,i){return r.appendToParentNav(e.children,t,n)})):e[i].children=e[i].children.concat(n)},findIndex(e,t){let n=-1;return e.forEach((e,r)=>{for(var i in t)if(t.hasOwnProperty(i)&&t[i]!==e[i])return!1;n=r}),n},find(e,t){return e.filter(e=>{for(let n in t)if(t.hasOwnProperty(n)&&t[n]!==e[n])return!1;return!0})},getTitle(e){let t=[],n=[];e.replace(/<[Hh]([1-6]).*?>(.*?)<\/[Hh]([1-6])>/g,(function(e,t){let r=e.replace("\n",""),i=parseInt(t);n.push({id:r.replace(/<[Hh]([1-6]).*?id="+/,"").replace(/".*?>.*?[Hh]([1-6])>/,""),title:r.replace(/<[Hh]([1-6]).*?>+/,"").replace(/<\/[Hh]([1-6])>/,""),level:i,children:[]})})),t=n.filter(e=>e.level>=1&&e.level<=4);let r=0;return t.map(e=>(e.index=r++,e))}}},s=o,h=(n("ea3a"),n("2877")),u=Object(h["a"])(s,r,i,!1,null,null,null);t["default"]=u.exports},e192:function(e,t,n){},ea3a:function(e,t,n){"use strict";n("e192")}}]);