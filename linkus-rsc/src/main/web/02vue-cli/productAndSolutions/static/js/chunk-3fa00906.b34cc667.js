(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3fa00906"],{"0eb5":function(t,i,e){t.exports=e.p+"static/img/img2.30aa9a17.png"},1049:function(t,i,e){t.exports=e.p+"static/img/ico_1.dceb2197.svg"},"2a9e":function(t,i,e){t.exports=e.p+"static/img/img3.e2cf0b3f.png"},"66a5":function(t,i,e){t.exports=e.p+"static/img/img4.90e0dd22.png"},"76e2":function(t,i,e){},"7b8f":function(t,i,e){t.exports=e.p+"static/img/pc_banner.dd7c223e.png"},"7fea":function(t,i){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGuSURBVHgBlVPtUcJAEL07LCD/gSFWYKzAowOsAKwAqECtQK1ArAA7MB2IFZAZ+M8VAMT34p5zXBIHd+bIsfv27d5+aPWHdLvdzBiT4erKsnTb7TZvw+pYkaZpcjwep1rrGf4mkbkAYQ7yxwLSStTr9Wyn01kKQQ6nL5xVBdQ6xRkzFgkRbL7ZbN7jRFS/3x8NBoMSZ4esRqpFYJsAsyaWgU8ygjFF5A+JPAzTpq16U6QD/lPwlzA5wz9I8552KGtvZwAf5LdQEGDvWAL4spbKCKsV+0KdKcCyPjl8pxURsrTMBlHf1D+FzWBW4MjM4XBIRFl4AEeAxY8dqaMtIKo6Co6MT0saIr1iVpZwGgfkY+pQk0WMx8i4C/ys4Mg6Zd6A+xy6DMc7ObkXIJsFuJTf/X7vDIrG9ByUVx4gXRnyKipmXcSjIQNacHV8117wsWFdIrIaCbAT9dOkvOKQ9ydQrIX0+syBbBxgGq2syFqiNYqs0o7YcJVOlpYGRHpSspicLT8WrKE0xEpNb5FI3kjk00aLHwC8EcJQnNTzmfsVGmpEEanFJ+HQckykw43yDeZb/mWUmXJPAAAAAElFTkSuQmCC"},b144:function(t,i,e){"use strict";e.r(i);var l=function(){var t=this,i=t._self._c;return i("div",{staticStyle:{width:"100%","max-width":"1920px","min-width":"1280px",height:"auto",margin:"auto"}},[i("div",{staticStyle:{width:"100%",height:"400px",background:"url(../../assets/images/markdown/pc_banner.png) no-repeat","background-size":"100% 100%",display:"flex","align-items":"center"},style:{background:"url("+e("7b8f")+")"}},[t._m(0)]),t._m(1),i("div",{staticStyle:{width:"1280px",margin:"auto"}},[i("div",{staticStyle:{"margin-bottom":"96px"},attrs:{id:"title1"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"32px","text-align":"center","margin-bottom":"20px"}},[t._v("数字孪生平台")]),i("p",{staticStyle:{width:"846px",margin:"auto",color:"#8C8C8C","font-size":"16px","text-align":"center","margin-bottom":"48px"}},[t._v("AISWare DigitalGemini面向智慧城市各行各业提供构建数字孪生技术底座的通用技术平台，通过孪生体自定义开发、基于业务灵活编排孪生场景的核心能力，助力企业实现轻量化自主交付。通过城市现实和虚拟数据闭环治理，结合AI融智洞悉城市运行态势，实现城市智慧运营。")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","justify-content":"center","margin-bottom":"12px"}},[t._l(6,(function(l){return i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}},[i("img",{attrs:{src:e("1049"),alt:""}}),i("div",{staticStyle:{color:"#1F1F1F","line-height":"20px","font-size":"24px","margin-bottom":"20px"}},[t._v("描绘城市")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"22px"}},[t._v("融合多源信息，雕琢城市模型")])])})),i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}}),i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}})],2),t._m(2)]),i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title2"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("产品优势")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","justify-content":"center","margin-bottom":"12px"}},[t._l(6,(function(l){return i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}},[i("img",{attrs:{src:e("1049"),alt:""}}),t._v(" "),i("div",{staticStyle:{color:"#1F1F1F","line-height":"20px","font-size":"24px","margin-bottom":"20px"}},[t._v("描绘城市")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"22px"}},[t._v("融合多源信息，雕琢城市模型")])])})),i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}}),i("li",{staticStyle:{width:"280px","text-align":"center",padding:"0 30px","margin-bottom":"60px"}})],2)]),i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title3"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("产品价值")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","justify-content":"space-between"}},t._l(4,(function(e){return i("li",{staticStyle:{width:"calc(50% - 12px)",height:"212px",background:"#FAFAFA",display:"flex","align-items":"center",padding:"26px 20px 26px 0","margin-bottom":"24px"}},[i("div",{staticStyle:{width:"calc(100% - 200px)","margin-right":"40px"}},[i("div",{staticStyle:{"line-height":"50px",color:"#1F1F1F","border-left":"4px solid #0080FF","padding-left":"20px","font-size":"36px","margin-bottom":"20px"}},[t._v("0"+t._s(e))]),t._m(3,!0)]),t._m(4,!0)])})),0)]),t._m(5),t._m(6),i("div",{staticStyle:{"margin-bottom":"36px","padding-bottom":"72px"},attrs:{id:"title6"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("产品文档下载")]),i("ul",{staticClass:"down_md_list",staticStyle:{display:"flex","flex-wrap":"wrap"}},t._l(19,(function(l){return i("li",{staticClass:"permissionCheckMdWarp",staticStyle:{border:"0.5px solid #D9D9D9","border-radius":"2px",width:"193px",cursor:"pointer",height:"60px","line-height":"60px",padding:"0 12px",display:"flex","align-items":"center","justify-content":"center",margin:"0 24px 24px 0"}},[i("div",{staticStyle:{"max-width":"calc(100% - 26px)","margin-right":"8px",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[t._v("产品报告")]),i("img",{staticStyle:{width:"18px",height:"18px"},attrs:{src:e("7fea"),alt:""}})])})),0)]),t._m(7)])])},a=[function(){var t=this,i=t._self._c;return i("div",{staticStyle:{height:"251px",width:"1280px",margin:"auto"}},[i("p",{staticStyle:{"font-size":"16px","line-height":"19px","font-style":"italic",color:"#8C8C8C","margin-bottom":"12px"}},[t._v("Power by PRD")]),i("div",{staticStyle:{"font-size":"40px","line-height":"56px",color:"#1F1F1F","margin-bottom":"12px"}},[t._v("数字孪生平台")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#1F1F1F","margin-bottom":"12px"}},[t._v("数字孪生平台 (AISWare DigitalGemini) 可快速构建应用的、面向业务交付的数字孪生底座。")]),i("p",{staticStyle:{"font-size":"16px","line-height":"22px",color:"#1F1F1F","margin-bottom":"48px"}},[t._v("产品经理：郭帅 13399997878")]),i("div",{staticStyle:{display:"flex","align-items":"center"}},[i("a",{staticStyle:{width:"193px","line-height":"48px",background:"#0080FF","border-radius":"4px",color:"#ffffff","text-align":"center","font-size":"16px","text-decoration":"none"},attrs:{href:"#",target:"_blank"}},[t._v("适用产品")]),i("div",{staticStyle:{"margin-left":"24px",display:"flex","align-items":"center"}},[i("a",{staticStyle:{"padding-right":"12px","border-right":"1px solid #0080FF","line-height":"16px","text-decoration":"none"},attrs:{href:"#",target:"_blank"}},[t._v("License申请")]),i("a",{staticStyle:{"padding-left":"12px","text-decoration":"none"},attrs:{href:"#",target:"_blank"}},[t._v("源代码申请")])])])])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"box-shadow":"0 2px 2px 2px rgba(0,0,0,0.08)",background:"#ffffff","margin-bottom":"48px"}},[i("div",{staticStyle:{width:"1280px",display:"flex",margin:"auto",height:"48px","line-height":"48px","font-size":"16px"}},[i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title1').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("概览")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title2').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("产品优势")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title3').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("产品价值")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title4').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("应用场景")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title5').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("客户成功故事")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F","margin-right":"72px"},attrs:{onclick:"document.querySelector('#title6').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("产品文档下载")]),i("span",{staticStyle:{cursor:"pointer",color:"#1F1F1F"},attrs:{onclick:"document.querySelector('#title7').scrollIntoView(true)",onMouseOver:"this.style.borderBottom='2px solid #0080FF'",onMouseOut:"this.style.borderBottom='2px solid transparent'"}},[t._v("产品学习路径")])])])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{width:"100%"}},[i("img",{attrs:{src:e("c986"),alt:""}})])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"padding-left":"24px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"34px","margin-bottom":"12px"}},[t._v("虚实融合万物皆可孪生")]),i("p",{staticStyle:{color:"#8C8C8C","font-size":"16px","line-height":"28px",display:"-webkit-box","text-overflow":"ellipsis",overflow:"hidden","-webkit-line-clamp":"2","-webkit-box-orient":"vertical","max-height":"56px"}},[t._v("支持业务场景的拖拽式在线编排，灵活定义应用场景。提供丰富的组件库及规则编排能力，低代码配置场景。")])])},function(){var t=this,i=t._self._c;return i("div",[i("img",{attrs:{src:e("0eb5"),alt:""}})])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"margin-bottom":"36px"},attrs:{id:"title4"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("应用场景")]),i("ul",{staticStyle:{display:"flex","flex-wrap":"wrap","padding-left":"0"}},[i("li",{staticStyle:{display:"flex",width:"calc(33% - 12px)",margin:"0 24px 48px 0"}},[i("div",{staticStyle:{width:"50%",background:"#FAFAFA",padding:"20px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px",overflow:"hidden","text-overflow":"ellipsis","margin-bottom":"48px"}},[t._v("智慧展馆")]),i("ul",[i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("运营中心")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("能效监控")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("应急安防")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("客流分析")])])]),i("img",{attrs:{src:e("2a9e"),alt:""}})]),i("li",{staticStyle:{display:"flex",width:"calc(33% - 12px)",margin:"0 24px 48px 0"}},[i("div",{staticStyle:{width:"50%",background:"#FAFAFA",padding:"20px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px",overflow:"hidden","text-overflow":"ellipsis","margin-bottom":"48px"}},[t._v("智慧展馆")]),i("ul",[i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("运营中心")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("能效监控")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("应急安防")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("客流分析")])])]),i("img",{attrs:{src:e("2a9e"),alt:""}})]),i("li",{staticStyle:{display:"flex",width:"calc(33% - 12px)",margin:"0 0 48px 0"}},[i("div",{staticStyle:{width:"50%",background:"#FAFAFA",padding:"20px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px",overflow:"hidden","text-overflow":"ellipsis","margin-bottom":"48px"}},[t._v("智慧展馆")]),i("ul",[i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("运营中心")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("能效监控")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("应急安防")]),i("li",{staticStyle:{color:"#8C8C8C","margin-bottom":"20px"}},[t._v("客流分析")])])]),i("img",{attrs:{src:e("2a9e"),alt:""}})])])])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"margin-bottom":"96px"},attrs:{id:"title5"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("客户成功故事")]),i("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"48px"}},[i("div",{staticStyle:{background:"#FAFAFA"}},[i("div",{staticStyle:{padding:"48px",height:"319px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"20px","margin-bottom":"34px"}},[t._v("某数字孪生智慧展馆平台")]),i("p",{staticStyle:{"line-height":"22px",color:"#8C8C8C","font-size":"16px"}},[t._v("基于AISWare DigitalGemini 为某展馆打造数字孪生智慧展馆，通过室内外三维空间场景建模，融合物联网、视频采集、实时定位等感知数据，帮助场馆实现基于主题事件的高效数字化运营，支撑人流监测、消防预案演练、应急疏散模拟、能耗异常监测、客群偏好识别、精准营销等业务需求。")])]),i("div",{staticStyle:{height:"136px",background:"#0080FF",padding:"34px  48px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"padding-right":"27px",color:"#FFFFFF","border-right":"1px solid #ffffff"}},[i("div",{staticStyle:{"font-size":"40px","line-height":"30px","margin-bottom":"12px"}},[t._v("85%")]),i("div",{staticStyle:{"font-size":"16px","line-height":"26px"}},[t._v("节省监控人力")])]),i("div",{staticStyle:{"padding-right":"27px",color:"#FFFFFF"}},[i("div",{staticStyle:{"font-size":"40px","line-height":"30px","margin-bottom":"12px"}},[t._v("100%")]),i("div",{staticStyle:{"font-size":"16px","line-height":"26px"}},[t._v("提高应急处理效率")])])])]),i("div",{staticStyle:{height:"455px"}},[i("img",{attrs:{src:e("66a5"),alt:""}})])]),i("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"48px"}},[i("div",{staticStyle:{height:"455px"}},[i("img",{attrs:{src:e("66a5"),alt:""}})]),i("div",{staticStyle:{background:"#FAFAFA"}},[i("div",{staticStyle:{padding:"48px",height:"319px"}},[i("div",{staticStyle:{color:"#1F1F1F","font-size":"24px","line-height":"20px","margin-bottom":"34px"}},[t._v("某数字孪生智慧展馆平台")]),i("p",{staticStyle:{"line-height":"22px",color:"#8C8C8C","font-size":"16px"}},[t._v("基于AISWare DigitalGemini 为某展馆打造数字孪生智慧展馆，通过室内外三维空间场景建模，融合物联网、视频采集、实时定位等感知数据，帮助场馆实现基于主题事件的高效数字化运营，支撑人流监测、消防预案演练、应急疏散模拟、能耗异常监测、客群偏好识别、精准营销等业务需求。")])]),i("div",{staticStyle:{height:"136px",background:"#0080FF",padding:"34px  48px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"padding-right":"27px",color:"#FFFFFF","border-right":"1px solid #ffffff"}},[i("div",{staticStyle:{"font-size":"40px","line-height":"30px","margin-bottom":"12px"}},[t._v("85%")]),i("div",{staticStyle:{"font-size":"16px","line-height":"26px"}},[t._v("节省监控人力")])]),i("div",{staticStyle:{"padding-right":"27px",color:"#FFFFFF"}},[i("div",{staticStyle:{"font-size":"40px","line-height":"30px","margin-bottom":"12px"}},[t._v("100%")]),i("div",{staticStyle:{"font-size":"16px","line-height":"26px"}},[t._v("提高应急处理效率")])])])])])])},function(){var t=this,i=t._self._c;return i("div",{staticStyle:{"margin-bottom":"36px","padding-bottom":"72px"},attrs:{id:"title7"}},[i("div",{staticStyle:{color:"#1F1F1F","text-align":"center","line-height":"32px","font-size":"32px","margin-bottom":"48px"}},[t._v("产品学习路径")]),i("div",{staticClass:"product_learning_md_list",staticStyle:{display:"flex","flex-wrap":"wrap"}},[i("div",{staticStyle:{width:"100%",display:"flex"}},[i("div",{staticStyle:{width:"150px",display:"flex","padding-top":"20px"}},[i("span",{staticStyle:{"line-height":"44px","margin-right":"12px","font-weight":"500",color:"#181818","font-size":"18px"}},[t._v("了解")]),i("div",[i("img",{staticStyle:{width:"44px",height:"44px"},attrs:{src:"https://img.alicdn.com/imgextra/i3/O1CN01HO4b1Y1M3cuz8LH8i_!!6000000001379-2-tps-124-122.png",alt:""}}),i("div",{staticStyle:{height:"calc(100% - 42px)",width:"1px","background-color":"#D9D9D9",margin:"4px auto"}})])]),i("div",{staticStyle:{width:"calc(100% - 150px)"}},[i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("关于DW")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v("产品概述")])])]),i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("关于DW")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{mdId:"6286f3e9e0ee7763f39b70e2"}},[t._v("产品概述")]),i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{mdId:"6286f3e9e0ee7763f39b70e2"}},[t._v("产品概述")]),i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{mdId:"6286f3e9e0ee7763f39b70e2"}},[t._v("产品概述")]),i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"},attrs:{mdId:"6286f3e9e0ee7763f39b70e2"}},[t._v("产品概述")])])])])]),i("div",{staticStyle:{width:"100%",display:"flex"}},[i("div",{staticStyle:{width:"150px",display:"flex","padding-top":"20px"}},[i("span",{staticStyle:{"line-height":"44px","margin-right":"12px","font-weight":"500",color:"#181818","font-size":"18px"}},[t._v("上手")]),i("div",[i("img",{staticStyle:{width:"44px",height:"44px"},attrs:{src:"https://img.alicdn.com/imgextra/i3/O1CN01fEzHtG1GnWv4XrR8n_!!6000000000667-2-tps-124-122.png",alt:""}}),i("div",{staticStyle:{height:"calc(100% - 42px)",width:"1px","background-color":"#D9D9D9",margin:"4px auto"}})])]),i("div",{staticStyle:{width:"calc(100% - 150px)"}},[i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("关于DW")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v("产品概述")])])]),i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("数据开发与分析数据开发与分析")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v("产品概述")])])])])]),i("div",{staticStyle:{width:"100%",display:"flex"}},[i("div",{staticStyle:{width:"150px",display:"flex","padding-top":"20px"}},[i("span",{staticStyle:{"line-height":"44px","margin-right":"12px","font-weight":"500",color:"#181818","font-size":"18px"}},[t._v("使用")]),i("div",[i("img",{staticStyle:{width:"44px",height:"44px"},attrs:{src:"https://img.alicdn.com/imgextra/i1/O1CN01G8ziQW27YZcatuFQI_!!6000000007809-2-tps-124-122.png",alt:""}})])]),i("div",{staticStyle:{width:"calc(100% - 150px)"}},[i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("关于DW")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap",cursor:"pointer"}},[t._v("产品概述")])])]),i("div",{staticStyle:{display:"flex",border:"1px solid #D9D9D9",padding:"24px 0","margin-bottom":"8px"}},[i("span",{staticStyle:{width:"200px",display:"flex","align-items":"center",padding:"0 20px","border-right":"1px solid #D9D9D9","line-height":"16px"}},[t._v("数据开发与分析数据开发与分析")]),i("ul",{staticClass:"md_file_help_file",staticStyle:{width:"calc(100% - 200px)",display:"flex","flex-wrap":"wrap",padding:"0 20px","margin-bottom":"0"}},[i("li",{staticStyle:{"line-height":"32px",width:"33%",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v("产品概述")])])])])])])])}],o={data(){return{}},created(){},mounted(){},methods:{}},p=o,r=(e("cf72"),e("2877")),s=Object(r["a"])(p,l,a,!1,null,null,null);i["default"]=s.exports},c986:function(t,i,e){t.exports=e.p+"static/img/video.a07e5ca4.png"},cf72:function(t,i,e){"use strict";e("76e2")}}]);