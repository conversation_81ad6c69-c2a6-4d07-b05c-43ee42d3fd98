(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ded2862"],{"67c1":function(i,t,e){},a9f6:function(i,t,e){"use strict";e("67c1")},eaaf:function(i,t,e){"use strict";e.r(t);var s=function(){var i=this,t=i._self._c;return t("div",{staticClass:"qualification_and_honor"},[i._m(0),t("div",{staticClass:"bot_warp"},[t("div",{staticClass:"bot_l_warp"},[t("div",{staticClass:"list"},[t("div",{staticClass:"list_search"},[t("Select",{attrs:{loading:i.searchLoading,placeholder:"请输入产品资质或荣誉名称","remote-method":i.remoteMethod,filterable:"",clearable:""},on:{"on-change":i.searchToView},model:{value:i.listKeyWords,callback:function(t){i.listKeyWords=t},expression:"listKeyWords"}},[t("Icon",{attrs:{slot:"prefix",type:"ios-search",size:"16"},slot:"prefix"}),i._l(i.list,(function(e){return e.kpType&&i.kpTypeViewId===e.kpType.cid||e.isPageCnfg?t("Option",{key:e.id,attrs:{value:e.id+"-"+(!!e.isPageCnfg&&e.isPageCnfg)}},[i._v(" "+i._s(e.fileName)+" "),t("p",{staticClass:"select_desc"},[i._v(i._s(e.uploadDesc))])]):i._e()}))],2)],1),t("div",{staticClass:"list_div_wrap"},i._l(i.subList,(function(e,s){return t("div",{key:e.value,staticClass:"list_menu_wrap"},[t("div",{staticClass:"list_div_title"},[i._v(i._s(e.label))]),t("ul",i._l(e.children,(function(e,s){return t("li",{key:e.value,class:{active:i.activeMenuValue===e.value},on:{click:function(t){return i.selectMenu(e.value)},mousemove:function(t){return i.prdHover(e.value)}}},[t("span",[i._v(" "+i._s(e.label)+" "),t("em")])])})),0)])})),0)])]),t("div",{staticClass:"bot_r_warp"},[t("div",{staticClass:"prd_list"},[t("div",{staticClass:"title ellipsis"},[i._v(i._s(i.rightItem.label))]),i.rightItem.children?t("div",i._l(i.rightItem.children,(function(e){return t("div",[t("div",{staticClass:"title_sub ellipsis"},[i._v(i._s(e.label))]),e.children?t("div",i._l(e.children,(function(e){return t("div",[t("div",{staticClass:"title_sub three_title ellipsis"},[i._v(i._s(e.label))]),e.children?i._e():t("ul",{staticClass:"list"},i._l(e.files,(function(e){return i.kpTypeViewId===e.kpType.cid?t("li",[t("div",{staticClass:"li_left"},[t("div",{staticClass:"li_title"},[i._v(i._s(e.fileName))]),t("p",[i._v(i._s(i.hyperTextsConvert(e.uploadDesc)))]),t("div",{staticClass:"li_date"},[i.isQualification?t("span",[i._v("获得资质日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e(),i.isHonor?t("span",[i._v("获得荣誉日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e()]),i.isQualification?t("div",{staticClass:"li_link"},[t("Button",{attrs:{type:"primary"},on:{click:i.toOutQualification}},[i._v("资质获取")])],1):i._e()]),t("div",{staticClass:"li_right"},[t("img",{attrs:{src:"/"+e.images,alt:""}})])]):i._e()})),0)])})),0):t("ul",{staticClass:"list"},i._l(e.files,(function(e){return i.kpTypeViewId===e.kpType.cid?t("li",[t("div",{staticClass:"li_left"},[t("div",{staticClass:"li_title"},[i._v(i._s(e.fileName))]),t("p",[i._v(i._s(i.hyperTextsConvert(e.uploadDesc)))]),t("div",{staticClass:"li_date"},[i.isQualification?t("span",[i._v("获得资质日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e(),i.isHonor?t("span",[i._v("获得荣誉日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e()]),i.isQualification?t("div",{staticClass:"li_link"},[t("Button",{attrs:{type:"primary"},on:{click:i.toOutQualification}},[i._v("资质获取")])],1):i._e()]),t("div",{staticClass:"li_right"},[t("img",{attrs:{src:"/"+e.images,alt:""}})])]):i._e()})),0)])})),0):t("ul",{staticClass:"list"},i._l(i.rightItem.files,(function(e){return i.kpTypeViewId===e.kpType.cid?t("li",[t("div",{staticClass:"li_left"},[t("div",{staticClass:"li_title"},[i._v(i._s(e.fileName))]),t("p",[i._v(i._s(i.hyperTextsConvert(e.uploadDesc)))]),t("div",{staticClass:"li_date"},[i.isQualification?t("span",[i._v("获得资质日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e(),i.isHonor?t("span",[i._v("获得荣誉日期："+i._s(i.hyperTextsConvert(e.uploadDesc,!0)))]):i._e()]),i.isQualification?t("div",{staticClass:"li_link"},[t("Button",{attrs:{type:"primary"},on:{click:i.toOutQualification}},[i._v("资质获取")])],1):i._e()]),t("div",{staticClass:"li_right"},[t("img",{attrs:{src:"/"+e.images,alt:""}})])]):i._e()})),0)])])])])},l=[function(){var i=this,t=i._self._c;return t("div",{staticClass:"top_warp"},[t("div",{staticClass:"top_title"},[t("p",[i._v("亚信科技产品资质与荣誉")]),t("p")])])}],a=(e("14d9"),e("8ca4")),n=e("40d6"),r={name:"QualificationAndHonor",data(){return{newsId:"62bacfe9e0ee775e97b643bf",newsBannerId:"62bad01fe0ee775e97b643c6",newsBannerList:[],pqAndHonorId:"627c8fcde0ee7757795cae67",productId:"685b96868dcfc50df01e26d4",honorId:"685b968c8dcfc50df01e26d5",kpTypeViewId:"5dcbc254c56a424c33470473",subList:[],activeMenuValue:"",rightItem:{},host:window.location.host,listKeyWords:"",searchLoading:!1,list:[],isQualification:!1,isHonor:!1}},created(){},mounted(){const i=this;a["c"].$on("getFolderTreeAndFiles",(function(t){t=t||[],t.forEach((function(t){if(t.value===i.pqAndHonorId){let e=t.children.map((function(i){let t={...i};return t}));e=i.sortListByDate(e),i.subList=e,i.subList.length>0&&i.subList[0].children&&i.subList[0].children.length>0&&(i.rightItem=i.subList[0].children[0],i.subList[0].label.indexOf("资质")>-1?(i.isQualification=!0,i.isHonor=!1):i.subList[0].label.indexOf("荣誉")>-1&&(i.isQualification=!1,i.isHonor=!0),i.activeMenuValue=i.subList[0].children[0].value)}else if(t.value===i.newsId)for(var e=0;e<t.children.length;e++)i.newsBannerId===t.children[e].value&&(i.newsBannerList=t.children[e].files||[])}))})),i.$store.subMenuGlobalList&&a["c"].$emit("getFolderTreeAndFiles",i.$store.subMenuGlobalList)},methods:{remoteMethod(i){const t=this;Object(n["h"])({fileFolderDefId:t.pqAndHonorId,keyWord:i}).then((function(i){t.list=i||[]}))},selectMenu(i){const t=this;t.rightItem={},t.subList.forEach((e,s)=>{e.children&&e.children.length>0&&e.children.forEach((s,l)=>{i===s.value&&(t.rightItem=s,e.label.indexOf("资质")>-1?(t.isQualification=!0,t.isHonor=!1):e.label.indexOf("荣誉")>-1&&(t.isQualification=!1,t.isHonor=!0))})})},toView(i){this.$router.push({path:"/dmp/QualificationView",name:"QualificationView",query:{fileId:i.id}})},hyperTextsConvert(i,t){if(i){var e=i.split("#"),s=e.pop();return t?s:e.join("#")}},searchToView(i){const t=this;var e="/dmp/QualificationView",s="QualificationView";const l=i.split("-")[0],a=i.split("-")[1];setTimeout((function(){t.clearKeyWords()}),200),this.$router.push({path:e,name:s,query:{fileId:l,isPageCnfg:"true"===a}})},clearKeyWords(){const i=this;i.listKeyWords="",i.list=[]},toOutQualification(){window.location.href="https://qas.asiainfo.com/webapps/ai-qas-web/borrows/qualificationSearch/list"},prdHover(i){const t=this;t.activeMenuValue=i},sortListByDate(i){const t=this;return i=i.map((function(i){let e={...i};return e.children&&e.children.length>0?e.children=t.sortListByDate(e.children):e.files&&e.files.length>0&&e.files.sort((function(i,e){let s=new Date(t.hyperTextsConvert(i.uploadDesc,!0)),l=new Date(t.hyperTextsConvert(e.uploadDesc,!0));return l-s})),e})),i}}},c=r,o=(e("a9f6"),e("2877")),d=Object(o["a"])(c,s,l,!1,null,null,null);t["default"]=d.exports}}]);