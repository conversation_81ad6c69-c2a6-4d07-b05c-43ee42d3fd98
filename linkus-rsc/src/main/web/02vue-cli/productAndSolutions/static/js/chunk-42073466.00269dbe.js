(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-42073466"],{a995:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("div",{domProps:{innerHTML:e._s(e.mdValue)}})])},c=[],o=n("40d6"),d=n("8ca4"),a=n("b2d8"),r=n("7c5c"),u={components:{mavonEditor:a["mavonEditor"]},data(){return{mdValue:"",fileId:""}},watch:{$route:{handler(e,t){var n=this;n.fileId=this.$route.query.fileId,n.getViewInfo()}}},created(){var e=this;a["mavonEditor"].getMarkdownIt();e.fileId=this.$route.query.fileId,e.getViewInfo()},mounted(){},methods:{getViewInfo(){var e=this;Object(o["m"])({id:e.fileId,knowledge:!0}).then((function(t){e.mdValue=Object(r["marked"])(t.hyperText),Object(d["a"])("5e4b63acc56a424c33470601",e.fileId,t.fileBaseDefId),e.$nextTick((function(){for(var e=document.getElementsByClassName("permissionCheckMdWarp"),t=0;t<e.length;t++){let n=e[t];e[t].onclick=function(e){Object(d["e"])(n)}}}))}))}}},l=u,s=(n("c7e1"),n("2877")),f=Object(s["a"])(l,i,c,!1,null,null,null);t["default"]=f.exports},c5cb:function(e,t,n){},c7e1:function(e,t,n){"use strict";n("c5cb")}}]);