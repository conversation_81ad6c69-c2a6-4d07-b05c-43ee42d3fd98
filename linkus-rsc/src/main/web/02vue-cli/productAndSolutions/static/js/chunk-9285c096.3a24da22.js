(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9285c096"],{"63cf":function(e,t,i){"use strict";i("7309")},"6a7e":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"md_news_con"},[t("div",{staticClass:"md_top"},[t("div",{staticClass:"md_title"},[e._v(e._s(e.view.fileName)+" ")]),t("p",[e._v("发布时间："+e._s(e.view.uploadTime))])]),t("div",{staticClass:"markdown-body",domProps:{innerHTML:e._s(e.mdValue)}})])},a=[],d=i("40d6"),o=i("8ca4"),c=i("b2d8"),s=i("7c5c"),l={components:{mavonEditor:c["mavonEditor"]},data(){return{mdValue:"",fileId:"",view:{}}},watch:{$route:{handler(e,t){var i=this;i.fileId=this.$route.query.fileId,i.getViewInfo()}}},created(){var e=this;c["mavonEditor"].getMarkdownIt();e.fileId=this.$route.query.fileId,e.getViewInfo()},mounted(){},methods:{getViewInfo(){var e=this;Object(d["m"])({id:e.fileId,knowledge:!0}).then((function(t){e.view=t,e.mdValue=Object(s["marked"])(t.hyperText),Object(o["a"])("5e4b63acc56a424c33470601",e.fileId,t.fileBaseDefId),e.$nextTick((function(){for(var e=document.getElementsByClassName("permissionCheckMdWarp"),t=0;t<e.length;t++){let i=e[t];e[t].onclick=function(e){Object(o["e"])(i)}}}))}))}}},r=l,u=(i("63cf"),i("2877")),f=Object(u["a"])(r,n,a,!1,null,null,null);t["default"]=f.exports},7309:function(e,t,i){}}]);