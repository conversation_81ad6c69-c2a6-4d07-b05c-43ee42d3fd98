"use strict";(self["webpackChunkai_raise_effect"]=self["webpackChunkai_raise_effect"]||[]).push([[22],{2927:function(e,t,s){s.r(t),s.d(t,{default:function(){return v}});var a=function(){var e=this,t=e._self._c;return t("div",{ref:"wordsWrap",staticClass:"words_wrap",attrs:{id:"wordsWrap"}},[t("Header",{ref:"header",attrs:{id:"header"}}),t("div",{staticClass:"view_warp library_warp",attrs:{id:"wordsContent"}},[t("div",{staticClass:"view_warp_left tab_warp_p"},[t("div",{staticClass:"tab"},[t("ul",[t("li",{class:{active:"new"===e.tabType},on:{click:function(t){return e.getList("new")}}},[e._v("最新")]),t("li",{class:{active:"host"===e.tabType},on:{click:function(t){return e.getList("host")}}},[e._v("最热")])])]),t("div",{staticClass:"list pad24",staticStyle:{background:"#ffffff","margin-bottom":"10px"}},[t("ul",{ref:"listWarp",staticClass:"view_list_warp prd_hor_list"},e._l(e.list,(function(s,a){return t("li",{staticClass:"uploadView"},[t("div",{staticClass:"prd_hor_list_warp"},[t("p",{staticClass:"title",on:{click:function(t){return e.toView(s.id,s.kpType.cid)}}},[s.isOriginal?t("em",{staticClass:"original"},[e._v("原")]):e._e(),e._v(e._s(s.fileName))]),t("ul",{staticClass:"tag_list"},[e._l(s.tagList,(function(s,a){return a<5?t("li",[e._v(e._s(s))]):e._e()})),s.column?t("li",{staticClass:"zl"},[e._v("来自专栏："),t("em",[e._v(e._s(s.column.name))])]):e._e()],2),t("div",{staticClass:"info_b"},[t("div",{staticClass:"img_b",class:{has_img:!!s.imagesPath}},[s.imagesPath?t("img",{attrs:{src:s.imagesPath?"/"+s.imagesPath[0]:null,alt:""}}):e._e(),t("p",{staticClass:"desc"},[t("em",[e._v("作者："+e._s(s.fileAuthor))]),s.uploadDesc?t("em",[e._v(e._s(s.uploadDesc))]):e._e(),e._v(" "+e._s(s.text)+" ")])]),t("div",{staticClass:"file_b"},[t("div",{staticClass:"file_info_b"},[t("span",{staticClass:"up",class:{active:s.isFileUped},on:{click:function(t){return e.viewUp(s.id,a)}}},[t("i",{staticClass:"iconfont icon-like-solid"}),e._v("点赞 "+e._s(e.handleData("count",s.fileUpCount,999))+" ")]),t("span",[t("i",{staticClass:"iconfont icon-comment-solid"}),e._v("评论 "+e._s(e.handleData("count",s.fileRemarkCount,999))+" ")]),t("span",{staticClass:"view"},[t("i",{staticClass:"iconfont icon-check"}),e._v("浏览 "+e._s(e.handleData("count",s.fileViewCount,999))+" ")]),t("span",{staticClass:"copy cursor",on:{click:function(t){return e.copyFileContent(s.id,s.text)}}},[t("i",{staticClass:"iconfont icon-copy-solid"}),e._v("复制 "+e._s(e.handleData("count",s.copyCount,999))+" ")])]),t("div",{staticClass:"user_i"},[t("img",{attrs:{user_card_warp_hover:!0,user_id_card:s.uploadUser.userId,src:e.getPicUrl(s.uploadUser.userId),alt:""}}),t("em",[e._v(e._s(s.uploadUser.userName))]),e._v(" · "+e._s(s.lastModifyTime?s.lastModifyTime.split(" ")[0]:s.lastModifyTime)+" ")])])])])])})),0),0===e.list.length?t("div",{staticClass:"no_data"},[t("img",{staticClass:"bf30",attrs:{src:s(9430),alt:""}})]):e._e(),t("p",{ref:"listMore",staticClass:"list_more",class:{hasMore:!e.listBottom}},[e._v(" "+e._s(e.listBottom?"---- 已经到底了----":"---- 滚动加载更多 ----"))]),t("spin",{directives:[{name:"show",rawName:"v-show",value:e.listLoading,expression:"listLoading"}],attrs:{fix:""}})],1)]),t("div",{staticClass:"view_warp_right library_warp_right"},[t("div",{staticClass:"side_warp side_warp_tab"},[t("spin",{directives:[{name:"show",rawName:"v-show",value:e.tagLoading,expression:"tagLoading"}],attrs:{fix:""}}),t("div",{ref:"sideHeaderWarp",staticClass:"side_header"},[t("span",[e._v("热门标签 "),t("div",[t("Select",{directives:[{name:"focus",rawName:"v-focus"}],ref:"searchSelect",class:{active:e.selectSearch},attrs:{size:"small",transfer:"",id:"searchSelect",placeholder:"请输入搜索内容",clearable:"",filterable:"","remote-method":e.selectSearchSearch,loading:e.tagSearchLoading},on:{"on-select":e.searchTagChange},model:{value:e.tagKeyWords,callback:function(t){e.tagKeyWords=t},expression:"tagKeyWords"}},e._l(e.tagSearchList,(function(s,a){return t("i-option",{key:a,attrs:{value:s}},[e._v(e._s(s))])})),1),t("i",{staticClass:"iconfont icon-search",attrs:{slot:"suffix"},on:{click:e.selectSearchChange},slot:"suffix"})],1)]),e._m(0)]),t("ul",{staticClass:"tag_list"},e._l(e.hotTagList,(function(s){return t("li",{class:{active:e.listParam.tags.indexOf(s)>-1},staticStyle:{margin:"0 6px 6px 0"},on:{click:function(t){return e.selectTag(s)}}},[e._v(e._s(s))])})),0)],1),t("div",{staticClass:"side_warp"},[t("spin",{directives:[{name:"show",rawName:"v-show",value:e.remarkLoading,expression:"remarkLoading"}],attrs:{fix:""}}),e._m(1),t("ul",{staticClass:"remark_list remark_list_new"},e._l(e.remarkList,(function(s){return t("li",[t("div",{staticClass:"remark_info"},[t("div",[e._v(" "+e._s(s.fileRemark)+" ")])]),t("div",{staticClass:"remark_u_info"},[t("img",{staticStyle:{cursor:"default"},attrs:{user_card_warp_hover:!0,user_id_card:s.userId,src:s.userImg,alt:"#"}}),t("span",[e._v(e._s(s.userName))]),e._v(" "+e._s(s.remarkTime)+"评论了 "),t("span",{staticClass:"kptype",staticStyle:{color:"#666666"}},[e._v(e._s(s.kpType.name))]),t("span",{staticClass:"cursor",on:{click:function(t){return e.toView(s.fileId,s.kpType.cid)}}},[e._v("『"+e._s(s.fileName)+"』")])])])})),0)],1)])])],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"side_header_b label_4"},[t("div",{staticClass:"blue"}),t("div")])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"side_header"},[t("span",[e._v("最新评论")]),t("div",{staticClass:"side_header_b label_4"},[t("div",{staticClass:"green"}),t("div")])])}],r=(s(4114),s(8992),s(2577),s(3949),s(6587)),n=s.n(r),l=s(7823),o=s(2216),c=s(6824),A=s(8192),d={name:"words",components:{Header:A.A},data(){return{selectBaseId:"676e16112cd989ffc9185347",kpTypeId:{homePage:"678db5a98a13e55b77350ada",words:"678db6568a13e55b7735160b",course:"678db6918a13e55b77351960"},headPicUrl:"/linkus-sys-user/sysUserCtrl/getLoginNamePicByUerId.action?userId=",tabType:"new",list:[],listCount:0,onlyFocus:!1,listParam:{tags:[],tag:"",pageIndex:0,pageSize:20},listLoading:!1,listBottom:!1,hotTagList:[],tagLoading:!1,selectSearch:!1,tagKeyWords:"",tagSearchLoading:!1,tagSearchList:[],allTagList:[],remarkList:[],remarkLoading:!1,splitMdAndHtml:"splitMdAndHtml",lastScrollTop:70}},watch:{},created(){},mounted(){const e=this;e.$nextTick((()=>{e.$refs.wordsWrap.addEventListener("scroll",e.handleScroll)})),document.addEventListener("click",(function(t){if(e.$refs.sideHeaderWarp){let s=e.$refs.sideHeaderWarp.contains(t.target);s||(e.selectSearch=!1,e.tagKeyWords="")}})),e.getList("new"),e.queryLatestViewRemark(),e.queryHotTag()},beforeDestroy(){const e=this;e.$refs.wordsWrap&&e.$refs.wordsWrap.removeEventListener("scroll",e.handleScroll)},methods:{toView(e,t){const s=this;var a="";t===s.kpTypeId.homePage?a="homePage":t===s.kpTypeId.words&&(a="words");const i=s.$router.resolve({path:"/viewDetails",name:"viewDetails",query:{menuName:a,fileId:e}});window.open(i.href,"_blank")},getPicUrl(e){return"/linkus-sys-user/sysUserCtrl/getLoginNamePicByUerId.action?userId="+e},handleScroll(){const e=this,t=document.getElementById("wordsWrap");t.scrollTop+t.clientHeight+10>=t.scrollHeight&&!e.listBottom&&!e.listLoading&&e.getList(e.tabType,!0);const s=document.getElementById("wordsContent");var a=s.getBoundingClientRect().top;const i=document.getElementById("header"),r=n()("#header");a<e.lastScrollTop?a>-100?(r.css("top",69-a),r.css("backdrop-filter","blur(10px)"),r.css("background-color","rgb(255 255 255 / 65%)"),r.css("box-shadow","0 2px 12px #C6D5E5")):(i.style.removeProperty("backdrop-filter"),r.css("background-color","transparent"),i.style.removeProperty("box-shadow"),r.css("height",0)):(r.css("height",60),a<0?(r.css("top",69-a),r.css("backdrop-filter","blur(10px)"),r.css("background-color","rgb(255 255 255 / 65%)"),r.css("box-shadow","0 2px 12px #C6D5E5")):(r.css("top",0),i.style.removeProperty("backdrop-filter"),r.css("background-color","transparent"),i.style.removeProperty("box-shadow"))),e.lastScrollTop=a},getList(e,t){const s=this;s.tabType=e,t?s.listParam.pageIndex++:(s.list=[],s.listParam.pageIndex=0,s.listBottom=!1),s.listLoading=!0,"host"===s.tabType?s.queryHottestFiles():s.queryLatestFiles()},queryHottestFiles(){const e=this;e.listBottom||(e.selectBaseId&&(e.listParam["fileBaseDefId"]=e.selectBaseId,e.listParam["kpTypeId"]=e.kpTypeId.words),(0,l.zv)(e.listParam).then((function(t){t&&t.list?(e.list=e.list.concat(e.dataProcessing(t)),t.list.length<20&&(e.listBottom=!0)):e.listBottom=!0,e.listLoading=!1})))},queryLatestFiles(){var e=this;e.listBottom||(e.selectBaseId&&(e.listParam["fileBaseDefId"]=e.selectBaseId,e.listParam["kpTypeId"]=e.kpTypeId.words),(0,l.vu)(e.listParam).then((function(t){t&&t.list?(e.list=e.list.concat(e.dataProcessing(t)),t.list.length<20&&(e.listBottom=!0)):e.listBottom=!0,e.listLoading=!1})))},queryHotTag(){const e=this;var t={fileBaseDefId:e.selectBaseId,kpTypeId:e.kpTypeId.words};e.tagLoading=!0,(0,l.YE)(t).then((function(t){e.hotTagList=t,e.allTagList=t,e.tagLoading=!1}))},selectSearchSearch(e){const t=this;""!==e?(t.tagSearchList=[],t.allTagList.forEach((function(s){s.indexOf(e)>-1&&t.tagSearchList.push(s)}))):t.tagSearchList=[]},searchTagChange(e){const t=this;t.selectTag(e.value)},selectSearchChange(){const e=this;e.selectSearch=!e.selectSearch,n()("#searchSelect").find(".ivu-select-input").focus()},selectTag(e){const t=this;var s=t.listParam.tags.indexOf(e);s>-1?t.listParam.tags.splice(s,1):t.listParam.tags.push(e),t.listParam.tag=t.listParam.tags.toString(),t.getList(t.tabType)},dataProcessing(e){const t=this;for(var s=0;s<e.list.length;s++){e.list[s].userId=e.list[s].uploadUser?e.list[s].uploadUser.userId:"",e.list[s].imagesPath&&e.list[s].imagesPath.length>0&&(e.list[s].titlePic="/"+e.list[s].imagesPath[0]);var a=e.list[s].isMarkdown;e.list[s].text=a?t.hyperTextsConvert(e.list[s].hyperText):t.getText(e.list[s].hyperText)}return e.list},hyperTextsConvert(e){const t=this;if(!e)return;let s=(0,c.xI)(e);return t.getText(s)},getText(e){if(!e)return"";e=e.replace(/<(?:[^"'>]|(["'])[^"']*\1)*>/g,""),e=e.replace(/&lt;/g,"<"),e=e.replace(/&gt;/g,">");const t=new RegExp("&nbsp;","g");return e.replace(t,"")},htmlEncodeJQ(e){return n()("<span/>").text(e).html()},handleData(e,t,s){if("fileName"===e){var a=t.split(".");return a.pop(),a.join(".")}if("count"===e)return t=parseInt(t),t>s?s+"+":t},viewUp(e,t){const s=this;(0,l.Rw)({id:e,knowledge:!0}).then((function(e){s.list[t].fileUpCount=e.upCount,e.isAdd?(s.list[t].isFileUped=!0,s.$Message.success("点赞成功",3)):(s.list[t].isFileUped=!1,s.$Message.success("取消点赞成功",3))}))},queryLatestViewRemark(){const e=this;e.remarkLoading=!0,e.remarkList=[];var t={pageNum:0,pageSize:10,fileBaseDefId:e.selectBaseId};(0,l.jf)(t).then((function(t){e.remarkLoading=!1,t&&t.list&&t.list.forEach((function(t,s){var a={userImg:e.headPicUrl+t.userId,userName:t.userName,userId:t.userId,fileType:t.kpType?t.kpType.name:null,fileName:t.viewName,fileRemark:t.remark?t.remark.content:null,remarkTime:(0,o.ie)(new Date(t.addTime).getTime()),id:t.id,fileId:t.fileId,kpType:t.kpType};e.remarkList.push(a)}))}))},copyFileContent(e,t){const s=this;(0,l.mM)({fileId:e}).then((function(e){var a=document.createElement("textarea"),i=t;a.textContent=i,a.style.position="fixed",document.body.appendChild(a),a.select();try{document.execCommand("copy"),s.$Message.success({content:"复制成功",duration:3})}catch(r){return alert("复制失败"),!1}finally{document.body.removeChild(a)}s.getList(s.tabType)}))}}},f=d,g=s(1656),p=(0,g.A)(f,a,i,!1,null,null,null),v=p.exports},9430:function(e){e.exports="data:image/png;base64,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"}}]);