Date.prototype.format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1,                 //月份
        "d+": this.getDate(),                    //日
        "h+": this.getHours(),                   //小时
        "m+": this.getMinutes(),                 //分
        "s+": this.getSeconds(),                 //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds()             //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
};
/* 控制登录失效后页面跳转登录页 */
verifyLogin();
var vue = new Vue({
    el: '#main',
    data: function () {
        var sf = this;
        return {
            queryDataTotal: 0,
            pageNum: 0,
            pageSize: 50,
            pageSizeOpts: [50, 100, 200],

            tableDatas: [],
            tableLoading: false,
            tableHeight: 250,
            columns: [],
            userPrjInfoColumns: [
                {
                    title: '序',
                    width: 60,
                    type: 'index',
                },
                {
                    title: '导入人',
                    key: 'importUser',
                    width: 120,
                },
                {
                    title: '数据时间',
                    key: 'importTime',
                    width: 130,
                },
                {
                    title: '数据来源',
                    key: 'dataFrom',
                    width: 130,
                },
                {
                    title: '数据状态',
                    key: 'dataStatus',
                    width: 120,
                },
                {
                    title: '姓名/NT',
                    key: 'userLoginName',
                    width: 120,
                },
                {
                    title: '工作角色',
                    key: 'roleName',
                    width: 100,
                },
                {
                    title: '项目编码/名称',
                    key: 'prjCodeName',
                    width: 160,
                    render: function (h, params) {
                        var name = params.row.prjCodeName;
                        if (name) {
                            return h('Tooltip', {
                                props: {placement: 'top', transfer: true}
                            }, [
                                name,
                                h('span', {
                                        slot: 'content',
                                        style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                    },
                                name)
                            ]);
                        }
                        return null;
                    },
                },
                {
                    title: '指标年月',
                    key: 'ym',
                    width: 100,
                },
            ],
            userInfoColumns: [
                {
                    title: '中心架构',
                    key: 'centerOrg',
                    width: 120,
                },
                {
                    title: '一级部门',
                    key: 'firstDept',
                    width: 120,
                },
                {
                    title: '二级部门',
                    key: 'secondDept',
                    width: 140,
                },
                {
                    title: '子族群',
                    key: 'userGroup',
                    width: 120,
                },
                {
                    title: '岗位',
                    key: 'userRole',
                    width: 120,
                },
                {
                    title: 'CC',
                    key: 'userCc',
                    width: 120,
                },
                {
                    title: '类型',
                    key: 'userType',
                    width: 100,
                },
                {
                    title: '度量角色',
                    key: 'pfmRole',
                    width: 100,
                },
                {
                    title: '是否度量',
                    key: 'isPfm',
                    width: 90,
                },
            ],

            detailTitle: "",
            detailModal: false,

            detailTableTotal: 0,
            detailPageIndex: 0,
            detailPageSize: 10,
            detailPageSizeOpts: [10, 20, 50],

            detailTableHeight: 250,
            detailTableLoading: false,
            detailTableDatas: [],
            detailColumns: [],

            dateOptions: {
                disabledDate(date) {
                    if (!!sf.startDate && !!sf.endDate) {
                        if (new Date(date).getTime() < new Date(sf.startDate).getTime()
                            || new Date(date).getTime() > new Date(sf.endDate).getTime()) {
                            return true;
                        }
                    }
                    return false;
                }
            },

            selectPrjId: null,
            selectDatePeriod: [],
            searchPrjList: [],

            isShowHead: true,

            startDate: "",
            endDate: "",

            provList: [],
            provTreeData: [],
            provFltList: [],
            prjMgtTypeList: [],
            prjStatusList: [],

            selectProv: null,
            selectPrjMgtTypes: [],
            selectCCId: "",
            selectYear: null,
            sbuId: "",

            queryPrjAjax: null,

            indicatorType: "",
            searchType: "",
            userIds: [],

            currentName: 'planEmp',
            selectPrjIds: [],

            currentPrjId: "",
            // 新增
            empWord: "",
            selectMonth: "",
            selectedPrdId: "",
            selectedPrdCtlgLogIds: [],
            reMetricsModal:false,
            reMetricsColumns: [],
            reMetricsDatas: [
                {

                }
            ],
            reMetricsLoading: false,
            reMetricsTableHeight: 300,
            selectedRows: [],
            userList: [],
            userLoading: false,
            selectUsers: null,
            importUser: null,
            isExpand :false,
            ccCodeList: [],
            exportAble: false,
            supplyAble: false,
            lastMonth: "",
            supplyShowFlag : false, // 重新审批按钮显示 只有上一个月才显示
            uploadModal : false,
            importGroupUserUrl: "",
            headerDatas: [], // 动态表头数据,
            selectImportMonth: null,

			selectStd: '已生效(含DMP-已审批和非DMP-已审批)',
            stdList: [
                {
                    id: '已生效(含DMP-已审批和非DMP-已审批)',
                    defName: '已生效(含DMP-已审批和非DMP-已审批)',
                },
                {
                    id: '非DMP-未审批',
                    defName: '非DMP-未审批',
                },
                {
                    id: '非DMP-已审批',
                    defName: '非DMP-已审批',
                },
                {
                    id: 'DMP-已审批',
                    defName: 'DMP-已审批',
                },
            ],
            selectDataOrigin: "",
            dataOriginList: [
                /*{
                    id: 'DMP',
                    defName: 'DMP',
                },
                {
                    id: '非DMP',
                    defName: '非DMP',
                },*/
                {
                    id: '总数(含明细)',
                    defName: '总数(含明细)',
                },
                {
                    id: '总数(不含明细)',
                    defName: '总数(不含明细)',
                },
            ],
            roleId : "",

            planEmpRoleId: "6879f069c935b27066256b6f", //"规划人员"角色id
            devUserRoleId: "6114e83e952372e6ad1bdd89", //"开发人员"角色id
            testUserRoleId: "6114e7e2952372e6ad1b6b68", //"测试人员"角色id
            reqUserRoleId: "6114e861952372e6ad1c1730", //"需求人员"角色id
            omUserRoleId: "676e6b622cd989ffc9238d22", //"运维人员"角色id
            otherUserRoleId: "676e6b992cd989ffc9239470", //"其他人员"角色id

            isAllSelect: true,
            isNotInited: false,

            downloadHref: linkus.location.km + '/kmFileCtrl/downloadByName.action?fileName=' + encodeURI("项目基础指标导入模板.xlsx"),
            initPrdCtlgIds: [],

            queryPrjLoading: false,
        };
    },

    created: function () {
        var sf = this;
        /*sf.getOrgList();*/
        if (!Vue.evtHub) {
            Vue.evtHub = new Vue();
        }

        sf.selectMonth = new Date(sf.getUpMonth());

        var urlName = sf.getQueryString("from");
        if (urlName == "bscPrjEffectEvaluationNew") {
            sf.isShowHead = false;
        }

        var condParam = sf.getQueryString("condParam");
        if (!!condParam && condParam != "{}") {
            sf.isNotInited = true;
            var param = JSON.parse(condParam);
            if(!!param["selectPrjId"]) {
                sf.selectPrjId = param["selectPrjId"];
            }
            if(!!param["selectMonth"]) {
                sf.selectMonth = param["selectMonth"];
            }
            if(!!param["selectStd"]) {
                sf.selectStd = param["selectStd"];
            }
            if(!!param["selectDataOrigin"]) {
                sf.selectDataOrigin = param["selectDataOrigin"];
            }
            sf.isAllSelect = param["isAllSelect"];
        }

        sf.loadCurrentUser();
        sf.queryPrjInfo();
        sf.importGroupUserUrl = linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/importPfmIndex';
    },

    mounted: function () {
        var sf = this;
        sf.detailTableHeight = window.innerHeight - 504;
        sf.detailTableHeight = sf.detailTableHeight > 250 ? sf.detailTableHeight : 250;

        sf.tableHeight = window.innerHeight - 272;
        sf.getCurrentYear();
        Vue.evtHub.$on("change-ctlgs", function(data) {
            sf.selectedPrdCtlgIds = data.selectedCtlgs;
            sf.pageNum = 0;
            sf.listCCCode();
            if(!!sf.isNotInited) {
                sf.queryData();
            }
        });
    },

    methods: {

        //查询组织树
        getOrgList: function() {
            var sf = this;
            sf.initPrdCtlgIds = [];
            $.ajax({
                url: linkus.location.report + '/bscEffectTeamManage/getEipOrgDept',
                type: 'post',
                dataType: "json",
                data: {
                    nonTeamMenu: true,
                    isNoAuth: false,
                },
                success: function(data) {
                    if(!!data && JSON.stringify(data) != "{}") {
                        if(!!data.success) {
                            var ctlgTree = data.data || [];
                            sf.recursion(ctlgTree);
                        }
                    }
                },
                error: function(data) {
                }
            });
        },

        //递归查询组织树
        recursion: function(trees) {
            var sf = this;
            (trees || []).forEach(function (item) {
                sf.initPrdCtlgIds.push(item["value"]);
                if (!!item.children && item.children.length > 0) {
                    sf.recursion(item.children);
                }
            });
        },

        //获取上个月份
        getUpMonth: function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();

            var year2 = year;
            // 注意：1月份返回的是0
            var month2 = parseInt(month);
            if (month2 == 0) {
                year2 = parseInt(year2) - 1;
                month2 = 12;
            }

            if (month2 < 10) {
                month2 = '0' + month2;
            }
            var m = year2.toString();
            var n = month2.toString();
            var t2 = m + "-" + n;
            // 最终返回格式2022-12
            return t2;
        },

        //选择项目集
        onPrjChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({"condChanged": true, "condParam": {
                    "selectPrjId": prjId,
                }
            }, '*');
            if (!sf.selectPrjId) {
                sf.queryPrjInfo();
                return;
            }
        },

        // 查询项目集信息
        queryPrjInfo: function (keyword) {
            var sf = this;
            var vo = {};
            vo["prjNameOrCode"] = "";
            if(!!keyword && !!keyword.trim()) {
                if(!!sf.selectPrjId && sf.selectPrjId == keyword.trim()) {
                    return;
                }
                vo["prjNameOrCode"] = keyword.trim();
            }
            if (sf.queryPrjAjax && sf.queryPrjAjax.abort) {
                sf.queryPrjAjax.abort();
            }
            sf.searchPrjList = [];
            sf.queryPrjLoading = true;
            sf.queryPrjAjax = $.ajax({
                type: 'post',
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/listPrjInfoByIds",
                data: JSON.stringify(vo),
                dataType: "json",
            });
            sf.queryPrjAjax.then(function (data) {
                sf.queryPrjLoading = false;
                if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                    //sf.searchPrjList = data.data || [];
                    if(data.data.length > 0){
                        let keyWord = keyword || '';
                        sf.searchPrjList = data.data.map(function(item){
                            let item0 = {
                                cid: item.cid + '-' + keyWord,
                                codeName: item.codeName,
                                name: item.name
                            }
                            return item0;
                        })
                    }
                }
            }, function (data) {
                sf.queryPrjLoading = false;
                if (!data || JSON.stringify(data) == "{}" || !data.statusText || data.statusText != "abort") {
                    sf.$Message.error({
                        content: "查询项目集错误，请联系管理员！",
                        duration: 5
                    });
                }
            });
        },

        /* 分页选择页码 */
        onPageNumChange: function(pageNum) {
            var sf = this;
            sf.pageNum = pageNum - 1;
            sf.queryData();
        },

        /* 分页选择每页条数 */
        onPageSizeChange: function(pageSize) {
            var sf = this;
            sf.pageNum = 0;
            sf.pageSize = pageSize;
            sf.queryData();
        },

        handleSelectionChange: function (val) {
            var sf = this;
            // 不加slice方法 就是引用同一数组对象 修改时不能保持独立
            sf.selectedRows = val.slice();
            sf.reMetricsDatas = val.slice();
        },
        //点击某一行
        selectRow: function(data, index) {
            var sf = this;
        },

        //组装表格列
        generateColumns: function(displayColList) {
            var sf = this;
            var getWidth = function(length) {
                var lengthWidth = 30 * length;
                if(lengthWidth < 82) {
                    return 82;
                }else if(lengthWidth > 200) {
                    return 200;
                }else{
                    return lengthWidth
                }
            }
            sf.columns = [];
            var indexColumns = [];
            if(!!displayColList && displayColList.length > 0) {
                for(var i = 0; i < displayColList.length; i++) {
                    var item = displayColList[i];
                    var col = {};
                    col["title"] = item["name"];
                    col["align"] = "center";
                    var children = [];
                    children.push({
                        "title": item["codeName"],
                        "align": "left",
                        "key": item["codeName"],
                        "width": getWidth(item["codeName"].length),
                        render: function (h, params) {
                            var indexScore = params.row["indexScore"];
                            if (!!indexScore && JSON.stringify(indexScore) != "{}") {
                                return h("span", indexScore[params.column.key]);
                            }
                            return null;
                        }
                    });
                    col["children"] = children;
                    indexColumns.push(col);
                }
            }
            sf.columns = sf.userPrjInfoColumns.concat(indexColumns).concat(sf.userInfoColumns);
        },

        //tab页切换
        tabChange: function(name) {
            var sf = this;
            sf.currentName = name;
            sf.queryDataAndHeader();
        },

        //传值给父页面展开/收起左侧栏
        route: function() {
            var sf = this;
            window.parent.postMessage({"expandOrShrink": true}, '*');
        },

        /*导出报表*/
        exportData: function () {
            var sf = this;
            if(!sf.selectMonth) {
                sf.$Message.warning({
                    content: "月份不能为空！",
                    duration: 3
                })
                return;
            }
            var ctlgIds = [];
            for (var index in sf.selectedPrdCtlgIds) {
                ctlgIds.push(sf.selectedPrdCtlgIds[index].id);
            }
            if(!sf.selectPrjId && ctlgIds.length == 0 && (!sf.importUser || !sf.importUser["userId"])) {
                sf.$Message.warning({
                    content: "导入人、项目集、部门至少选择一项！",
                    duration: 3
                });
                return;
            }
            if(!sf.selectStd) {
                sf.$Message.warning({
                    content: "请选择数据状态！",
                    duration: 3
                });
                return;
            }
            /*if(ctlgIds.length == 0) {
                ctlgIds = sf.initPrdCtlgIds.concat([]);
                if(!sf.selectPrjId) {
                    sf.$Message.warning({
                        content: "项目集不能为空！",
                        duration: 3
                    });
                    return;
                }
            }*/
            if (sf.currentName === 'demandEmp') { //需求人员
                sf.roleId = sf.reqUserRoleId;
            } else if(sf.currentName === 'devEmp') { //开发人员
                sf.roleId = sf.devUserRoleId;
            } else if(sf.currentName === 'testEmp'){ // 测试人员
                sf.roleId = sf.testUserRoleId;
            } else if(sf.currentName === 'operationEmp'){ //运维人员
                sf.roleId = sf.omUserRoleId;
            }else if(sf.currentName === 'otherEmp'){ //其他
                sf.roleId = sf.otherUserRoleId;
            }else if(sf.currentName === 'planEmp'){ //规划人员
                sf.roleId = sf.planEmpRoleId;
            }
            var vo = {
                roleId: sf.roleId,
                ym: !!sf.selectMonth ? new Date(sf.selectMonth).format("yyyy-MM") : "",
                deptIds: ctlgIds,
                prjSetIds: !!sf.selectPrjId ? [sf.selectPrjId.split('-')[0]] : [],
                ccList: !!sf.selectCCId ? [sf.selectCCId] : [],
                userId: !!sf.selectUsers ? sf.selectUsers.userId : null,
                importUserId: !!sf.importUser ? sf.importUser.userId : null,
                dataStatus: sf.selectStd,
                isExport: true,
            }

            if(!!sf.selectStd && sf.selectStd == '已生效(含DMP-已审批和非DMP-已审批)') {
                vo["dataFrom"] = sf.selectDataOrigin;
            }

            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 4
            });

            var xhr = new XMLHttpRequest();
            var url = linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/exportMetaPfmIndex';
            xhr.open('POST', url, true);
            xhr.responseType = 'blob'; // 设置响应类型为 Blob
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(JSON.stringify(vo));
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    var blob = xhr.response; // 获取 Blob 数据
                    sf.blobDataExport(blob, "基础指标数据" + '.xlsx');
                } else {
                    sf.$Message.error({
                        content: "导出基础指标数据失败，请联系管理员！",
                        duration: 8
                    });
                }
            };
            xhr.onerror = function() {
                sf.$Message.error({
                    content: "导出基础指标数据失败，请联系管理员！",
                    duration: 8
                });
            };
        },

        // blob数据导出
        blobDataExport: function(blob, fileName) {
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        },

        // 查询服务器时间 来判断是否展示申请按钮
        getCurrentYear:function (){
            var sf = this;
            $.ajax({
                type:'get',
                url:linkus.location.abp + '/index/queryServerTime',
                success:function(res) {
                    if (res.success){
                        var data = res.data;
                        sf.lastMonth = data.lastMonth;
                    }else{
                        sf.$Message.warning(res.message);
                    }
                },
                error:function(res){

                }
            });
        },

        //获取当前登录人
        loadCurrentUser: function () {
            var sf = this;
            $.ajax({
                url: linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
                type: 'post',
                dataType: 'JSON',
            }).done(function (data) {
                sf.sbuId = data.sbuId;
                // sf.listPrjProv();
                // sf.listPrjMgtType()
            });
        },

        /*选择归属年份*/
        yearChanged: function () {
            var sf = this;
        },

        /*效能度量-省份选择下拉列表*/
        listPrjProv: function () {
            var sf = this;
            sf.provTreeData = [];
            $.ajax({
                url: linkus.location.abp + '/v1/findProvince',
                type: 'get',
                data: {
                    buCode: sf.sbuId
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}") {
                        if (!!data.success) {
                            sf.provList = data.data || [];
                            sf.transToProvTreeData();
                        } else {
                            sf.$Message.error({
                                content: data.message || '查询省份列表失败，请联系管理员！',
                                duration: 6
                            });
                        }
                    }
                },
                error: function (data) {
                    if (!!data && !!data.responseText && !!JSON.parse(data.responseText)) {
                        sf.$Message.error({
                            content: JSON.parse(data.responseText).errorMessage || '查询省份列表失败，请联系管理员！',
                            duration: 6
                        });
                    } else {
                        sf.$Message.error({
                            content: '查询省份列表失败，请联系管理员！',
                            duration: 6
                        });
                    }
                }
            });
        },

        // 将省份信息转换成树形结构
        transToProvTreeData: function () {
            var sf = this;
            var provList = sf.provList;

            // 删除所有的children,以防止多次调用
            provList.forEach(function (item) {
                delete item.children;
            });
            var map = {}; //构建map
            provList.forEach(prov => {
                map[prov.id] = {
                    'value': prov.id,
                    'label': prov.defName
                };
                map[prov.cndtItems[0].cid] = {
                    'value': prov.cndtItems[0].cid,
                    'label': prov.cndtItems[0].name,
                    'children': []
                };
                map[prov.cndtItems[1].cid] = {
                    'value': prov.cndtItems[1].cid,
                    'label': prov.cndtItems[1].name,
                    'children': []
                }
            });
            var treeData = [];
            provList.forEach(child => {
                var areaId = child.cndtItems[1].cid;
                var bigAreaId = child.cndtItems[0].cid;

                var prov = map[child.id];
                var area = map[areaId];
                var bigArea = map[bigAreaId];
                // 去重
                if (!JSON.stringify(area.children).includes(JSON.stringify(prov))) {
                    area.children.push(prov);
                }
                if (!JSON.stringify(bigArea.children).includes(JSON.stringify(area))) {
                    bigArea.children.push(area);
                }
                if (!JSON.stringify(treeData).includes(JSON.stringify(bigArea))) {
                    treeData.push(bigArea);
                }
            })
            sf.provTreeData = treeData;
        },

        /*查询cc列表*/
        listCCCode: function () {
            var sf = this;
            var ctlgIds = [];
            for (var index in sf.selectedPrdCtlgIds) {
                ctlgIds.push(sf.selectedPrdCtlgIds[index].id);
            }
            /*if(ctlgIds.length == 0) {
                ctlgIds = sf.initPrdCtlgIds.concat([]);
            }*/
            if (!ctlgIds || ctlgIds.length < 1) {
                return;
            }
            sf.ccCodeList = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectTeamManage/getBuCc",
                dataType: "json",
                data: JSON.stringify(ctlgIds),
                type: 'post',
                headers : {'Content-Type' : 'application/json;charset=utf-8'},
                success: function (data) {
                    sf.ccCodeList = data.data || [];
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询管cc列表错误，请联系管理员！",
                        duration: 6
                    });
                }
            });
        },

        /*获取参数*/
        getQueryString: function (name) {
            // 获取参数
            var url = window.location.search;
            // 正则筛选地址栏
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            // 匹配目标参数
            var result = url.substr(1).match(reg);
            //返回参数值
            return result ? decodeURIComponent(result[2]) : null;
        },

        //查询
        queryData: function () {
            var sf = this;
            if(!sf.selectMonth) {
                sf.$Message.warning({
                    content: "月份不能为空！",
                    duration: 3
                })
                return;
            }
            var ctlgIds = [];
            for (var index in sf.selectedPrdCtlgIds) {
                ctlgIds.push(sf.selectedPrdCtlgIds[index].id);
            }
            if(!sf.selectPrjId && ctlgIds.length == 0 && (!sf.importUser || !sf.importUser["userId"])) {
                sf.$Message.warning({
                    content: "导入人、项目集、部门至少选择一项！",
                    duration: 3
                });
                return;
            }
            if(!sf.selectStd) {
                sf.$Message.warning({
                    content: "请选择数据状态！",
                    duration: 3
                });
                return;
            }
            /*if(ctlgIds.length == 0) {
                ctlgIds = sf.initPrdCtlgIds.concat([]);
                if(!sf.selectPrjId) {
                    sf.$Message.warning({
                        content: "项目集不能为空！",
                        duration: 3
                    });
                    return;
                }
            }*/
            sf.isNotInited = false;
            sf.tableDatas = [];
            sf.queryDataTotal = 0;
            sf.exportAble = false;
            if (sf.currentName === 'demandEmp') { //需求人员
                sf.roleId = sf.reqUserRoleId;
            } else if(sf.currentName === 'devEmp') { //开发人员
                sf.roleId = sf.devUserRoleId;
            } else if(sf.currentName === 'testEmp'){ // 测试人员
                sf.roleId = sf.testUserRoleId;
            } else if(sf.currentName === 'operationEmp'){ //运维人员
                sf.roleId = sf.omUserRoleId;
            }else if(sf.currentName === 'otherEmp'){ //其他
                sf.roleId = sf.otherUserRoleId;
            }else if(sf.currentName === 'planEmp'){ //规划人员
                sf.roleId = sf.planEmpRoleId;
            }
            sf.tableLoading = true;
            var vo = {
                roleId: sf.roleId,
                ym: !!sf.selectMonth ? new Date(sf.selectMonth).format("yyyy-MM") : "",
                deptIds: ctlgIds,
                prjSetIds: !!sf.selectPrjId ? [sf.selectPrjId.split('-')[0]] : [],
                ccList: !!sf.selectCCId ? [sf.selectCCId] : [],
                userId: !!sf.selectUsers ? sf.selectUsers.userId : null,
                importUserId: !!sf.importUser ? sf.importUser.userId : null,
                dataStatus: sf.selectStd,
                pageNum: sf.pageNum,
                pageSize: sf.pageSize,
                isExport: false,
            }
            if(!!sf.selectStd && sf.selectStd == '已生效(含DMP-已审批和非DMP-已审批)') {
                vo["dataFrom"] = sf.selectDataOrigin;
            }
            $.ajax({
                url: linkus.location.rsc + "/linkus-pfm/pfmIndexCtrl/queryMetaPfmIndex",
                type: 'post',
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (res) {
                    sf.tableLoading = false;
                    if (!!res.success) {
                        sf.tableDatas = (!!res.data.pageBean && JSON.stringify(res.data.pageBean) != "{}") ? (res.data.pageBean.objectList || []) : [];
                        var displayColList = res.data.indexList || [];
                        sf.generateColumns(displayColList);
                        if (sf.tableDatas.length != 0) {
                            sf.exportAble = true;
                        }
                        sf.queryDataTotal = (!!res.data.pageBean && JSON.stringify(res.data.pageBean) != "{}") ? (res.data.pageBean.count || 0) : 0;
                    }else {
                        sf.$Message.error({
                            content: res.message || "查询基础指标数据错误，请联系管理员！",
                            duration: 5
                        });
                    }
                },
                error: function (data) {
                    sf.tableLoading = false;
                    sf.$Message.error({
                        content: "查询基础指标数据错误，请联系管理员！",
                        duration: 5
                    });
                }
            });
        },


        /* 导入成功 */
        importSuccess: function(result) {
            var sf = this;
            sf.$Spin.hide();
            sf.uploadModal = false;
            if(!!result.data && JSON.stringify(result.data) != "{}") {
                if(!!result.data.result) {
                    sf.$Message.success({
                        content: "导入成功,即将刷新页面数据!",
                        duration: 5
                    });
                    sf.queryData();
                }else {
                    sf.$Message.error({
                        content: result.data.msg,
                        duration: 5
                    });
                }
            }else {
                sf.$Message.error({
                    content: "导入失败，请联系管理员！",
                    duration: 5
                });
            }
        },
        /* 导入前 */
        beforeImport : function() {
            var sf = this;
            sf.$Spin.show();
        },
        /* 导入失败 */
        importFail : function(result) {
            var sf = this;
            sf.$Spin.hide();
            sf.uploadModal = false;
            sf.$Message.error({
                content: "导入失败，请联系管理员！",
                duration: 5
            });
        },

        // 查询表头
        queryTableHeader : function(callFunc) {
            var sf = this;
            sf.exportAble = false;
            if (sf.currentName === 'demandEmp') { //需求人员
                sf.roleId = sf.reqUserRoleId;
            } else if(sf.currentName === 'devEmp') { //开发人员
                sf.roleId = sf.devUserRoleId;
            } else if(sf.currentName === 'testEmp'){ // 测试人员
                sf.roleId = sf.testUserRoleId;
            } else if(sf.currentName === 'operationEmp'){ //运维人员
                sf.roleId = sf.omUserRoleId;
            }else if(sf.currentName === 'otherEmp'){ //其他
                sf.roleId = sf.otherUserRoleId;
            }else if(sf.currentName === 'planEmp'){ //规划人员
                sf.roleId = sf.planEmpRoleId;
            }
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/getColByPageId',
                type: 'get',
                data: {
                    pageId: sf.roleId,
                },
                dataType: "json",
                success: function (resultData) {
                    if (callFunc) {
                        callFunc(resultData || []);
                    }
                }
            });
        },

        queryDataAndHeader: function() {
            var sf = this;
            sf.pageNum = 0;
            sf.queryData();
        }

    },

});
