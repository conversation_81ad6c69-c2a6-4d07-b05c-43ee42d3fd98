# Spring Cloud Gateway 访问日志系统

<div align="center">

![Spring Cloud Gateway](https://img.shields.io/badge/Spring%20Cloud%20Gateway-3.x-green)
![Java](https://img.shields.io/badge/Java-8+-blue)
![MongoDB](https://img.shields.io/badge/MongoDB-4.x+-orange)
![SkyWalking](https://img.shields.io/badge/SkyWalking-8.x+-purple)

**为 Spring Cloud Gateway 提供完整的访问日志记录功能，支持控制台日志输出和MongoDB数据存储，具备灵活的配置控制和智能过滤机制。**

[快速开始](#-快速开始) • [配置详解](#️-配置详解) • [使用场景](#-使用场景) • [常见查询](#-常见查询)

</div>

---

## 📋 目录

- [概述](#-概述)
- [核心特性](#-核心特性)
- [快速开始](#-快速开始)
- [日志格式](#-日志格式)
- [存储管理](#-存储管理)
- [配置详解](#️-配置详解)
- [使用场景](#-使用场景)
- [常见查询](#-常见查询)
- [注意事项](#️-注意事项)
- [故障排除](#️-故障排除)
- [扩展功能](#-扩展功能)

## 📖 概述

Spring Cloud Gateway 访问日志系统是一个高性能、可扩展的日志记录解决方案，专为微服务架构设计。系统提供完整的请求-响应生命周期记录，支持多种输出方式，并具备智能过滤和安全脱敏功能。

### 🎯 设计目标

- **完整性**：记录请求-响应的完整生命周期信息
- **高性能**：最小化对网关性能的影响
- **可扩展**：支持多种存储方式和自定义扩展
- **安全性**：自动脱敏敏感信息，保护数据安全
- **易用性**：提供灵活的配置选项和丰富的查询功能

## ✨ 核心特性

### 🔍 完整信息记录

| 类别 | 记录内容 | 示例 |
|------|----------|------|
| **请求信息** | HTTP方法、URL、客户端IP、User-Agent、Referer、登录用户 | `GET /api/users?page=1` |
| **响应信息** | 状态码、处理时间、响应时间戳 | `200 OK, 150ms` |
| **追踪信息** | 请求ID、SkyWalking链路追踪 | `traceId=abc123, segmentId=def456` |
| **安全信息** | 敏感数据自动脱敏 | `password=***, Authorization=***` |

### 🎯 智能过滤机制

```yaml
# 路径过滤 - 支持Ant路径匹配
exclude-paths:
  - "/linkusMonitor/**"           # 监控端点
  - "/linkus-*/linkusMonitor/**"  # 微服务监控端点
  - "/health/**"                  # 健康检查

# 静态文件过滤 - 正则表达式识别
static-file-pattern: "\\.(css|js|png|jpg|jpeg|gif|bmp|ico|svg|woff|woff2|ttf|eot|otf)$"

# 方法过滤 - 排除特定HTTP方法
exclude-methods:
  - "OPTIONS"
  - "HEAD"

# 状态码过滤 - 排除错误状态码
exclude-status-codes:
  - 404
  - 405
```

### 🔧 灵活配置控制

- **功能开关**：独立控制访问日志、控制台输出、MongoDB存储
- **微服务支持**：支持`linkus-`前缀的context-path架构
- **SkyWalking集成**：可配置是否使用SkyWalking traceId作为请求ID
- **环境适配**：针对开发、测试、生产环境提供不同配置模板

## 🚀 快速开始

### 1. 添加配置

在 `application.yml` 中添加配置：

```yaml
spring:
  cloud:
    gateway:
      access-log:
        # 基础配置
        enabled: true
        use-sky-walking-trace-id: true
        
        # 日志输出控制
        enable-console-log: true
        enable-mongo-storage: true
        
        # 排除规则
        exclude-paths:
          - "/linkusMonitor/**"
          - "/linkus-*/linkusMonitor/**"
          - "/health/**"
        
        # 静态文件正则匹配
        static-file-pattern: "\\.(css|js|png|jpg|jpeg|gif|bmp|ico|svg|woff|woff2|ttf|eot|otf)$"
        
        # 敏感信息脱敏
        sensitive-headers:
          - "Authorization"
          - "Cookie"
          - "JWT-TOKEN"
        sensitive-params:
          - "password"
          - "token"
          - "secret"
```

### 2. 查看日志

#### 控制台日志查看

```bash
# 实时查看访问日志
tail -f logs/linkus-gateway/access.log

# 查看特定请求ID
grep "RequestId=abc123" logs/linkus-gateway/access.log

# 查看错误请求
grep "StatusCode=5" logs/linkus-gateway/access.log

# 查看慢请求（超过1秒）
grep "Duration=[0-9]\{4,\}ms" logs/linkus-gateway/access.log
```

#### MongoDB查询

```javascript
// 查询特定请求
db.tssAccessLog_202401.findOne({requestId: "abc123"})

// 时间范围查询
db.tssAccessLog_202401.find({
  timestamp: {
    $gte: new Date("2024-01-01"),
    $lte: new Date("2024-01-02")
  }
})

// 错误请求统计
db.tssAccessLog_202401.count({statusCode: {$gte: 400}})

// 按小时统计访问量
db.tssAccessLog_202401.aggregate([
  {
    $group: {
      _id: {
        year: {$year: "$timestamp"},
        month: {$month: "$timestamp"},
        day: {$dayOfMonth: "$timestamp"},
        hour: {$hour: "$timestamp"}
      },
      count: {$sum: 1}
    }
  },
  {$sort: {"_id": 1}}
])
```

## 📊 日志格式

### 控制台日志格式

```
ACCESS_LOG RequestId=abc123, Method=GET, Domain=http://example.com, Path=/api/users, Query=page=1&size=10, ClientIP=***********, UserAgent=Mozilla/5.0, LoginName=admin, Referer=http://example.com, StatusCode=200, Duration=150ms, SkyWalkingTraceId=abc123, Timestamp=2024-01-01 12:00:00.150
```

### MongoDB文档结构

```json
{
  "_id": "ObjectId",
  "requestId": "abc123",
  "method": "GET",
  "requestUri": {
    "scheme": "http",
    "host": "example.com",
    "port": 8080,
    "path": "/api/users",
    "query": "page=1&size=10"
  },
  "clientIp": "***********",
  "userAgent": "Mozilla/5.0",
  "loginUser": {
    "userId": "507f1f77bcf86cd799439011",
    "loginName": "admin",
    "userName": "管理员",
    "jobCode": "EMP001"
  },
  "referer": "http://example.com",
  "statusCode": 200,
  "duration": 150,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "skyWalkingInfo": {
    "skyWalkingTraceId": "abc123",
    "skyWalkingSegmentId": "def456",
    "skyWalkingSpanId": "ghi789"
  },
  "sensitiveHeaders": "Authorization=***, Cookie=***",
  "sensitiveParams": "password=***, token=***"
}
```

### 📋 数据结构说明

#### TeSysAccessLog 主实体
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `_id` | ObjectId | MongoDB文档ID | `507f1f77bcf86cd799439011` |
| `requestId` | String | 请求唯一标识 | `abc123` |
| `method` | String | HTTP方法 | `GET`、`POST` |
| `requestUri` | TeUri | 请求URI信息 | 见下方说明 |
| `clientIp` | String | 客户端IP地址 | `***********` |
| `userAgent` | String | 用户代理字符串 | `Mozilla/5.0` |
| `loginUser` | TeUser | 登录用户信息 | 见下方说明 |
| `referer` | String | 引用页面 | `http://example.com` |
| `statusCode` | Integer | HTTP状态码 | `200`、`404` |
| `duration` | Long | 请求处理时间（毫秒） | `150` |
| `timestamp` | Date | 请求时间戳 | `2024-01-01T12:00:00.000Z` |
| `skyWalkingInfo` | TeSkyWalkingInfo | 链路追踪信息 | 见下方说明 |
| `sensitiveHeaders` | String | 脱敏的敏感请求头 | `Authorization=***` |
| `sensitiveParams` | String | 脱敏的敏感参数 | `password=***` |

#### TeUri 请求URI子实体
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `scheme` | String | 协议方案 | `http`、`https` |
| `host` | String | 主机名 | `example.com` |
| `port` | Integer | 端口号 | `8080` |
| `path` | String | 请求路径 | `/api/users` |
| `query` | String | 查询参数（已脱敏） | `page=1&size=10` |

#### TeUser 用户信息子实体
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `userId` | String | 用户ID | `507f1f77bcf86cd799439011` |
| `loginName` | String | 登录名 | `admin` |
| `userName` | String | 用户姓名 | `管理员` |
| `jobCode` | String | 工号 | `EMP001` |

#### TeSkyWalkingInfo 链路追踪子实体
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `skyWalkingTraceId` | String | SkyWalking TraceId | `abc123` |
| `skyWalkingSegmentId` | String | SkyWalking SegmentId | `def456` |
| `skyWalkingSpanId` | String | SkyWalking SpanId | `ghi789` |

## 💾 存储管理

### 📅 按月分表策略

| 特性 | 说明 |
|------|------|
| **表命名规则** | `tssAccessLog_YYYYMM`（如：`tssAccessLog_202401`） |
| **自动分表** | 系统根据访问日志的时间戳自动存储到对应月份的表 |
| **查询支持** | 支持跨月份查询和历史数据检索 |
| **索引优化** | 为每个分表建立合适的索引，提高查询性能 |

### 📦 数据归档策略

#### 归档条件
- 数据库空间使用率超过阈值（建议80%）
- 历史数据查询频率降低
- 存储成本优化需求
- 性能优化需求

#### 归档方式
```javascript
// 归档脚本示例
function archiveAccessLog(targetMonth) {
  const sourceCollection = `tssAccessLog_${targetMonth}`;
  const archiveCollection = `tssAccessLog_archive_${targetMonth}`;
  
  // 迁移数据到归档表
  db[sourceCollection].aggregate([
    {$out: archiveCollection}
  ]);
  
  // 清理原始表数据
  db[sourceCollection].drop();
  
  print(`归档完成: ${sourceCollection} -> ${archiveCollection}`);
}
```

#### 存储优化建议

```javascript
// 查看各月份表的数据量
db.getCollectionNames()
  .filter(name => name.startsWith("tssAccessLog_"))
  .forEach(table => {
    const count = db[table].count();
    const stats = db[table].stats();
    print(`${table}: ${count} 条记录, ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
  });

// 查看数据库空间使用情况
const dbStats = db.stats();
print(`数据库大小: ${(dbStats.dataSize / 1024 / 1024).toFixed(2)} MB`);
print(`存储大小: ${(dbStats.storageSize / 1024 / 1024).toFixed(2)} MB`);
print(`索引大小: ${(dbStats.indexSize / 1024 / 1024).toFixed(2)} MB`);

// 查看特定月份表的存储大小
const tableStats = db.tssAccessLog_202401.stats();
print(`表大小: ${(tableStats.size / 1024 / 1024).toFixed(2)} MB`);
print(`平均文档大小: ${(tableStats.avgObjSize / 1024).toFixed(2)} KB`);
```

## ⚙️ 配置详解

### 基础配置

| 配置项 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| `enabled` | boolean | true | 是否启用访问日志功能 | `true` |
| `use-sky-walking-trace-id` | boolean | true | 是否使用SkyWalking traceId作为请求ID | `true` |

### 输出控制

| 配置项 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| `enable-console-log` | boolean | true | 是否输出控制台日志 | `true` |
| `enable-mongo-storage` | boolean | true | 是否保存到MongoDB | `true` |

### 过滤规则

| 配置项 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `exclude-paths` | List<String> | 排除的路径（支持Ant匹配） | `["/linkusMonitor/**"]` |
| `static-file-pattern` | String | 静态文件正则表达式 | `"\\.(css\|js\|png)$"` |
| `exclude-methods` | List<String> | 排除的HTTP方法 | `["OPTIONS", "HEAD"]` |
| `exclude-status-codes` | List<Integer> | 排除的状态码 | `[404, 405]` |

### 安全配置

| 配置项 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `sensitive-headers` | List<String> | 敏感请求头（自动脱敏） | `["Authorization"]` |
| `sensitive-params` | List<String> | 敏感参数（自动脱敏） | `["password"]` |

## 🎯 使用场景

### 🛠️ 开发环境

```yaml
access-log:
  enabled: true
  enable-console-log: true      # 开启控制台日志，便于调试
  enable-mongo-storage: false   # 关闭MongoDB存储，节省资源
  exclude-paths:
    - "/linkusMonitor/**"
    - "/health/**"
```

**特点：**
- 实时查看请求日志，便于调试
- 减少存储开销
- 保留必要的过滤规则

### 🧪 测试环境

```yaml
access-log:
  enabled: true
  enable-console-log: true      # 开启控制台日志
  enable-mongo-storage: true    # 开启MongoDB存储，便于分析
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
```

**特点：**
- 完整的日志记录
- 便于性能测试和问题分析
- 支持数据统计和监控

### 🚀 生产环境

```yaml
access-log:
  enabled: true
  enable-console-log: false     # 关闭控制台日志，提高性能
  enable-mongo-storage: true    # 开启MongoDB存储，便于监控
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
    - "/favicon.ico"
    - "/robots.txt"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
  exclude-status-codes:
    - 404
    - 405
```

**特点：**
- 优化性能，减少日志输出开销
- 完整的存储记录，便于监控和分析
- 严格的过滤规则，减少无效数据

### ⚡ 性能优化模式

```yaml
access-log:
  enabled: true
  enable-console-log: false     # 关闭控制台日志
  enable-mongo-storage: false   # 关闭MongoDB存储
  exclude-paths:
    - "/linkusMonitor/**"
    - "/linkus-*/linkusMonitor/**"
    - "/health/**"
    - "/favicon.ico"
    - "/robots.txt"
    - "/static/**"
    - "/assets/**"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
    - "TRACE"
  exclude-status-codes:
    - 404
    - 405
    - 406
```

**特点：**
- 最小化性能影响
- 仅记录关键业务请求
- 适用于高并发场景

## 🔍 常见查询

### 📈 性能分析

```javascript
// 查询响应时间超过1秒的请求
db.tssAccessLog_202401.find({duration: {$gt: 1000}})

// 统计平均响应时间
db.tssAccessLog_202401.aggregate([
  {$group: {_id: null, avgDuration: {$avg: "$duration"}}}
])

// 按状态码分组统计
db.tssAccessLog_202401.aggregate([
  {$group: {_id: "$statusCode", count: {$sum: 1}}},
  {$sort: {_id: 1}}
])

// 查询最慢的10个请求
db.tssAccessLog_202401.find({})
  .sort({duration: -1})
  .limit(10)
  .project({"requestUri.path": 1, duration: 1, method: 1, timestamp: 1})

// 按路径统计平均响应时间
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: "$requestUri.path",
    avgDuration: {$avg: "$duration"},
    count: {$sum: 1},
    maxDuration: {$max: "$duration"},
    minDuration: {$min: "$duration"}
  }},
  {$sort: {avgDuration: -1}},
  {$limit: 10}
])
```

### 👥 用户行为分析

```javascript
// 查询特定用户的访问记录
db.tssAccessLog_202401.find({"loginUser.loginName": "admin"})

// 统计最活跃的IP地址
db.tssAccessLog_202401.aggregate([
  {$group: {_id: "$clientIp", count: {$sum: 1}}},
  {$sort: {count: -1}},
  {$limit: 10}
])

// 按路径分组统计访问量
db.tssAccessLog_202401.aggregate([
  {$group: {_id: "$requestUri.path", count: {$sum: 1}}},
  {$sort: {count: -1}},
  {$limit: 10}
])

// 查询特定域名的访问记录
db.tssAccessLog_202401.find({"requestUri.host": "example.com"})

// 按小时统计访问量趋势
db.tssAccessLog_202401.aggregate([
  {
    $group: {
      _id: {
        year: {$year: "$timestamp"},
        month: {$month: "$timestamp"},
        day: {$dayOfMonth: "$timestamp"},
        hour: {$hour: "$timestamp"}
      },
      count: {$sum: 1}
    }
  },
  {$sort: {"_id": 1}},
  {$limit: 24}
])

// 统计用户访问频率
db.tssAccessLog_202401.aggregate([
  {$match: {"loginUser.loginName": {$ne: null}}},
  {$group: {_id: "$loginUser.loginName", count: {$sum: 1}}},
  {$sort: {count: -1}},
  {$limit: 20}
])
```

### 🚨 错误监控

```javascript
// 查询所有错误请求
db.tssAccessLog_202401.find({statusCode: {$gte: 400}})

// 统计错误率
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: null,
    total: {$sum: 1},
    errors: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}}
  }},
  {$project: {errorRate: {$divide: ["$errors", "$total"]}}}
])

// 按状态码统计错误分布
db.tssAccessLog_202401.aggregate([
  {$match: {statusCode: {$gte: 400}}},
  {$group: {_id: "$statusCode", count: {$sum: 1}}},
  {$sort: {count: -1}}
])

// 查询特定路径的错误请求
db.tssAccessLog_202401.find({
  "requestUri.path": "/api/users",
  statusCode: {$gte: 400}
})

// 按时间统计错误趋势
db.tssAccessLog_202401.aggregate([
  {$match: {statusCode: {$gte: 400}}},
  {
    $group: {
      _id: {
        year: {$year: "$timestamp"},
        month: {$month: "$timestamp"},
        day: {$dayOfMonth: "$timestamp"},
        hour: {$hour: "$timestamp"}
      },
      errorCount: {$sum: 1}
    }
  },
  {$sort: {"_id": 1}},
  {$limit: 24}
])
```

### 🔗 链路追踪分析

```javascript
// 查询特定SkyWalking traceId的请求
db.tssAccessLog_202401.find({"skyWalkingInfo.skyWalkingTraceId": "abc123"})

// 统计链路追踪覆盖率
db.tssAccessLog_202401.aggregate([
  {$group: {
    _id: null,
    total: {$sum: 1},
    withTraceId: {$sum: {$cond: [{$ne: ["$skyWalkingInfo.skyWalkingTraceId", null]}, 1, 0]}}
  }},
  {$project: {coverage: {$divide: ["$withTraceId", "$total"]}}}
])

// 查询没有链路追踪的请求
db.tssAccessLog_202401.find({"skyWalkingInfo.skyWalkingTraceId": null})
```

## ⚠️ 注意事项

### 🚀 性能考虑

| 建议 | 说明 | 影响 |
|------|------|------|
| **合理配置排除规则** | 避免记录不必要的请求 | 减少日志量，提高性能 |
| **生产环境关闭控制台日志** | 减少I/O开销 | 显著提升网关性能 |
| **定期清理历史数据** | 控制MongoDB存储大小 | 避免存储空间不足 |
| **优化索引策略** | 为常用查询字段建立索引 | 提高查询性能 |
| **使用分表存储** | 按月分表，便于管理和查询 | 提高查询效率 |

### 🔒 安全考虑

| 建议 | 说明 | 风险 |
|------|------|------|
| **确保敏感信息脱敏** | 配置完整的脱敏规则 | 避免敏感数据泄露 |
| **定期检查脱敏配置** | 验证脱敏功能正常工作 | 防止配置遗漏 |
| **避免记录敏感业务数据** | 不在日志中记录业务敏感信息 | 保护业务数据安全 |
| **控制日志访问权限** | 限制日志数据的访问范围 | 防止未授权访问 |

### 💾 存储管理

#### 分表策略
- **按月分表**：系统自动按月份分表存储，表名格式为`sysAccessLog_YYYYMM`
- **索引优化**：为每个分表建立合适的索引，提高查询性能
- **跨表查询**：支持跨月份查询和历史数据检索

#### 归档策略
- **手动归档**：根据数据库空间使用情况手动执行归档操作
- **归档条件**：数据库空间使用率超过阈值、历史数据查询频率降低
- **归档方式**：将历史数据迁移到归档存储，保留必要的索引结构

#### 空间监控
```javascript
// 监控脚本示例
function monitorStorage() {
  const dbStats = db.stats();
  const usagePercent = (dbStats.dataSize / dbStats.storageSize * 100).toFixed(2);
  
  print(`数据库使用率: ${usagePercent}%`);
  print(`数据大小: ${(dbStats.dataSize / 1024 / 1024).toFixed(2)} MB`);
  print(`存储大小: ${(dbStats.storageSize / 1024 / 1024).toFixed(2)} MB`);
  
  if (usagePercent > 80) {
    print("⚠️  警告: 数据库使用率过高，建议执行归档操作");
  }
}
```

### 🏗️ 微服务架构

| 配置 | 说明 | 示例 |
|------|------|------|
| **context-path支持** | 支持`linkus-`前缀的context-path架构 | `linkus-user`、`linkus-order` |
| **监控端点路径** | 使用`/linkusMonitor`和`/linkus-*/linkusMonitor`路径 | `/linkusMonitor/health` |
| **路径匹配规则** | 支持Ant路径匹配模式 | `**`、`*`、`?` |

## 🛠️ 故障排除

### 🔍 常见问题

#### 日志不记录

**症状：** 访问请求没有产生日志记录

**排查步骤：**
1. 检查`enabled`配置是否为`true`
2. 检查路径是否被排除规则过滤
3. 确认微服务context-path配置正确
4. 查看应用启动日志，确认过滤器已加载

**解决方案：**
```yaml
# 临时开启调试模式
access-log:
  enabled: true
  enable-console-log: true
  exclude-paths: []  # 清空排除规则进行测试
```

#### 性能问题

**症状：** 网关响应时间明显增加

**排查步骤：**
1. 检查日志配置是否过于宽松
2. 监控MongoDB连接池使用情况
3. 查看系统资源使用情况

**解决方案：**
```yaml
# 性能优化配置
access-log:
  enable-console-log: false     # 关闭控制台日志
  exclude-paths:
    - "/linkusMonitor/**"
    - "/static/**"
    - "/assets/**"
  exclude-methods:
    - "OPTIONS"
    - "HEAD"
  exclude-status-codes:
    - 404
    - 405
```

#### 敏感信息泄露

**症状：** 日志中出现敏感信息

**排查步骤：**
1. 检查敏感信息配置是否完整
2. 验证脱敏功能是否正常工作
3. 查看日志格式是否正确

**解决方案：**
```yaml
# 完善敏感信息配置
access-log:
  sensitive-headers:
    - "Authorization"
    - "Cookie"
    - "JWT-TOKEN"
    - "X-Auth-Token"
    - "X-API-Key"
  sensitive-params:
    - "password"
    - "token"
    - "secret"
    - "key"
    - "credential"
```

#### MongoDB连接问题

**症状：** 无法连接MongoDB或连接超时

**排查步骤：**
1. 检查MongoDB连接配置
2. 确认数据库权限设置
3. 查看MongoDB服务状态
4. 检查网络连接

**解决方案：**
```yaml
# MongoDB连接配置
spring:
  data:
    mongodb:
      uri: ********************************:port/database
      connection-pool:
        max-size: 100
        min-size: 5
        max-wait-time: 30000
```

#### 分表存储问题

**症状：** 分表功能异常或查询性能下降

**排查步骤：**
1. 确认分表策略配置正确
2. 检查表命名规则是否符合预期
3. 验证跨月份查询功能是否正常
4. 监控分表后的查询性能

**解决方案：**
```javascript
// 检查分表状态
function checkTablePartition() {
  const tables = db.getCollectionNames().filter(name => name.startsWith("tssAccessLog_"));
  print("当前分表列表:");
  tables.forEach(table => {
    const count = db[table].count();
    print(`  ${table}: ${count} 条记录`);
  });
}

// 重建索引
function rebuildIndexes() {
  const tables = db.getCollectionNames().filter(name => name.startsWith("tssAccessLog_"));
  tables.forEach(table => {
    print(`重建索引: ${table}`);
    db[table].reIndex();
  });
}
```

#### 数据归档问题

**症状：** 归档操作失败或数据不完整

**排查步骤：**
1. 检查归档脚本的执行权限
2. 确认归档存储空间充足
3. 验证归档后数据完整性
4. 监控归档操作的执行时间

**解决方案：**
```javascript
// 归档前检查
function preArchiveCheck(targetMonth) {
  const sourceCollection = `tssAccessLog_${targetMonth}`;
  const archiveCollection = `tssAccessLog_archive_${targetMonth}`;
  
  // 检查源表是否存在
  if (!db.getCollectionNames().includes(sourceCollection)) {
    print(`错误: 源表 ${sourceCollection} 不存在`);
    return false;
  }
  
  // 检查目标表是否已存在
  if (db.getCollectionNames().includes(archiveCollection)) {
    print(`警告: 归档表 ${archiveCollection} 已存在，将覆盖`);
  }
  
  // 检查存储空间
  const sourceStats = db[sourceCollection].stats();
  const dbStats = db.stats();
  const availableSpace = dbStats.storageSize - dbStats.dataSize;
  
  if (availableSpace < sourceStats.size) {
    print(`错误: 存储空间不足，需要 ${sourceStats.size} 字节，可用 ${availableSpace} 字节`);
    return false;
  }
  
  return true;
}
```

## 📈 扩展功能

### 🔧 自定义日志格式

可以修改`AccessLogGlobalFilter`中的日志格式，添加自定义字段：

```java
// 自定义日志格式示例
String logMessage = String.format(
    "ACCESS_LOG RequestId=%s, Method=%s, Path=%s, ClientIP=%s, " +
    "CustomField=%s, StatusCode=%d, Duration=%dms",
    requestId, method, path, clientIp, customValue, statusCode, duration
);
```

### 🗄️ 新增存储方式

可以扩展`IMongoStorageDao`接口，支持其他数据库存储：

```java
public interface IStorageDao {
    void save(AccessLogInfo logInfo);
    List<AccessLogInfo> query(AccessLogQuery query);
    void delete(String requestId);
}

// 实现类示例
@Component
public class RedisStorageDao implements IStorageDao {
    // Redis存储实现
}
```

### 📊 实时监控

可以基于MongoDB数据构建实时监控面板，展示访问统计和性能指标：

```javascript
// 实时统计脚本
function getRealTimeStats() {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  
  return db.tssAccessLog_202401.aggregate([
    {$match: {timestamp: {$gte: oneHourAgo}}},
    {$group: {
      _id: null,
      totalRequests: {$sum: 1},
      avgResponseTime: {$avg: "$duration"},
      errorCount: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}},
      uniqueUsers: {$addToSet: "$loginUser.loginName"}
    }},
    {$project: {
      totalRequests: 1,
      avgResponseTime: {$round: ["$avgResponseTime", 2]},
      errorRate: {$divide: ["$errorCount", "$totalRequests"]},
      uniqueUserCount: {$size: "$uniqueUsers"}
    }}
  ]).toArray()[0];
}
```

### 🚨 告警机制

可以基于访问日志数据设置告警规则，如错误率过高、响应时间异常等：

```javascript
// 告警检查脚本
function checkAlerts() {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
  
  // 检查错误率
  const errorStats = db.tssAccessLog_202401.aggregate([
    {$match: {timestamp: {$gte: fiveMinutesAgo}}},
    {$group: {
      _id: null,
      total: {$sum: 1},
      errors: {$sum: {$cond: [{$gte: ["$statusCode", 400]}, 1, 0]}}
    }},
    {$project: {errorRate: {$divide: ["$errors", "$total"]}}}
  ]).toArray()[0];
  
  if (errorStats && errorStats.errorRate > 0.05) {
    print(`🚨 告警: 错误率过高 ${(errorStats.errorRate * 100).toFixed(2)}%`);
  }
  
  // 检查响应时间
  const responseTimeStats = db.tssAccessLog_202401.aggregate([
    {$match: {timestamp: {$gte: fiveMinutesAgo}}},
    {$group: {
      _id: null,
      avgResponseTime: {$avg: "$duration"},
      maxResponseTime: {$max: "$duration"}
    }}
  ]).toArray()[0];
  
  if (responseTimeStats && responseTimeStats.avgResponseTime > 1000) {
    print(`🚨 告警: 平均响应时间过长 ${responseTimeStats.avgResponseTime.toFixed(2)}ms`);
  }
}
```

### 🤖 数据归档自动化

可以开发自动化归档脚本，根据预设条件自动执行归档操作：

```javascript
// 自动化归档脚本
function autoArchive() {
  const now = new Date();
  const threeMonthsAgo = new Date(now.getYear(), now.getMonth() - 3, 1);
  
  // 获取需要归档的月份
  const monthsToArchive = [];
  let currentDate = new Date(threeMonthsAgo);
  
  while (currentDate < now) {
    const monthStr = currentDate.getFullYear() + 
                    String(currentDate.getMonth() + 1).padStart(2, '0');
    monthsToArchive.push(monthStr);
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  
  // 执行归档
  monthsToArchive.forEach(month => {
    const sourceCollection = `tssAccessLog_${month}`;
    if (db.getCollectionNames().includes(sourceCollection)) {
      print(`开始归档: ${sourceCollection}`);
      archiveAccessLog(month);
    }
  });
}

// 设置定时任务（需要外部调度器）
// 每月1号凌晨2点执行归档
// 0 2 1 * * node autoArchive.js
```

### 🔍 分表查询优化

可以开发统一的查询接口，自动处理跨月份查询：

```javascript
// 统一查询接口
function unifiedQuery(query) {
  const { startTime, endTime, ...otherConditions } = query;
  
  // 确定需要查询的表
  const tables = getTablesInRange(startTime, endTime);
  
  // 执行跨表查询
  const results = [];
  tables.forEach(table => {
    const tableResults = db[table].find({
      timestamp: { $gte: startTime, $lte: endTime },
      ...otherConditions
    }).toArray();
    results.push(...tableResults);
  });
  
  return results;
}

// 获取时间范围内的表
function getTablesInRange(startTime, endTime) {
  const tables = [];
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  
  let currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
  
  while (currentDate <= endDate) {
    const monthStr = currentDate.getFullYear() + 
                    String(currentDate.getMonth() + 1).padStart(2, '0');
    const tableName = `tssAccessLog_${monthStr}`;
    
    if (db.getCollectionNames().includes(tableName)) {
      tables.push(tableName);
    }
    
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  
  return tables;
}
```

---

<div align="center">

**Spring Cloud Gateway 访问日志系统** - 为您的微服务架构提供强大的日志记录能力

[返回顶部](#spring-cloud-gateway-访问日志系统)

</div> 
