package com.linkus.gateway.dao.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.unit.DataUnit;
import com.linkus.gateway.entity.TeSysAccessLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import com.linkus.gateway.dao.IMongoStorageDao;
import com.linkus.gateway.entity.TeSysUser;

@Repository
@Slf4j
public class MongoStorageDaoImpl implements IMongoStorageDao {

	private static final String COLLECTION_NAME = "tssAccessLog";

	@Autowired
	private MongoTemplate mongoTemplate;

	@Autowired
	@Qualifier("tssMongoTemplate")
	private MongoTemplate tssMongoTemplate;

	@Override
	public TeSysUser getSysUserByLoginName(String loginName) {
		Query q = Query.query(Criteria.where("isValid").is(true).and("loginName").is(loginName));
		return mongoTemplate.findOne(q, TeSysUser.class);
	}

	@Override
	public TeSysUser getItfUserByLoginName(String loginName) {
		Query q = Query.query(Criteria.where("loginName").is(loginName).and("isValid").is(true).and("isItfUser").is(true));
		return mongoTemplate.findOne(q, TeSysUser.class);
	}

	@Override
	public List<TeSysUser> listItfUsers() {
		Query q = Query.query(Criteria.where("isValid").is(true).and("isItfUser").is(true));
		return mongoTemplate.find(q, TeSysUser.class);
	}

	/**
	 * 保存访问日志
	 *
	 * @param accessLog 访问日志对象
	 */
	@Override
	public void saveAccessLog(TeSysAccessLog accessLog) {
		try {
			String month = DateUtil.format(new Date(), "yyyyMM");
			tssMongoTemplate.insert(accessLog, COLLECTION_NAME + "_" + month);
		} catch (Exception e) {
			log.error("保存访问日志失败: {}", e.getMessage(), e);
		}
	}

}
