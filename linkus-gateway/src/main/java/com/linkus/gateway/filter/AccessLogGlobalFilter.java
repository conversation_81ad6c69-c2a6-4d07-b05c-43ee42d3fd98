package com.linkus.gateway.filter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.linkus.gateway.config.AccessLogConfig;
import com.linkus.gateway.dao.IMongoStorageDao;
import com.linkus.gateway.entity.*;
import com.linkus.gateway.utils.IpUtils;
import com.linkus.gateway.utils.SensitiveDataUtil;
import com.linkus.gateway.utils.SkyWalkingUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.URI;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 访问日志全局过滤器
 * 记录所有请求的访问日志，包括请求信息、响应状态、处理时间等
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccessLogGlobalFilter extends BaseGlobalFilter implements GlobalFilter, Ordered {

    private final Cache<String, TeSysUser> userCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(24, TimeUnit.DAYS)
            .build();

    @Resource
    private IMongoStorageDao mongoStorageDao;

    private static final String ACCESS_LOG_PREFIX = "ACCESS_LOG";

    @Resource
    private AccessLogConfig accessLogConfig;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 缓存编译后的正则表达式，避免重复编译
    private volatile Pattern staticFilePattern;

    @Override
    public int getOrder() {
        return FilterOrder.ACCESS_LOG_FILTER_ORDER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();

        // 检查是否启用访问日志
        if (!accessLogConfig.isEnabled()) {
            return chain.filter(exchange);
        }

        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // 检查是否需要排除该请求
        if (shouldExcludeRequest(request)) {
            return chain.filter(exchange);
        }

        return chain.filter(exchange.mutate().request(request).build()).doFinally(signalType -> {
            try {
                // 记录完整的访问日志（包含请求和响应信息）
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                logAccessInfo(request, response, duration);

            } catch (Exception e) {
                log.error("记录访问日志时发生错误: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 检查是否需要排除该请求
     */
    private boolean shouldExcludeRequest(ServerHttpRequest request) {
        String path = request.getPath().value();
        HttpMethod method = request.getMethod();

        // 检查路径是否在排除列表中
        if (accessLogConfig.getExcludePaths() != null) {
            for (String excludePath : accessLogConfig.getExcludePaths()) {
                if (pathMatcher.match(excludePath, path)) {
                    return true;
                }
            }
        }

        // 检查是否为静态文件（根据文件后缀）
        if (isStaticFile(path)) {
            return true;
        }

        // 检查HTTP方法是否在排除列表中
        if (method != null && accessLogConfig.getExcludeMethods() != null &&
                accessLogConfig.getExcludeMethods().contains(method.name())) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为静态文件
     */
    private boolean isStaticFile(String path) {
        if (StrUtil.isBlank(path) || StrUtil.isBlank(accessLogConfig.getStaticFilePattern())) {
            return false;
        }

        // 移除查询参数
        int queryIndex = path.indexOf('?');
        if (queryIndex > 0) {
            path = path.substring(0, queryIndex);
        }

        // 使用缓存的正则表达式匹配静态文件后缀
        return getStaticFilePattern().matcher(path).find();
    }

    /**
     * 获取缓存的静态文件正则表达式
     */
    private Pattern getStaticFilePattern() {
        if (staticFilePattern == null) {
            synchronized (this) {
                if (staticFilePattern == null) {
                    staticFilePattern = Pattern.compile(accessLogConfig.getStaticFilePattern(), Pattern.CASE_INSENSITIVE);
                }
            }
        }
        return staticFilePattern;
    }

    /**
     * 生成请求ID
     * 根据配置决定是否优先使用 SkyWalking 的 traceId
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 记录完整的访问信息（包含请求和响应）
     */
    private void logAccessInfo(ServerHttpRequest request, ServerHttpResponse response, long duration) {
        try {

            // 生成请求ID
            String requestId = "";
            // 获取 SkyWalking 的链路追踪信息
            TeSkyWalkingInfo skyWalkingInfo = null;
            if (accessLogConfig.isUseSkyWalkingTraceId()) {
                String traceId = SkyWalkingUtil.getTraceId();
                if (StrUtil.isNotBlank(traceId)) {
                    requestId = traceId;
                    skyWalkingInfo = new TeSkyWalkingInfo();
                    skyWalkingInfo.setSkyWalkingTraceId(traceId);
                    skyWalkingInfo.setSkyWalkingSegmentId(SkyWalkingUtil.getSegmentId());
                    skyWalkingInfo.setSkyWalkingSpanId(SkyWalkingUtil.getSpanId());
                }
            }
            if (StringUtils.isEmpty(requestId)) {
                requestId = generateRequestId();
            }
            // 获取响应状态码
            HttpStatus status = response.getStatusCode();
            int statusCode = status != null ? status.value() : 0;

            // 检查是否需要排除该状态码
            if (accessLogConfig.getExcludeStatusCodes() != null &&
                    accessLogConfig.getExcludeStatusCodes().contains(statusCode)) {
                return;
            }

            // 获取请求信息
            String method = Objects.requireNonNull(request.getMethod()).name();

            URI uri = request.getURI();
            TeUri requestUri = new TeUri();
            requestUri.setScheme(uri.getScheme());
            requestUri.setHost(uri.getHost());
            requestUri.setPort(uri.getPort());
            requestUri.setPath(uri.getPath());

            // 脱敏查询参数中的敏感参数
            String maskedQuery = SensitiveDataUtil.maskSensitiveUrl(uri.getQuery(), accessLogConfig.getSensitiveParams());
            requestUri.setQuery(maskedQuery);

            // 获取客户端IP
            String clientIp = IpUtils.getIpAddr(request);

            // 获取用户代理
            String userAgent = request.getHeaders().getFirst(HttpHeaders.USER_AGENT);
            userAgent = StrUtil.isBlank(userAgent) ? "-" : userAgent;

            // 获取登录用户名
            String loginName = getLoginName(request);
            TeUser loginUser = new TeUser();
            if (StrUtil.isBlank(loginName)) {
                loginUser.setLoginName("-");
            } else {
                loginUser.setLoginName(loginName);
                TeSysUser user = userCache.get(loginName, userName -> mongoStorageDao.getSysUserByLoginName(userName));
                if (Objects.nonNull(user)) {
                    loginUser.setUserName(user.getUserName());
                    loginUser.setUserId(user.getId().toHexString());
                    loginUser.setJobCode(user.getJobCode());
                }
            }

            // 获取请求头信息
            String referer = request.getHeaders().getFirst(HttpHeaders.REFERER);
            referer = StrUtil.isBlank(referer) ? "-" : referer;

            // 获取脱敏的敏感请求头信息
            String sensitiveHeaders = SensitiveDataUtil.maskSensitiveHeaders(request, accessLogConfig.getSensitiveHeaders());

            // 获取脱敏的敏感参数信息
            String sensitiveParams = SensitiveDataUtil.maskSensitiveParams(request, accessLogConfig.getSensitiveParams());

            // 创建访问日志实体对象
            TeSysAccessLog accessLog = new TeSysAccessLog();
            accessLog.setRequestId(requestId);
            accessLog.setMethod(method);
            accessLog.setRequestUri(requestUri);
            accessLog.setClientIp(clientIp);
            accessLog.setUserAgent(userAgent);
            accessLog.setLoginUser(loginUser);
            accessLog.setReferer(referer);
            accessLog.setStatusCode(statusCode);
            accessLog.setDuration(duration);
            accessLog.setTimestamp(new Date());

            if (StrUtil.isNotBlank(sensitiveHeaders)) {
                accessLog.setSensitiveHeaders(sensitiveHeaders);
            }
            if (StrUtil.isNotBlank(sensitiveParams)) {
                accessLog.setSensitiveParams(sensitiveParams);
            }

            // 获取 SkyWalking 的链路追踪信息
            if (Objects.nonNull(skyWalkingInfo)) {
                accessLog.setSkyWalkingInfo(skyWalkingInfo);
            }

            // 输出控制台日志
            outputLog(accessLog);

        } catch (Exception e) {
            log.error("记录访问日志时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 输出日志
     *
     * @param accessLog 日志消息
     */
    private void outputLog(TeSysAccessLog accessLog) {
        // 输出控制台日志
        if (accessLogConfig.isEnableConsoleLog()) {
            try {
                log.info("{} {}", ACCESS_LOG_PREFIX, JSON.toJSONString(accessLog));
            } catch (Exception e) {
                log.error("输出控制台日志失败: {}", e.getMessage(), e);
            }
        }
        // 保存访问日志到MongoDB
        if (accessLogConfig.isEnableMongoStorage()) {
            try {
                mongoStorageDao.saveAccessLog(accessLog);
            } catch (Exception e) {
                log.error("保存访问日志到MongoDB失败: {}", e.getMessage(), e);
            }
        }
    }
} 
