package com.linkus.gateway.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;

/**
 * SkyWalking 工具类
 * 用于获取和管理链路追踪信息
 *
 * <AUTHOR>
 */
@Slf4j
public class SkyWalkingUtil {

    private static final String N_A_VALUE = "N/A";

    /**
     * 获取 SkyWalking traceId
     *
     * @return traceId，如果获取失败则返回空字符串
     */
    public static String getTraceId() {
        try {
            String traceId = TraceContext.traceId();
            if (StrUtil.isNotBlank(traceId) && !N_A_VALUE.equals(traceId)) {
                return traceId;
            }
        } catch (Exception e) {
            log.debug("获取 SkyWalking traceId 失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取 SkyWalking segmentId
     *
     * @return segmentId，如果获取失败则返回空字符串
     */
    public static String getSegmentId() {
        try {
            String segmentId = TraceContext.segmentId();
            if (StrUtil.isNotBlank(segmentId) && !N_A_VALUE.equals(segmentId)) {
                return segmentId;
            }
        } catch (Exception e) {
            log.debug("获取 SkyWalking segmentId 失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取 SkyWalking spanId
     *
     * @return spanId，如果获取失败则返回空字符串
     */
    public static String getSpanId() {
        try {
            int spanId = TraceContext.spanId();
            return Integer.toString(spanId);
        } catch (Exception e) {
            log.debug("获取 SkyWalking spanId 失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 检查是否在 SkyWalking 链路追踪上下文中
     *
     * @return true 如果在追踪上下文中，false 否则
     */
    public static boolean isInTraceContext() {
        String traceId = getTraceId();
        return StrUtil.isNotBlank(traceId);
    }

    /**
     * 获取完整的 SkyWalking 链路追踪信息
     *
     * @return 包含 traceId、segmentId、spanId 的字符串
     */
    public static String getFullTraceInfo() {
        StringBuilder traceInfo = new StringBuilder();

        String traceId = getTraceId();
        String segmentId = getSegmentId();
        String spanId = getSpanId();

        if (StrUtil.isNotBlank(traceId)) {
            traceInfo.append("TraceId=").append(traceId);
        }

        if (StrUtil.isNotBlank(segmentId)) {
            if (traceInfo.length() > 0) {
                traceInfo.append(", ");
            }
            traceInfo.append("SegmentId=").append(segmentId);
        }

        if (StrUtil.isNotBlank(spanId)) {
            if (traceInfo.length() > 0) {
                traceInfo.append(", ");
            }
            traceInfo.append("SpanId=").append(spanId);
        }

        return traceInfo.toString();
    }
} 
