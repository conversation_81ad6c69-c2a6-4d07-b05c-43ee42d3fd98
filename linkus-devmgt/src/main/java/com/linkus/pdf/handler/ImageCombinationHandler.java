package com.linkus.pdf.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linkus.pdf.model.image.Image;
import com.linkus.pdf.model.pdf.PdfPageBodySection;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;


public class ImageCombinationHandler implements PdfHtmlHandler {

    private final String TEMPLATE_FILE_NAME = "image_template.ftl";

    @Override
    public Object handle(String chapterName, String jsonString, boolean viewPort) throws JsonProcessingException {

        ObjectMapper objectMapper = new ObjectMapper();

        DataModel dataModel = objectMapper.readValue(jsonString, DataModel.class);

        // 创建 FreeMarker 配置
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(PortalOverviewTopicEntryHandler.class, "/templates");
        cfg.setDefaultEncoding("UTF-8");

        Template template;

        // 加载模板
        try {
            template = cfg.getTemplate(TEMPLATE_FILE_NAME);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        Attribute attribute = dataModel.getAttribute();
        Map<String, Object> templateData = new HashMap<>();
        Image image = new Image();
        image.setFit(attribute.getFit());
        image.setUrl(attribute.getUrl());
        image.setPreview(attribute.isPreview());
        image.setImgHeight(attribute.getImgHeight());
        image.setImgWidth(attribute.getImgWidth());
        templateData.put("attribute", image);

        // 加载模板
        try {
            template = cfg.getTemplate(TEMPLATE_FILE_NAME);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        StringWriter out = new StringWriter();
        try {
            template.process(templateData, out);
        } catch (TemplateException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return new PdfPageBodySection(chapterName, out.toString());
    }

    @Data
    public static class DataModel {
        private Attribute attribute;

        // getters, setters, default constructor, etc.

    }

    @Data
    @AllArgsConstructor
    static class Attribute {
        private String url;
        private String fit;
        private boolean preview;
        private int imgWidth;
        private int imgHeight;
        private int paddingTop;
        private int paddingBottom;

        public Attribute(){

        }
        // getters, setters, default constructor, etc.
    }

}
