server:
  port: ${port:18086}
  servlet:
    context-path: /${spring.application.name}

spring:
  application:
    name: linkus-pdf
  profiles:
    active: dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 500MB
    
outputBasePath: D:/transferFile/temp/
logging:
  file:
    name: logs/app.log  # 日志文件路径（目录会自动创建）
    max-size: 10MB     # 单个文件最大 100MB（触发拆分）
    max-history: 30     # 保留最近 30 天的旧日志（按文件名中的日期排序）
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
