<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%
    String ver = "20190422";
%>
<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>项目人员效能评估</title>

    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>

    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>

    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/4.4.0/styles/iview.css"/>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css"/>

    <link href="../../01css/icomoon/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

    <script type="text/javascript" src="../../bizQuery/cnpt/queryPrjAndPrjSetSelect.js?ver=<%=ver%>"></script>
    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <script src="../../biz/bscPrjEffectEvaluation/taskFisrtPassRateDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/defectClassifyBySeverityDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/storeTaskRankByUserDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/devTaskDefectDensityDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/defectRankByUserDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/demandChangeTrendDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/bugChangeTrendDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/reqSurveyDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/bugSurveyDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/taskPromptnessDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/timelinessRepairOrVerifyDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/codeChangeRankByUserDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/codeViolateRankByUserDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/codeChangeTrendDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/codeViolateDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/devActivityOverViewDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/codeSurveyDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/bugFallbackRateDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/burnDownChartDetail.js"></script>
    <script src="../../biz/bscPrjEffectEvaluation/bugEscapeRateDetail.js"></script>
    <!-- echarts -->
    <script src="../../00scripts/00lib/echarts5.3/echarts.min.js"></script>
    <script src="../../00scripts/00lib/echarts-liquidfill/echarts-liquidfill.js"></script>

    <style>
        [v-cloak] {
            display: none;
        }

        body {
            overflow-y: hidden;
        }

        .ivu-icon-ios-arrow-down:before {
            content: "\f3d0";
        }

        .ivu-icon-ios-browsers:before {
            content: "\f3f0";
        }

        .ivu-icon-ios-cube:before {
            content: "\f318";
        }

        .ivu-icon-ios-search:before {
            content: "\f21f";
        }

        .ivu-icon-md-menu:before {
            content: "\f20d";
        }

        .ivu-icon-ios-list-box-outline:before {
            content: "\f453";
        }

        .ivu-icon-ios-settings:before {
            content: "\f43d";
        }

        .ivu-icon-ios-arrow-forward:before {
            content: "\f10a";
        }

        .ivu-cascader-menu .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-icon-ios-arrow-back:before {
            content: "\f3d2";
        }

        .ivu-date-picker-prev-btn-arrow-double i:after {
            content: "\F3D2";
        }

        .ivu-date-picker-header .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-date-picker-header .ivu-date-picker-next-btn-arrow-double i:after {
            content: "\F3D3";
            margin-left: -8px;
        }

        .ivu-page .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-page .ivu-icon-ios-arrow-down:before {
            content: "\f104";
        }

        .ivu-page-item-active a, .ivu-page-item-active:hover a {
            color: #fff !important;
        }

        /*强制表格文本一行超出后打点表示 */
        .lineClamp2 .ivu-tooltip-rel {
            word-break: break-all;;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .ivu-modal-confirm-head-icon-error {
            color: #f30;
        }

        .ivu-modal-confirm-head-icon-warning {
            color: #f90;
        }

        .ivu-icon-ios-information-circle:before {
            content: "\F149";
        }

        .ivu-icon-ios-alert:before {
            content: "\f35b";
        }

        .ivu-icon-ios-checkmark-circle:before {
            content: "\f120";
        }

        .ivu-icon-ios-help-circle:before {
            content: "\f142";
        }

        .ivu-icon-ios-loading:before {
            content: "\F29C";
        }

        .ivu-icon-md-arrow-dropup:before {
            content: "\f10d";
        }

        .ivu-icon-md-arrow-dropdown:before {
            content: "\f104";
        }

        .ivu-icon-ios-close:before {
            content: "\f178";
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm {
            display: inline-block;
            margin-right: 12px;
            vertical-align: middle;
            position: relative;
            top: -2px;
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm .ivu-icon {
            font-size: 28px;
            color: #f90;
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-title {
            font-size: 16px;
        }

        .ivu-modal-confirm .ivu-modal-confirm-body {
            font-size: 14px;
            padding-left: 48px;
        }

        .ivu-icon-ios-close-circle:before {
            content: "\f177" !important;
        }

        .ivu-icon-ios-add:before {
            content: "\f489";
        }

        .ivu-icon-ios-remove:before {
            content: "\f2f4";
        }

        .ivu-btn-ghost {
            color: #495060;
            background-color: transparent;
            border-color: #dddee1;
        }

        .ivu-select-arrow {
            margin-top: 0px;
        }

        .left_col_wrap {
            display: inline-block;
            height: 100%;
            width: 15%;
            /* border-right: 1px solid #ADADAD;*/
            padding: 8px 0px;
            overflow-y: auto;
            box-shadow: 4px 0px 12px 0px rgba(198, 213, 229, 0.40);
            position: relative;
        }

        .left_col_wrap.no_expand_class {
            width: 5%;
        }

        .right_col_wrap {
            display: inline-block;
            height: 100%;
            width: calc(85% - 4px);
        }

        .right_col_wrap.no_expand_class {
            width: calc(95% - 4px);
        }

        .right_col_wrap .top {
            display: flex;
            padding: 14px 16px;
            font-size: 14px;
            color: #3883E5;
            background: #f5f8fb;
        }

        .right_col_wrap .top .iconfont {
            padding-right: 6px;
            font-size: 20px;
        }

        .right_col_wrap .contentWrap {
            display: flex;
            height: 100%;
            width: 100%;
        }

        .left_col_wrap .ivu-menu-vertical .ivu-menu-submenu-title-icon {
            top: 10px;
            right: 0px;
        }

        .left_col_wrap .left_icon_class {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            padding-top: 8px;
            border-right: 1px solid #ADADAD
        }

        .left_col_wrap .left_icon_class .iconfont {
            color: #a5acc4;
            font-size: 26px;
        }

        .left_col_wrap .left_icon_class .ivu-tooltip {
            padding-bottom: 12px;
        }

        .ivu-tooltip-inner {
            white-space: normal;
        }

        .left_col_wrap .left_icon_class .ivu-tooltip:hover {
            color: #3883e5;
        }

        .left_col_wrap .left_icon_class .ivu-tooltip.subMenuTooltip {
            padding-left: 12px;
        }

        .left_col_wrap .iconfont {
            vertical-align: inherit;
            padding-right: 8px;
        }

        .left_menu_class {
            width: 100% !important;
            height: 100%;
            /*padding-left: 16px;*/
        }

        .ivu-menu-vertical .ivu-menu-item, .ivu-menu-vertical .ivu-menu-submenu-title {
            color: #a5acc4;
            padding: 14px 28px;
            padding-right: 12px;
        }

        .left_col_wrap::-webkit-scrollbar, .echarts_wrap::-webkit-scrollbar, .ivu-table-body::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        .left_col_wrap::-webkit-scrollbar-thumb, .echarts_wrap::-webkit-scrollbar-thumb, .ivu-table-body::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 10px;
        }

        .left_col_wrap::-webkit-scrollbar-track, .echarts_wrap::-webkit-scrollbar-track, .ivu-table-body::-webkit-scrollbar-track {
            background: #efefef;
            border-radius: 2px;
        }

        .left_col_wrap::-webkit-scrollbar-thumb:hover, .echarts_wrap::-webkit-scrollbar-thumb:hover, .ivu-table-body::-webkit-scrollbar-thumb:hover {
            background: #979797;
        }

        .left_col_wrap::-webkit-scrollbar-corner, .echarts_wrap::-webkit-scrollbar-corner, .ivu-table-body::-webkit-scrollbar-corner {
            background: #179a16;
        }

        .right_col_wrap .top .iconfont.icon-back {
            color: #3883e5;
            margin-left: auto;
            padding-right: 0;
        }

        .task_dmp_report_warp.dmp_report_warp {
            background-color: #fff;
        }

        .content_area_top {
            display: flex;
            height: 350px;
            margin: 16px 16px 0 16px;
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgb(198 213 229 / 40%);
        }

        .dmp_report_warp .data_area .content_area_trend_text {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            color: #555555;
            line-height: 22px;
            padding-bottom: 16px;
        }

        .surveyDetailModal .dmp_report_warp {
            background-color: #fff;
        }

        .surveyDetailModal .content_area_top {
            display: flex;
            height: 84px;
            margin: 16px 16px 0 16px;
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgb(198 213 229 / 40%);
        }

        .surveyDetailModal .content_area_top .req_complete_rate, .task_complete_rate {
            background-color: #F2F2F2;
        }

        .surveyDetailModal .content_area_top > div {
            display: flex;
            width: 12.5%;
            justify-content: center;
            align-items: center;
        }

        .surveyDetailModal.codeSurveyDetailModal .content_area_top > div {
            width: 25%;
        }

        .surveyDetailModal .content_area_top > div .content_node_wrap {
            font-family: Georgia, Georgia-Bold;
            font-weight: 700;
            color: #666666;
            line-height: 22px;
            display: flex;
            justify-content: center;
            align-items: baseline;
        }

        .surveyDetailModal .content_area_top > div .content_node_wrap .content_node_num {
            font-size: 32px;
        }

        .surveyDetailModal .content_area_top > div .content_node_text {
            font-size: 12px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            color: #bbc0cf;
            line-height: 22px;
            display: flex;
            justify-content: center;
            padding-top: 4px;
        }

        .surveyDetailModal .content_area_top .dev_req_delay .content_node_wrap {
            color: #e65d4e;
        }

        .surveyDetailModal .content_area_top .test_req_delay .content_node_wrap {
            color: #e65d4e;
        }

        .surveyDetailModal .content_area_top .dev_req, .dev_req_delay, .test_req, .test_req_delay {
            cursor: pointer;
            position: relative;
        }

        .surveyDetailModal .content_area_top > div .content_split {
            width: 1px;
            height: 36px;
            background: #e7eef5;
            position: absolute;
            right: 0px;
        }

        .detail_table .ivu-table tr:first-child th:first-child {
            border-bottom: 1px solid #dddee1 !important;
        }

        .ivu-menu-vertical.ivu-menu-light:after {
            display: none;
        }

        .data_area .detail_title_wrap .content_area_trend_text {
            display: inline-block;
        }

        .data_area .detail_title_wrap .detail_export {
            float: right;
            font-size: 18px;
            color: #3883e5;
        }

        .detailModalClass .icon-export {
            line-height: 2;
            font-size: 20px;
            margin-left: auto;
            color: #3883e5;
        }

        .ivu-table td.demo-table-info-cell-planEndDate-red {
            color: #E65D4E;
        }

        .burnDownChartDetailModal .dmp_report_warp .data_area {
            position: relative;
        }

        .burnDownChartDetailModal .dmp_report_warp .data_area .burnDownChartDetailTips{
            position: absolute;
            right: 16px;
            top: 24px;
            color: #F59B27;
            font-size: 14px;
        }

        .detailModalClass .ivu-modal-body .filter_warp {
            padding: 8px 0;
            font-size: 12px;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }

        .detailModalClass .ivu-modal-body .filter_warp > div {
            width: 25%;
        }

        .detailModalClass .ivu-modal-body .filter_warp > div.pd_l5 {
            padding-left: 5px;
        }

        .detailModalClass .ivu-modal-body .filter_warp > div label {
            display: inline-block;
            width: 6em;
            line-height: 32px;
        }

        .detailModalClass .ivu-modal-body .filter_warp > div .filter_component {
            margin-left: -4px;
            width: calc(100% - 6em);
            display: inline-block;
            vertical-align: middle;
        }

        .detailModalClass .ivu-modal-body .filter_warp .view_button {
            margin-left: auto;
            text-align: right;
        }

        .detailModalClass .view_table .ivu-table-body::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        .detailModalClass .view_table .ivu-table-body::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 10px;
        }
        .detailModalClass .view_table .ivu-table-body::-webkit-scrollbar-track {
            background: #efefef;
            border-radius: 2px;
        }
        .detailModalClass .view_table .ivu-table-body::-webkit-scrollbar-thumb:hover {
            background: #979797;
        }
        .detailModalClass .view_table .ivu-table-body::-webkit-scrollbar-corner {
            background: #179a16;
        }

        .detailModalClass .view_table .ivu-table-tip::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        .detailModalClass .view_table .ivu-table-tip::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 10px;
        }
        .detailModalClass .view_table .ivu-table-tip::-webkit-scrollbar-track {
            background: #efefef;
            border-radius: 2px;
        }
        .detailModalClass .view_table .ivu-table-tip::-webkit-scrollbar-thumb:hover {
            background: #979797;
        }
        .detailModalClass .view_table .ivu-table-tip::-webkit-scrollbar-corner {
            background: #179a16;
        }

        .suspension-1{
            position: fixed;
            right: 0;
            top: calc(50% - 100px);
            z-index: 100;
            width: 40px;
            box-shadow: 0 1px 4px 0 rgba(76,103,167,.4);
        }

        .suspension-1>ul:first-child li:first-child {
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 14px;
            padding: 4px 8px;
            border-radius:  4px 0  0 0;
            background: #4c67a7;
            cursor: pointer;
        }

        .suspension-1>ul:first-child li:nth-child(2) {
            position: relative;
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: #84b1ec;
            border-radius:  0  0  0  4px;
        }
        .suspension-1>ul:first-child li:nth-child(2):after {
            content: "在线帮助文档";
        }
        /*.suspension-1>ul:first-child li:hover:after {*/
        /*    width: 104px;*/
        /*}*/
        .suspension-1>ul:first-child li:after {
            position: absolute;
            right: 40px;
            width: 0;
            transition-property: width;
            transition-duration: .5s;
            height: 40px;
            line-height: 40px;
            text-align: center;
            color: #fff;
            background-color: rgba(132, 177, 236, .8);
            overflow: hidden;
        }

        .suspension-1>ul:first-child li i {
            color: #fff;
            font-size: 18px;
            cursor: pointer;
        }
        .ivu-modal-no-mask {
            pointer-events: auto;
        }
        .selectedBtnClass {
            display: flex;
            margin-left: auto;
        }
        .selectedBtnClass .change_button {
            width: 94px;
            height: 24px;
            border: 1px solid #dbe3eb;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            color: #555555;
        }
        .selectedBtnClass .change_button.selected {
            border: 1px solid #3883e5;
            color: #3883e5;
            font-weight: 500;
        }
        .selectedBtnClass .prjNoticeBoard {
            border-radius: 4px 0 0 4px;
        }
        .selectedBtnClass .prjUserPerformance {
            border-left: 0;
        }
        .selectedBtnClass .digitalProductionEvaluation {
            border-radius: 0 4px 4px 0;
            border-left: 0;
        }
        .search_tasks .lks_menu_tasks_search .ivu-input-wrapper .ivu-input-prefix {
            width: 14px;
            height: 18px;
            left: inherit;
        }
        .search_tasks .lks_menu_tasks_search .tasks_search_input .ivu-input-prefix i {
            line-height: normal;
        }
    </style>
</head>

<body style="overflow: hidden;">
<%@include file="../menu/headMenu.jsp" %>

<div class="main report_body_height" id="main" style="overflow: hidden;" v-cloak>
    <div class="suspension-1" v-drag>
        <ul draggable="false">
            <li>在线帮助</li>
            <li @click="showHelp = true">
                <i class="iconfont icon-question-line"></i>
            </li>

        </ul>
    </div>

    <div class="layout-content layout-container report_warp_height" v-if="showHelp">
        <iframe src="/02html/public/helpDocument.html?fileBaseDefId=65f171eb3cebe61f6767a3d7" frameborder="0" style="width: 100%;height: 100%"></iframe>
    </div>
    <div class="layout-content layout-container report_warp_height"
         style="display: flex;min-width: 1190px; width: 100%; position: relative;" v-if="!showHelp">

        <div :class="[ !!isExpand? 'left_col_wrap' : 'left_col_wrap no_expand_class' ]">
            <i-Menu ref="side_menu" class="left_menu_class" v-show="!!isExpand" mode="vertical" theme="light" @on-select="route"
                    :open-names="openNames" :active-name="activeName">
                <div v-for="(first,index) in menuData" v-show="((!!isPrjEffectEvalutionAdmin || !!isEipAdmin || (!!currentUser.sbuId && currentUser.sbuId == '185')) && first.current.id ==='650ab318c61b712bbe2acd7a')
                                                                || first.current.id != '650ab318c61b712bbe2acd7a'">
                    <Menu-Item v-if="!first.children && first.current.value" :name="first.current.id">
                        <span class="iconfont" :class="first.current.icon"></span>
                        {{first.current.defName}}
                    </Menu-Item>
                    <Submenu :name="first.current.id" v-else-if="first.children">
                        <template slot="title">
                            <span class="iconfont" :class="first.current.icon"></span>
                            {{first.current.defName}}
                        </template>
                        <Menu-Item v-for="second in first.children" :name="second.current.id" v-show="!!isPrjEffectEvalutionAdmin || (!!currentUser.sbuId && currentUser.sbuId == '185' && second.current.id == '650ab5fcc61b712bbe2ad494') ||
                                   (!isEipAdmin && !isPrjEffectEvalutionAdmin && (second.current.id != '650ab5fcc61b712bbe2ad494' && second.current.id != '650ab61ec61b712bbe2ad4ec' && second.current.id != '668f4af265be226b1f338fed' && second.current.id != '667290ddd70673a94a31fafd' && second.current.id != '680ee020fb03e0e173a98bf7'))
                                   || (!!isEipAdmin && !isPrjEffectEvalutionAdmin && (second.current.id != '650ab5fcc61b712bbe2ad494' && second.current.id != '650ab61ec61b712bbe2ad4ec' && second.current.id != '668f4af265be226b1f338fed' && second.current.id != '680ee020fb03e0e173a98bf7'))">
                            <span class="iconfont" :class="second.current.icon"></span>
                            {{second.current.defName}}
                        </Menu-Item>
                    </Submenu>
                </div>
            </i-Menu>

            <div v-show="!isExpand" class="left_icon_class">
                <Tooltip transfer placement="bottom-start" v-for="item in expendMenu" :class="{subMenuTooltip:item.parentId}" v-show="!!isPrjEffectEvalutionAdmin || (!!currentUser.sbuId && currentUser.sbuId == '185' && item.id == '650ab5fcc61b712bbe2ad494') ||
                                   (!isEipAdmin && !isPrjEffectEvalutionAdmin && (item.id !='650ab318c61b712bbe2acd7a' || item.id != '650ab5fcc61b712bbe2ad494' && item.id != '650ab61ec61b712bbe2ad4ec' && item.id != '668f4af265be226b1f338fed' && item.id != '667290ddd70673a94a31fafd' && item.id != '680ee020fb03e0e173a98bf7'))
                                   || (!!isEipAdmin && !isPrjEffectEvalutionAdmin && (item.id != '650ab5fcc61b712bbe2ad494' && item.id != '650ab61ec61b712bbe2ad4ec' && item.id != '668f4af265be226b1f338fed' && item.id != '680ee020fb03e0e173a98bf7'))">
                    <span class="iconfont" :class="item.icon" @click="route(item.id)"></span>
                    <template slot="content">
                        <p>{{item.name}}</p>
                    </template>
                </Tooltip>
            </div>
        </div>
        <div :class="[ !!isExpand? 'right_col_wrap' : 'right_col_wrap no_expand_class' ]">
            <div v-show="!!checkedNodeAndParents" class="top">
                <span v-show="!!isExpand" class="iconfont icon-packUp" @click="expandLeftMenu"></span>
                <span v-show="!isExpand" class="iconfont icon-unfold" @click="expandLeftMenu"></span>
                <span>{{ checkedNodeAndParents }}</span>

                <div class="selectedBtnClass" v-show="activeName == '650ab48bc61b712bbe2ad0f0'">
                    <span :class="[ selectedBtn == 'prjNoticeBoard' ? 'change_button prjNoticeBoard selected' : 'change_button prjNoticeBoard' ]" @click="changeBtn('prjNoticeBoard')">项目看板</span>
                    <span :class="[ selectedBtn == 'prjUserPerformance' ? 'change_button prjUserPerformance selected' : 'change_button prjUserPerformance' ]" @click="changeBtn('prjUserPerformance')">项目人员绩效</span>
                    <span :class="[ selectedBtn == 'digitalProductionEvaluation' ? 'change_button digitalProductionEvaluation selected' : 'change_button digitalProductionEvaluation' ]" @click="changeBtn('digitalProductionEvaluation')">数字化生产评估</span>
                </div>
            </div>
            <div class="contentWrap">
                <iframe id="contentIframe" ref="contentIframe" name="contentIframe" style="width: 100%" :height="windowInnerHeight" frameborder="0"
                        :src="iframeSrc"></iframe>
            </div>
        </div>

    </div>
    <task-fisrt-pass-rate-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                 :is-task-fisrt-pass-rate-detail="isTaskFisrtPassRateDetail">
    </task-fisrt-pass-rate-detail>

    <defect-classify-by-severity-detail :select-prj-id="selectPrjId" :defect-classify-by-severity-vo="defectClassifyBySeverityVo"
                                        :is-defect-classify-by-severity-detail="isDefectClassifyBySeverityDetail">
    </defect-classify-by-severity-detail>

    <store-task-rank-by-user-detail :select-prj-id="selectPrjId" :task-type="taskType"
                                    :is-store-task-rank-by-user-detail="isStoreTaskRankByUserDetail">
    </store-task-rank-by-user-detail>

    <dev-task-defect-density-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                    :is-dev-task-defect-density-detail="isDevTaskDefectDensityDetail">
    </dev-task-defect-density-detail>

    <defect-rank-by-user-detail :select-prj-id="selectPrjId" :task-type="taskType" :defect-rank-by-user-data-vo="defectRankByUserDataVo"
                                :is-defect-rank-by-user-detail="isDefectRankByUserDetail">
    </defect-rank-by-user-detail>

    <demand-change-trend-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                :is-demand-change-trend-detail="isDemandChangeTrendDetail">
    </demand-change-trend-detail>

    <bug-change-trend-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                             :is-bug-change-trend-detail="isBugChangeTrendDetail">
    </bug-change-trend-detail>

    <req-survey-detail :select-prj-id="selectPrjId"
                       :is-req-survey-detail="isReqSurveyDetail">
    </req-survey-detail>

    <bug-survey-detail :select-prj-id="selectPrjId"
                       :is-bug-survey-detail="isBugSurveyDetail">
    </bug-survey-detail>

    <task-promptness-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                            :is-task-promptness-detail="isTaskPromptnessDetail">
    </task-promptness-detail>

    <timeliness-repair-or-verify-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                        :is-timeliness-repair-or-verify-detail="isTimelinessRepairOrVerifyDetail">
    </timeliness-repair-or-verify-detail>

    <code-change-rank-by-user-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                     :is-code-change-rank-by-user-detail="isCodeChangeRankByUserDetail">
    </code-change-rank-by-user-detail>

    <code-violate-rank-by-user-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                      :is-code-violate-rank-by-user-detail="isCodeViolateRankByUserDetail">
    </code-violate-rank-by-user-detail>

    <code-change-trend-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                              :is-code-change-trend-detail="isCodeChangeTrendDetail">
    </code-change-trend-detail>

    <code-violate-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                         :is-code-violate-detail="isCodeViolateDetail">
    </code-violate-detail>

    <dev-activity-over-view-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                                   :is-dev-activity-over-view-detail="isDevActivityOverViewDetail">
    </dev-activity-over-view-detail>

    <code-survey-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                        :code-survey-type="codeSurveyType" :is-code-survey-detail="isCodeSurveyDetail">
    </code-survey-detail>

    <bug-fallback-rate-detail :select-prj-id="selectPrjId" :select-date-period="selectDatePeriod"
                              :is-bug-fallback-rate-detail="isBugFallbackRateDetail">
    </bug-fallback-rate-detail>

    <burn-down-chart-detail :select-prj-id="selectPrjId" :search-type="searchType"
                            :is-burn-down-chart-detail="isBurnDownChartDetail">
    </burn-down-chart-detail>

    <bug-escape-rate-detail :select-prj-id="selectPrjId" :is-bug-escape-rate-detail="isBugEscapeRateDetail">
    </bug-escape-rate-detail>
</div>

</body>

<script>
    Date.prototype.format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1,                 //月份
            "d+": this.getDate(),                    //日
            "h+": this.getHours(),                   //小时
            "m+": this.getMinutes(),                 //分
            "s+": this.getSeconds(),                 //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds()             //毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };
    /* 控制登录失效后页面跳转登录页 */
    verifyLogin();

    var prjBizHttpRequest = linkus.location.prjbiz;
    var queryHttpRequest = linkus.location.query;
    var prjUserHttpRequest 	= linkus.location.prjuser;
    var headPicUrl 	= prjUserHttpRequest + '/sysUserCtrl/getHeadPic.action?headImagePath=';
    var bizDomain = linkus.location.prjbiz;
    var queryDomain = linkus.location.query;
    var kmDomain = linkus.location.km;
    var prjDomain = linkus.location.prj;
    var biz = linkus.location.prjbiz;
    var vue = new Vue({
        el: '#main',
        data: function () {
            var sf = this;
            return {

                selectedBtn: "prjNoticeBoard",

                activeName: "650ab23fc61b712bbe2acb5f",

                windowInnerHeight: 250,
                iframeSrc: "",
                checkedNodeAndParents: "",

                buId: "5a70111329974f7cabe9115d",
                isPrjEffectEvalutionAdmin: false,
                isEipAdmin: false,

                isExpand: true,
                isShowBack: false,

                selectPrjId: "",
                selectDatePeriod: [],
                taskType: "",
                isTaskFisrtPassRateDetail: false,
                isDefectClassifyBySeverityDetail: false,
                isStoreTaskRankByUserDetail: false,
                isDevTaskDefectDensityDetail: false,
                isDefectRankByUserDetail: false,
                isDemandChangeTrendDetail: false,
                isBugChangeTrendDetail: false,
                isReqSurveyDetail: false,
                isBugSurveyDetail: false,
                isTaskPromptnessDetail: false,
                isTimelinessRepairOrVerifyDetail: false,
                condParam: {},

                isCodeChangeRankByUserDetail: false,
                isCodeViolateRankByUserDetail: false,
                isCodeChangeTrendDetail: false,
                isCodeViolateDetail: false,
                isDevActivityOverViewDetail: false,
                isCodeSurveyDetail: false,
                isBugFallbackRateDetail: false,
                codeSurveyType: "",

                defectClassifyBySeverityVo: {},
                defectRankByUserDataVo: {},

                searchType: "",
                isBurnDownChartDetail: false,

                isBugEscapeRateDetail: false,

                menuData:[],
                expendMenu:[],
                openNames:[],

                showHelp:false,
                isDrag:false,

                currentUser: {},
            };
        },
        watch:{
            openNames() {
                this.$nextTick(() => {
                    this.$refs.side_menu.updateOpened();
                    this.$refs.side_menu.updateActiveName();
                })
            }
        },

        // 自定义指令
        directives:{
            drag:function(el,bindings){
                var sf = this;
                el.onmousedown = function(e){
                    sf.isDrag = true;
                    var disx = e.pageX- el.offsetLeft;
                    var disy = e.pageY- el.offsetTop;

                    if (e.preventDefault) {
                        e.preventDefault()
                    } else {
                        e.returnValue = false  // 解决快速频繁拖动滞后问题
                    }

                    document.onmousemove = function (e) {
                        // 鼠标位置-鼠标相对元素位置=元素位置
                        let left = e.clientX - disx;
                        let top = e.clientY - disy;
                        // 限制拖拽范围不超出可视区
                        if ( left <= 0) {
                            left = 5    // 设置成5,离边缘不要太近
                        } else if (left > document.documentElement.clientWidth - el.clientWidth) { // document.documentElement.clientWidth屏幕可视区宽度
                            left = document.documentElement.clientWidth - el.clientWidth - 5
                        }

                        if ( top <= 0) {
                            top = 5
                        } else if (top > document.documentElement.clientHeight - el.clientHeight) {
                            top = document.documentElement.clientHeight - el.clientHeight - 5
                        }
                        el.style.top= top + 'px';
                        el.style.bottom = 'auto';
                    };
                    document.onmouseup = function(){
                        document.onmousemove = document.onmouseup = null;
                        sf.isDrag = false;
                    }
                }
            }
        },

        created: function () {
            var sf = this;
            if (!Vue.evtHub) {
                Vue.evtHub = new Vue();
            }
            var activeName = sf.getQueryString("activeName");
            if(!!activeName) {
                sf.activeName = activeName;
            }
            var condParam = sf.getQueryString("condParam");
            if(!!condParam && condParam != "{}") {
                sf.condParam = JSON.parse(condParam);
            }
            var selectedBtn = sf.getQueryString("selectedBtn");
            if(!!selectedBtn) {
                sf.selectedBtn = selectedBtn;
            }
            sf.queryMenu();

            sf.loadCurrentUser();
        },

        mounted: function () {
            var sf = this;
            sf.windowInnerHeight = "100%";
            sf.checkIsPrjEffectEvalutionAdmin();
            sf.checkIsEipAdmin();
            window.addEventListener('message', function (e) {
                if (!!e.data && JSON.stringify(e.data) != "{}") {
                    if (!!e.data.expandOrShrink) {
                        sf.isExpand = !sf.isExpand;
                    } else if (!!e.data.isPrjCodeLink) {
                        sf.isShowBack = false;
                        sf.checkedNodeAndParents = "项目人员效能评估-详情";
                        sf.iframeSrc = e.data.prjCodeLinkUrl + "&from=bscPrjEffectEvaluationNew";
                    } else if (!!e.data.reqSurveyDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.isReqSurveyDetail = true;
                    } else if (!!e.data.bugSurveyDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.isBugSurveyDetail = true;
                    } else if (!!e.data.demandChangeTrendDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isDemandChangeTrendDetail = true;
                    } else if (!!e.data.bugChangeTrendDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isBugChangeTrendDetail = true;
                    } else if (!!e.data.taskFisrtPassRateDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isTaskFisrtPassRateDetail = true;
                    } else if (!!e.data.defectClassifyBySeverityDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.defectClassifyBySeverityVo = e.data.defectClassifyBySeverityVo;
                        sf.isDefectClassifyBySeverityDetail = true;
                    } else if (!!e.data.storeTaskRankByUserDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.taskType = e.data.taskType;
                        sf.isStoreTaskRankByUserDetail = true;
                    } else if (!!e.data.devTaskDefectDensityDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isDevTaskDefectDensityDetail = true;
                    } else if (!!e.data.defectRankByUserDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.taskType = e.data.taskType;
                        sf.defectRankByUserDataVo = e.data.defectRankByUserDataVo;
                        sf.isDefectRankByUserDetail = true;
                    } else if (!!e.data.taskPromptnessDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isTaskPromptnessDetail = true;
                    } else if (!!e.data.timelinessRepairOrVerifyDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isTimelinessRepairOrVerifyDetail = true;
                    } else if (!!e.data.condChanged) {
                        sf.condParam = e.data.condParam;
                    }else if(!!e.data.codeChangeRankByUserDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isCodeChangeRankByUserDetail = true;
                    }else if(!!e.data.codeViolateRankByUserDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isCodeViolateRankByUserDetail = true;
                    }else if(!!e.data.codeChangeTrendDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isCodeChangeTrendDetail = true;
                    }else if(!!e.data.codeViolateDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isCodeViolateDetail = true;
                    }else if(!!e.data.devActivityOverViewDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isDevActivityOverViewDetail = true;
                    }else if(!!e.data.openPrjTargetSummary) {
                        sf.condParam = e.data.condParam;
                        sf.activeName = "650ab4eac61b712bbe2ad1e5";
                        sf.route("650ab4eac61b712bbe2ad1e5");
                    }else if(!!e.data.codeSurveyDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.codeSurveyType = e.data.type;
                        sf.isCodeSurveyDetail = true;
                    }else if(!!e.data.bugFallbackRateDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.selectDatePeriod = e.data.selectDatePeriod;
                        sf.isBugFallbackRateDetail = true;
                    }else if(!!e.data.burnDownChartDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.searchType = e.data.searchType;
                        sf.isBurnDownChartDetail = true;
                    }else if(!!e.data.bugEscapeRateDetail) {
                        sf.selectPrjId = e.data.selectPrjId;
                        sf.isBugEscapeRateDetail = true;
                    }
                }
            });

            /*关闭任务一次通过率详情弹窗*/
            Vue.evtHub.$on("closeTaskFisrtPassRateDetail", function (data) {
                sf.isTaskFisrtPassRateDetail = data;
            });

            /*关闭缺陷按严重程度分类详情弹窗*/
            Vue.evtHub.$on("closeDefectClassifyBySeverityDetail", function (data) {
                sf.isDefectClassifyBySeverityDetail = data;
            });

            /*关闭存量任务按成员排名详情弹窗*/
            Vue.evtHub.$on("closeStoreTaskRankByUserDetail", function (data) {
                sf.isStoreTaskRankByUserDetail = data;
            });

            /*关闭开发任务缺陷密度详情弹窗*/
            Vue.evtHub.$on("closeDevTaskDefectDensityDetail", function (data) {
                sf.isDevTaskDefectDensityDetail = data;
            });

            /*关闭待修复/验证缺陷按成员排名详情弹窗*/
            Vue.evtHub.$on("closeDefectRankByUserDetail", function (data) {
                sf.isDefectRankByUserDetail = data;
            });

            /*关闭需求存量/新增/完成趋势详情弹窗*/
            Vue.evtHub.$on("closeDemandChangeTrendDetail", function (data) {
                sf.isDemandChangeTrendDetail = data;
            });

            /*缺陷需求存量/新增/完成趋势详情弹窗*/
            Vue.evtHub.$on("closeBugChangeTrendDetail", function (data) {
                sf.isBugChangeTrendDetail = data;
            });

            /*需求概览详情弹窗*/
            Vue.evtHub.$on("closeReqSurveyDetail", function (data) {
                sf.isReqSurveyDetail = data;
            });

            /*缺陷概览详情弹窗*/
            Vue.evtHub.$on("closeBugSurveyDetail", function (data) {
                sf.isBugSurveyDetail = data;
            });

            /*任务及时率详情弹窗*/
            Vue.evtHub.$on("closeTaskPromptnessDetail", function (data) {
                sf.isTaskPromptnessDetail = data;
            });

            /*缺陷修复/验证及时率详情弹窗*/
            Vue.evtHub.$on("closeTimelinessRepairOrVerifyDetail", function (data) {
                sf.isTimelinessRepairOrVerifyDetail = data;
            });

            /*代码行变更按成员排名详情弹窗*/
            Vue.evtHub.$on("closeCodeChangeRankByUserDetail", function (data) {
                sf.isCodeChangeRankByUserDetail = data;
            });

            /*代码违规数按成员排名详情弹窗*/
            Vue.evtHub.$on("closeCodeViolateRankByUserDetail", function (data) {
                sf.isCodeViolateRankByUserDetail = data;
            });

            /*代码行变更趋势详情弹窗*/
            Vue.evtHub.$on("closeCodeChangeTrendDetail", function (data) {
                sf.isCodeChangeTrendDetail = data;
            });

            /*代码违规数详情弹窗*/
            Vue.evtHub.$on("closeCodeViolateDetail", function (data) {
                sf.isCodeViolateDetail = data;
            });

            /*开发活跃度详情弹窗*/
            Vue.evtHub.$on("closeDevActivityOverViewDetail", function (data) {
                sf.isDevActivityOverViewDetail = data;
            });

            /*代码质量概览详情弹窗*/
            Vue.evtHub.$on("closeCodeSurveyDetail", function (data) {
                sf.isCodeSurveyDetail = data;
            });

            /*缺陷回退率详情弹窗*/
            Vue.evtHub.$on("closeBugFallbackRateDetail", function (data) {
                sf.isBugFallbackRateDetail = data;
            });

            /*项目任务燃尽图详情弹窗*/
            Vue.evtHub.$on("closeBurnDownChartDetail", function (data) {
                sf.isBurnDownChartDetail = data;
            });

            /*缺陷逃逸率详情弹窗*/
            Vue.evtHub.$on("closeBugEscapeRateDetail", function (data) {
                sf.isBugEscapeRateDetail = data;
            });
        },

        methods: {
            loadCurrentUser: function () {
                var _this = this;
                return $.ajax({
                    url: prjUserHttpRequest + '/sysUserCtrl/queryByLoginName.action',
                    type: 'post',
                    dataType: 'JSON',
                    async: true,
                }).done(function (teSysUser) {
                    _this.currentUser = teSysUser;
                });
            },

            //项目度量-切换tab页
            changeBtn: function(tabName) {
                var sf = this;
                sf.selectedBtn = tabName;
                sf.activeName = "650ab48bc61b712bbe2ad0f0";
                /*sf.route("650ab48bc61b712bbe2ad0f0");*/
                var iframe = document.getElementById('contentIframe');
                iframe.contentWindow.postMessage({ changeTab: true, tabName: sf.selectedBtn }, '*');
            },

            /*返回上一层目录*/
            backParentCatalogue: function () {
                var sf = this;
                sf.activeName = "650ab48bc61b712bbe2ad0f0";
                sf.route("650ab48bc61b712bbe2ad0f0");
            },

            //展开/收起菜单
            expandLeftMenu: function () {
                var sf = this;
                sf.isExpand = !sf.isExpand;
            },

            //点击菜单
            route: function (name) {
                var sf = this;
                sf.activeName = name;
                sf.isShowBack = false;
                sf.checkedNodeAndParents = "";
                sf.iframeSrc = "";
                var selectMenuData = sf.expendMenu.filter(function(item){
                    return item.id === name;
                })[0];
                if(selectMenuData.id === '67ee204c1a33679603d96f0e'){
                    var iframeSrc = '/02vue-cli/projectPerformance/index.html#/performanceQuery?isNoMenu=true';
                    sf.iframeSrc = iframeSrc;
                }else if(selectMenuData.value){
                    if(selectMenuData.id != '650ab5bfc61b712bbe2ad3f6' && selectMenuData.id != "650ab4eac61b712bbe2ad1e5"
                        && selectMenuData.id != "668f4af265be226b1f338fed" && selectMenuData.id != "668f493665be226b1f338fec"
                        && selectMenuData.id != "669a2a4f2c7018f1bab834a7" && selectMenuData.id != "67fcc1a0fb03e0e1733343b4"){
                        sf.checkedNodeAndParents = selectMenuData.name;
                    }
                    var iframeSrc = linkus.location.prjbiz + '/forward.action?t=report/'+selectMenuData.value;
                    if(selectMenuData.value.indexOf('condParam') > -1){
                        iframeSrc=iframeSrc+'='+JSON.stringify(sf.condParam)
                    }
                    if(sf.activeName == "650ab48bc61b712bbe2ad0f0") {
                        iframeSrc = iframeSrc + "&tabName=" + sf.selectedBtn;
                    }else if(sf.activeName == "67fcc1a0fb03e0e1733343b4") {
                        iframeSrc = iframeSrc + "&condParam=" + JSON.stringify(sf.condParam);
                    }
                    sf.iframeSrc = iframeSrc;
                }
            },

            queryMenu:function (){
                var sf = this;
                $.ajax({
                    url: linkus.location.prjuser +'/sysDefCtrl/querySubSysMenu.action',
                    data: {
                        srcDefId:'649d29c664586deda422d3bd',
                    },
                    type: 'post',
                    success: function(res) {
                        if(res.success){
                            sf.menuData = [];
                            res.data = res.data || [];
                            if(res.data && res.data){
                                sf.menuData = res.data;
                                sf.menuData.forEach(function(item,index){
                                    sf.openNames.push(item.current.id)
                                });
                                sf.getExpendMenu(res.data);
                            }
                        }else{
                            sf.$Message.error(res.message)
                        }
                    },
                    error: function(data) {
                        sf.$Message.error({
                            content: '查询菜单失败，请联系管理员！',
                            duration: 3
                        });
                    }
                });
            },

            getExpendMenu:function(menuData){
                var sf = this;
                sf.expendMenu = [];
                menuData.forEach(function(item){
                    let param = {
                        value:item.current.value,
                        name :item.current.defName,
                        id:item.current.id,
                        icon:item.current.icon,
                        parentId:null,
                    }
                    if(item.children || item.current.value){
                        sf.expendMenu.push(param);
                    }
                    if(item.children){
                        item.children.forEach(function(second){
                            let param = {
                                value:second.current.value,
                                name :item.current.defName+ ' / ' + second.current.defName,
                                id:second.current.id,
                                icon:second.current.icon,
                                parentId:item.current.id
                            }
                            sf.expendMenu.push(param);
                        })
                    }
                });
                sf.route(sf.activeName);
            },

            //查询是否是项目人员效能管理员
            checkIsPrjEffectEvalutionAdmin: function () {
                var sf = this;
                sf.isPrjEffectEvalutionAdmin = false;
                $.ajax({
                    url: linkus.location.prjuser + '/sysDefRoleUserCtrl/checkIsPrjEffectEvalutionAdmin.action',
                    type: 'post',
                    data: {
                        buId: sf.buId
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (!!data) {
                            sf.isPrjEffectEvalutionAdmin = true;
                        }
                    },
                    error: function (data) {
                        sf.$Message.error({
                            content: "查询是否是项目人员效能管理员角色失败，请联系管理员！",
                            duration: 3
                        });
                    }
                });
            },

            //查询是否是部门管理员角色
            checkIsEipAdmin: function() {
                var sf = this;
                sf.isEipAdmin = false;
                $.ajax({
                    url: linkus.location.report + '/bscEffectTeamManage/checkIsEipAdmin',
                    type: 'post',
                    dataType: 'json',
                    success: function (data) {
                        if(!!data && JSON.stringify(data) != "{}" && !!data.success && !!data.data) {
                            sf.isEipAdmin = true;
                        }
                    },
                    error: function (data) {
                        sf.$Message.error({
                            content: "查询是否是部门管理员角色失败，请联系管理员！",
                            duration: 3
                        });
                    }
                });
            },

            /*获取参数*/
            getQueryString: function (name) {
                // 获取参数
                var url = window.location.search;
                // 正则筛选地址栏
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                // 匹配目标参数
                var result = url.substr(1).match(reg);
                //返回参数值
                return result ? decodeURIComponent(result[2]) : null;
            },

        },

    });

</script>

</html>
