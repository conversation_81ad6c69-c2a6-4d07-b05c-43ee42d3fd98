<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>基础指标数据导入</title>

    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>

    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>

    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css"/>

    <link href="../../01css/icomoon/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <!-- echarts -->
    <script src="../../00scripts/00lib/echarts5.3/echarts.min.js"></script>

    <script type="text/javascript" src="../../00scripts/00lib/vue/comboxTree/deptMultiSelect.js"></script>

    <!-- 人员搜索自定义组件 -->
    <script src="../../00scripts/user/lks-user-load-fuzzy.js" type="text/javascript"></script>

    <style>
        [v-cloak] {
            display: none;
        }

        .ivu-modal-confirm-head-icon-error {
            color: #f30;
        }

        .ivu-modal-confirm-head-icon-warning {
            color: #f90;
        }

        .ivu-icon-ios-information-circle:before {
            content: "\F149";
        }

        .ivu-icon-ios-alert:before {
            content: "\f35b";
        }

        .ivu-icon-ios-checkmark-circle:before {
            content: "\f120";
        }

        .ivu-icon-ios-help-circle:before {
            content: "\f142";
        }

        .ivu-icon-ios-loading:before {
            content: "\F29C";
        }

        .ivu-icon-md-arrow-dropup:before {
            content: "\f10d";
        }

        .ivu-icon-md-arrow-dropdown:before {
            content: "\f104";
        }

        .ivu-icon-ios-close:before {
            content: "\f178";
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm {
            display: inline-block;
            margin-right: 12px;
            vertical-align: middle;
            position: relative;
            top: -2px;
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm .ivu-icon {
            font-size: 28px;
            color: #f90;
        }

        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-title {
            font-size: 16px;
        }

        .ivu-modal-confirm .ivu-modal-confirm-body {
            font-size: 14px;
            padding-left: 48px;
        }

        /*.ivu-icon-ios-close-circle:before{
            content: "\f177" !important;
        }*/

        .ivu-icon-ios-add:before {
            content: "\f489";
        }

        .ivu-icon-ios-remove:before {
            content: "\f2f4";
        }

        .ivu-icon-ios-arrow-down:before {
            content: "\f3d0";
        }

        .ivu-icon-ios-browsers:before {
            content: "\f3f0";
        }

        .ivu-icon-ios-cube:before {
            content: "\f318";
        }

        .ivu-icon-ios-search:before {
            content: "\f21f";
        }

        .ivu-icon-md-menu:before {
            content: "\f20d";
        }

        .ivu-icon-ios-list-box-outline:before {
            content: "\f453";
        }

        .ivu-icon-ios-settings:before {
            content: "\f43d";
        }

        .ivu-icon-ios-arrow-forward:before {
            content: "\f10a";
        }

        .ivu-cascader-menu .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-icon-ios-arrow-back:before {
            content: "\f3d2";
        }

        .ivu-date-picker-prev-btn-arrow-double i:after {
            content: "\F3D2";
        }

        .ivu-date-picker-header .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-date-picker-header .ivu-date-picker-next-btn-arrow-double i:after {
            content: "\F3D3";
            margin-left: -8px;
        }

        .ivu-page .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-page .ivu-icon-ios-arrow-down:before {
            content: "\f104";
        }

        .ivu-page-item-active a, .ivu-page-item-active:hover a {
            color: #fff !important;
        }

        /*强制表格文本两行超出后打点表示 */
        .lineClamp1 .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .lineClamp1 .ivu-tooltip {
            vertical-align: middle;
        }

        .prj_select_class .ivu-select-dropdown {
            width: 100%;
            z-index: 9999999;
        }

        .prj_select_class .ivu-select-dropdown {
            min-width: 550px !important;
        }

        .content_area {
            overflow-y: auto;
            height: calc(100% - 82px);
        }

        .dmp_report_warp .filter_warp .filter_col > label {
            display: flex;
        }

        .dmp_report_warp .filter_warp .filter_col > label .date_img {
            color: #3883e5;
            font-weight: bolder;
            padding-left: 2px;
            font-size: 14px;
        }

        .content_area::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        .content_area::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 10px;
        }

        .content_area::-webkit-scrollbar-track {
            background: #efefef;
            border-radius: 2px;
        }

        .content_area::-webkit-scrollbar-thumb:hover {
            background: #979797;
        }

        .content_area::-webkit-scrollbar-corner {
            background: #179a16;
        }

        .detailModalClass .icon-export {
            line-height: 2;
            font-size: 20px;
            margin-left: auto;
            color: #3883e5;
        }

        .dmp_report_warp .filter_warp .prj_filter_col.filter_col {
            width: 50%;
        }

        .ivu-select-multiple .ivu-select-selection {
            overflow-y: auto;
            height: 32px;
        }
        .ivu-table-cell {
            display: flex;
        }
        .ivu-table-cell .ivu-table-cell-tree {
            margin-right: 5px;
            display: flex;
            width: 16px;
            height: 16px;
            border: 1px solid #dcdee2;
            border-radius: 2px;
            background-color: #fff;
            line-height: 12px;
            cursor: pointer;
            vertical-align: middle;
            -webkit-transition: color .2s ease-in-out,border-color .2s ease-in-out;
            transition: color .2s ease-in-out,border-color .2s ease-in-out;
            justify-content: center;
        }
        .ivu-table-cell .ivu-table-cell-tree .ivu-icon {
            font-size: 14px;
        }
        .ivu-table-cell .ivu-table-cell-tree-empty {
            cursor: default;
            color: transparent;
            background-color: transparent;
            border-color: transparent;
        }
        .view_table .ivu-table th {
            border-bottom: 1px solid #dddee1 !important;
        }
        .isNoShowHeadClass .blank-name {
            padding: 14px 16px;
        }
        .isNoShowHeadClass .blank-name .top {
            font-size: 14px;
            color: #495060;
            display: flex;
            align-items: center;
        }
        .isNoShowHeadClass .blank-name .top .iconfont {
            font-size: 20px;
            padding-right: 6px;
        }
        .blank-name-tab .tab_blank {
            margin-left: auto;
            display: flex;
        }
        .isNoShowHeadClass .blank-name-tab .tab_blank span {
            padding: 0 8px;
        }
        .isNoShowHeadClass.main {
            min-width: 100% !important;
            width: 100%;
        }
        .dmp_report_warp .filter_warp .view_button {
            margin-left: auto;
            /*text-align: left;*/
            height: 32px;
            padding-left: 75px;
        }

        .multi-line-header::before {
            content: '工作负荷、效率、质量及附加考核等4项之和';
            display: block; /* 使内容成为块级元素，换到下一行 */
            font-size: 14px; /* 调整字体大小以匹配设计需求 */
            color: #606266; /* 调整字体颜色 */
            margin-top: 4px; /* 根据需要调整上边距 */
        }
        .filter_component .user-fuzzy {
            position: relative;
        }
        .tipStyle {
            display: flex;color: #ed4014; margin-top: 10px;align-items: center;
        }

        .filter_component .ctlgTreeAsyn{
            width: 100% !important;
        }
        .ivu-checkbox+span, .ivu-checkbox-wrapper+span {
            margin-top: 8px;
        }
        .report_warp_height .filter_tree .data_area {
            height: calc(100% - 154px);
        }
        .ivu-page-item-jump-next::after, .ivu-page-item-jump-prev::after {
            content: "•••";
        }
    </style>
</head>

<body style="overflow: hidden;">
<%@include file="../menu/headMenu.jsp" %>
<div :class="[ !isShowHead? 'isNoShowHeadClass main bg-light-grey report_body_height' : 'main bg-light-grey report_body_height' ]"
     id="main" style="height: 100%; overflow: hidden;" v-cloak>
    <div class="layout-content layout-container bg-white report_warp_height"
         style="width: 100%; position: relative" :style="{minWidth: (!isShowHead ? '100%' : '1190px')}">

        <div class="blank-name blank-name-tab" style="display: flex; align-items: baseline;">
            <h1 :style="{display: (!isShowHead ? 'none' : 'inline-block')}">基础指标数据导入</h1>
            <div v-if="!isShowHead" class="top" @click="route"><span class="iconfont icon-packUp"></span>效能度量 / 基础指标数据导入</div>
            <div class="tab_blank">
                <span :class="{active:currentName === 'planEmp'}" @click="tabChange('planEmp')">规划人员</span>
                <span :class="{active:currentName === 'demandEmp'}" @click="tabChange('demandEmp')">需求人员</span>
                <span :class="{active:currentName === 'devEmp'}" @click="tabChange('devEmp')">开发人员</span>
                <span :class="{active:currentName === 'testEmp'}" @click="tabChange('testEmp')">测试人员</span>
                <span :class="{active:currentName === 'operationEmp'}" @click="tabChange('operationEmp')">运维人员</span>
                <span :class="{active:currentName === 'otherEmp'}" @click="tabChange('otherEmp')">其他</span>
            </div>
        </div>

        <div class="dmp_report_warp filter_tree">
            <div class="filter_warp filter_col_4">

                <div class="filter_col">
                    <label>部门</label>
                    <div class="filter_component">
                        <dept-multi-select ref="multitreeRef" :is-all-select="isAllSelect">
                        </dept-multi-select>
                    </div>
                </div>

                <div class="filter_col" >
                    <label>项目集</label>
                    <div class="filter_component">
                        <i-Select class="prj_select_class" ref="prj" v-model="selectPrjId" placeholder="请选择项目集" clearable filterable @on-change="onPrjChanged"
                                  :remote-method="queryPrjInfo" :loading="queryPrjLoading">
                            <i-Option v-for="prj in searchPrjList" :value="prj.cid" :key="prj.cid">{{ prj.codeName }}/{{ prj.name }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>月份</label>
                    <div class="filter_component">
                        <Date-Picker type="month" v-model="selectMonth" format="yyyy-MM" @on-change="yearChanged"
                                     placeholder="请选择月份" style="width: 100%;"></Date-Picker>
                    </div>
                </div>

                <div class="filter_col">
                    <label>数据状态</label>
                    <div class="filter_component">
                        <i-Select v-model="selectStd" placeholder="请选择数据状态" clearable filterable>
                            <i-Option v-for="item in stdList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>CC</label>
                    <div class="filter_component">
                        <i-Select v-model="selectCCId" placeholder="请选择CC" clearable filterable>
                            <i-Option v-for="item1 in ccCodeList" :value="item1.ccId" :key="item1.ccId">{{ item1.ccId + "/" + item1.ccNameCn }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>人员</label>
                    <div class="filter_component">
                        <lks-load-user-fuzzy :user.sync="selectUsers" :include-quit="true" :user-flag="true"></lks-load-user-fuzzy>
                    </div>
                </div>

                <div class="filter_col">
                    <label>数据类型</label>
                    <div class="filter_component">
                        <i-Select v-model="selectDataOrigin" placeholder="请选择数据类型" clearable filterable :disabled="selectStd != '已生效(含DMP-已审批和非DMP-已审批)'">
                            <i-Option v-for="item in dataOriginList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col">
                    <label>导入人</label>
                    <div class="filter_component">
                        <lks-load-user-fuzzy :user.sync="importUser"></lks-load-user-fuzzy>
                    </div>
                </div>

                <div class="view_button">
                    <i-button type="primary" @click="queryData">查询</i-button>
                    <i-button type="primary" @click="uploadModal = true" >导入</i-button>
                    <i-button type="primary" @click="exportData" :disabled="!exportAble">导出</i-button>
                </div>
            </div>
            <div class="data_area" ref="dataArea">
                <i-table row-key="name" border stripe :columns="columns" :data="tableDatas" :loading="tableLoading"
                         :height="tableHeight" class="view_table table-noborder lineClamp1"
                         @on-selection-change="handleSelectionChange"
                         @on-row-click="selectRow">
                </i-table>
                <div style="display: flex">
                    <div>
                        <Page
                            :total="queryDataTotal"
                            show-sizer class="mr-top10 page-select view_page"
                            @on-change="onPageNumChange"
                            @on-page-size-change="onPageSizeChange"
                            :page-size="pageSize"
                            :page-size-opts="pageSizeOpts"
                            :current="pageNum + 1"
                            show-total>
                        </Page>
                    </div>
                    <%--<div class="tipStyle">说明：指标值为NaN，表示未获取到数据，该指标不参与计算！</div>--%>
                </div>

            </div>
        </div>
    </div>


    <Modal v-model="uploadModal" title="导入度量数据" width="500px" :styles="{top: '160px'}">
        <%--<div style="display: flex;align-items: center;margin-bottom: 8px;">
            <label style="width: 70px">度量月份</label>
            <Date-Picker type="month" v-model="selectImportMonth" format="yyyy-MM" @on-change="yearChanged"
                         placeholder="请选择月份" style="width: 100%;"></Date-Picker>
        </div>--%>
        <Upload type="drag" :on-success="importSuccess" :on-error="importFail" :before-upload="beforeImport"
                :show-upload-list="false" :action="importGroupUserUrl">
            <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或将文件拖拽到这里上传</p>
            </div>
        </Upload>
        <div slot="footer">
            <Button style="border: none;background: none;display: inline-block;float: left;" type="primary">
                <a :href="downloadHref" style="color: #3883e5;font-size: 16px;">下载导入模板</a>
            </Button>
            <i-button type="info" @click="uploadModal = false">关闭</i-button>
        </div>
    </Modal>

</div>
</body>
<script src="../../biz/bscPrjEffectEvaluation/baseIndexDataImport.js"></script>
</html>
