package com.linkus.biz.testcasemgt.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCnBt;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.*;
import com.linkus.biz.ai.LLmContants;
import com.linkus.biz.ctrl.model.vo.BizP0Constants;
import com.linkus.biz.ctrl.service.IBizService;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.dao.IBizDao;
import com.linkus.biz.testcasemgt.service.ITestCaseMgtService;
import com.linkus.biz.testcasemgt.vo.TestCaseMgtSearchVo;
import com.linkus.biz.testcasemgt.vo.TestCaseMgtVo;
import com.linkus.biz.testcasemgt.vo.TestcaseMgtAiGenVo;
import com.linkus.common.web.CommonResult;
import com.linkus.sys.dao.IFileDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeFile;
import com.linkus.sys.model.po.TeFile2User;
import com.linkus.sys.service.ISysDefService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.commonmark.node.AbstractVisitor;
import org.commonmark.node.FencedCodeBlock;
import org.commonmark.node.IndentedCodeBlock;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Service
public class TestCaseMgtServiceImpl implements ITestCaseMgtService {

    public static final ObjectId TEST_SYSTEM = new ObjectId("5eccd4c1e0ee7742aa72c992");
    @Autowired
    private IBizDao bizDao;
    @Autowired
    private IBizService bizService;
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    public FastDFSClient fastDFSClient;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IFileDao fileDao;

    @Value("${file.parser.serve.url}")
    private String fileParseServeUrl;

    @Override
    public PageBean queryTestCase(TestCaseMgtSearchVo param) {
        // 产品
        if (StringUtil.isNull(param.getPrdId())) {
            throw BusinessException.initExc("产品id为空");
        }

        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        getQueryConds(param, conds);
        long count = bizDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count > 0) {
            List<DbFieldName> fieldNames = new ArrayList<DbFieldName>();
            fieldNames.add(DFN.biz_prd);
            fieldNames.add(DFN.biz_prdCtlg);
            fieldNames.add(DFN.common_code);
            fieldNames.add(DFN.common_name);
            fieldNames.add(DFN.common_desc);
            fieldNames.add(DFN.biz_custFieldInfo);
            fieldNames.add(DFN.biz_priority);
            fieldNames.add(DFN.common_addUser);
            fieldNames.add(DFN.common_addTime);
            fieldNames.add(DFN.biz_linkedBizs);
            fieldNames.add(DFN.biz_prj);
            Pager pager = null;
            int startIndex = 0;
            TestCaseMgtSearchVo.Page page = param.getPage();
            if (Objects.nonNull(page)) {
                int pageNum = page.getPageNum();
                int pageSize = page.getPageSize();
                startIndex = pageNum * pageSize;
                pager = new Pager(pageNum, pageSize);
            }
            // 排序
            Sort sort = Sort.by(Direction.DESC, DFN.biz_addTime.n());
            // 查询数据
            List<TeBiz> bizs = bizDao.findByFieldAndConds(conds, fieldNames, sort, pager);
            if (null == bizs || bizs.isEmpty()) {
                return pageBean;
            }
            List<ObjectId> prdCtlgIdList = new ArrayList<ObjectId>();
            for (TeBiz teBiz : bizs) {
                if (null != teBiz.getPrdCtlg() && StringUtil.isNotNull(teBiz.getPrdCtlg().getCid())) {
                    prdCtlgIdList.add(teBiz.getPrdCtlg().getCid());
                }
            }
            Map<String, String> parentFullPathMap = bizService.getParentDefPath(prdCtlgIdList);
            List<TestCaseMgtVo> bizTestCaseVoList = new ArrayList<>();
            for (TeBiz teBiz : bizs) {
                TestCaseMgtVo vo = new TestCaseMgtVo();
                startIndex++;
                vo.setRowNo(startIndex);
                vo.setBizId(teBiz.getId());
                vo.setCasePrd(teBiz.getPrd().getName());
                if (null != teBiz.getPrdCtlg() && StringUtil.isNotNull(teBiz.getPrdCtlg().getCid())) {
                    // 全路径
                    vo.setCaseCtlg(parentFullPathMap.get(teBiz.getPrdCtlg().getCid().toHexString()));
                }
                vo.setCaseCode(teBiz.getCode());
                List<TeIdNameCnBt> linkedBizs = teBiz.getLinkedBizs();
                // 关联业务
                if (CollectionUtils.isNotEmpty(linkedBizs)) {
                    List<String> linkedBizNameList = new ArrayList<>();
                    for (TeIdNameCnBt linkedBiz : linkedBizs) {
                        if (linkedBiz.getIsValid() == null || linkedBiz.getIsValid()) {
                            linkedBizNameList.add(linkedBiz.getCodeName());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(linkedBizNameList)) {
                        vo.setLinkedBizName(StringUtil.listToString(linkedBizNameList, ','));
                    }
                }
                vo.setCaseName(teBiz.getName());
                vo.setCaseDesc(teBiz.getDesc());
                Map<ObjectId, Object> custFieldInfo = teBiz.getCustFieldInfo();
                if (null != custFieldInfo) {
                    vo.setTestEnvironment(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_TEST_ENVIRONMENT), String.class));
                    vo.setTestScene(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_TEST_SCENE), String.class));
                    vo.setCaseLevel(getListToResultName(custFieldInfo.get(SysDefConstants.FIELD_ID_CASE_LEVEL)));
                    vo.setTestType(getListToResultName(custFieldInfo.get(SysDefConstants.FIELD_ID_TEST_TYPE)));
                    vo.setCaseKeyWord(StringUtil.getNotNullStr(custFieldInfo.get(SysDefConstants.FIELD_ID_CASE_KEY_WORD)));
                    vo.setPreCondition(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_PRE_CONDITION), String.class));
                    vo.setTestImport(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_TEST_IMPORT), String.class));
                    vo.setTestStep(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_TEST_STEP), String.class));
                    String desiredOutput = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_DESIRED_OUTPUT), String.class);
                    String expectResult = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_EXPECT_RESULT), String.class);
                    vo.setTestOutput(StringUtils.isNotEmpty(desiredOutput) ? desiredOutput : expectResult);
                    vo.setCaseEditor(getListToResultName(custFieldInfo.get(SysDefConstants.FIELD_ID_CASE_EDITOR)));
                    vo.setCaseApproveStatus(getListToResultName(custFieldInfo.get(SysDefConstants.FIELD_ID_APPROVAL_STATUS)));
                }
                vo.setCaseAddUser(null != teBiz.getAddUser() ? teBiz.getAddUser().getUserName() + "/" + teBiz.getAddUser().getLoginName() : "");
                vo.setCaseAddTime(DateUtil.formatDate2Str(teBiz.getAddTime(), DateUtil.DATE_FORMAT));
                vo.setPrj(teBiz.getPrj());
                bizTestCaseVoList.add(vo);
            }
            pageBean.setObjectList(bizTestCaseVoList);
        }

        return pageBean;
    }

    /**
     * 获取查询条件
     *
     * @param param
     * @param conds
     */
    private void getQueryConds(TestCaseMgtSearchVo param, List<IDbCondition> conds) {
        conds.add(new DC_E(DFN.common_isValid, true));

        ObjectId prdId = StringUtil.toObjectId(param.getPrdId());
        conds.add(new DC_E(DFN.biz_prd.dot(DFN.common_cid), prdId));
        // 提出时间
        if (StringUtil.isNotNull(param.getBizAddStartTime()) && StringUtil.isNotNull(param.getBizAddEndTime())) {
            Date startDate = DateUtil.parseDate(param.getBizAddStartTime() + DateUtil.DAY_START, DateUtil.DATETIME_FORMAT);
            Date endDate = DateUtil.parseDate(param.getBizAddEndTime() + DateUtil.DAY_DEADLINE, DateUtil.DATETIME_FORMAT);
            conds.add(new DC_B(DFN.common_addTime, startDate, endDate));
        }
        // 产品目录
        if (CollectionUtils.isNotEmpty(param.getPrdCtlgs())) {
            // 产品目录 多选
            List<ObjectId> prdCtlgIds = param.getPrdCtlgs();
            List<ObjectId> newCatalogSet = new ArrayList<>();
            if (null != prdCtlgIds && !prdCtlgIds.isEmpty()) {
                for (ObjectId prdCtlgId : prdCtlgIds) {
                    if (!newCatalogSet.contains(prdCtlgId)) {
                        newCatalogSet.add(prdCtlgId);
                    }
                }
            }
            // 包含后代目录
            Set<ObjectId> selectPrdCtlgIds = new HashSet<>();
            List<SysDef> selectedPrdCtlgDefs;
            if (CollectionUtils.isNotEmpty(prdCtlgIds)) {
                selectedPrdCtlgDefs = sysDefService.getSysDefByAncestors(newCatalogSet);
            } else {
                selectedPrdCtlgDefs = sysDefService.getSysDefsBySrc(prdId, SysDefTypeCodeName.PRDCTLG);
            }
            if (selectedPrdCtlgDefs != null && selectedPrdCtlgDefs.size() > 0) {
                for (SysDef prdCtlgDef : selectedPrdCtlgDefs) {
                    selectPrdCtlgIds.add(prdCtlgDef.getId());
                }
            }
            conds.add(new DC_I<>(DFN.biz_prdCtlg.dot(DFN.common_cid), new ArrayList<ObjectId>(selectPrdCtlgIds)));
        }
        // 业务类型
        conds.add(new DC_E(DFN.biz_bizType.dot(DFN.common_cid), SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT));
        // 评审状态
        ObjectId reviewStatusId = StringUtil.toObjectId(param.getReviewStatus());
        if (reviewStatusId != null) {
            conds.add(new DC_E(DFN.biz_custFieldInfo.dot(BizP0Constants.APPROVAL_STATUS).dot(DFN.common_cid), reviewStatusId));
        }
        // 提出人
        ObjectId addUserId = StringUtil.toObjectId(param.getAddUser());
        if (addUserId != null) {
            conds.add(new DC_E(DFN.biz_addUser.dot(DFN.common_userId), addUserId));
        }
        // 用例名称/用例关键词
        String codeName = StringUtil.getNotNullStr(param.getCodeName());
        if (StringUtils.isNotEmpty(codeName)) {
            List<IDbCondition> codeNameOrConds = new ArrayList<>();
            codeNameOrConds.add(new DC_L(DFN.biz_name, codeName));
            codeNameOrConds.add(new DC_L(DFN.biz_custFieldInfo.dot(SysDefConstants.FIELD_ID_CASE_KEY_WORD), codeName));
            conds.add(new DC_OR(codeNameOrConds));
        }
        // 关联业务
        ObjectId linkedBizId = StringUtil.toObjectId(param.getLinkedBiz());
        if (linkedBizId != null) {
            conds.add(new DC_E(DFN.biz_linkedBizs.dot(DFN.common_cid), linkedBizId));
        }

        ObjectId prjId = StringUtil.toObjectId(param.getPrjId());
        if (prjId != null) {
            conds.add(new DC_E(DFN.biz_prj.dot(DFN.common_cid), prjId));
        }
    }

    @Override
    public LinkedHashMap<String, List<LinkedHashMap<String, Object>>> exporTestCase(Map<String, Object> param) {
        TestCaseMgtSearchVo vo = new TestCaseMgtSearchVo();
        vo.setPrdId(StringUtil.toObjectId(param.get("prdId")));
        vo.setPrdCtlgs(getParamList(param.get("prdCtlgs")));
        vo.setBizAddStartTime(StringUtil.getNotNullStr(param.get("bizAddStartTime")));
        vo.setBizAddEndTime(StringUtil.getNotNullStr(param.get("bizAddEndTime")));
        vo.setReviewStatus(StringUtil.toObjectId(param.get("reviewStatus")));
        vo.setAddUser(StringUtil.toObjectId(param.get("addUser")));
        vo.setCodeName(StringUtil.getNotNullStr(param.get("codeName")));
        vo.setLinkedBiz(StringUtil.toObjectId(param.get("linkedBiz")));
        vo.setPrjId(StringUtil.toObjectId(param.get("prjId")));
        vo.setPage(StringUtil.to(param.get("page"), TestCaseMgtSearchVo.Page.class));

        PageBean data = queryTestCase(vo);
        List<TestCaseMgtVo> reportList = data.getObjectList();
        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> excelMap = new LinkedHashMap<String, List<LinkedHashMap<String, Object>>>();
        List<LinkedHashMap<String, Object>> list = new ArrayList<LinkedHashMap<String, Object>>();
        LinkedHashMap<String, Object> titleMap = new LinkedHashMap<String, Object>();
        int index = 0;
        titleMap.put(String.valueOf(++index), "序号");
        titleMap.put(String.valueOf(++index), "产品名称");
        titleMap.put(String.valueOf(++index), "产品目录");
        titleMap.put(String.valueOf(++index), "项目");
        titleMap.put(String.valueOf(++index), "用例编号");
        titleMap.put(String.valueOf(++index), "关联业务");
        titleMap.put(String.valueOf(++index), "测试用例名称");
        titleMap.put(String.valueOf(++index), "用例描述/测试目的");
        titleMap.put(String.valueOf(++index), "测试环境");
        titleMap.put(String.valueOf(++index), "测试场景");
        titleMap.put(String.valueOf(++index), "用例级别");
        titleMap.put(String.valueOf(++index), "测试类型");
        titleMap.put(String.valueOf(++index), "用例关键字");
        titleMap.put(String.valueOf(++index), "预设条件");
        titleMap.put(String.valueOf(++index), "测试输入");
        titleMap.put(String.valueOf(++index), "测试步骤");
        titleMap.put(String.valueOf(++index), "期望输出/预期结果");
        titleMap.put(String.valueOf(++index), "用例编写人");
        titleMap.put(String.valueOf(++index), "评审状态");
        titleMap.put(String.valueOf(++index), "提出人");
        titleMap.put(String.valueOf(++index), "提出日期");
        list.add(titleMap);
        int num = 1;
        if (null != reportList && !reportList.isEmpty()) {
            for (int i = 0, len = reportList.size(); i < len; i++) {
                TestCaseMgtVo report = reportList.get(i);
                LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<String, Object>();
                // 序号
                linkedMap.put("index", num);
                // 产品名称
                linkedMap.put("prd", report.getCasePrd());
                // 模块
                linkedMap.put("prdCtlg", report.getCaseCtlg());
                linkedMap.put("prj", report.getPrj() == null ? "" : report.getPrj().getName());
                // 用例编号
                linkedMap.put("code", report.getCaseCode());
                // 关联业务
                linkedMap.put("linkedBizName", report.getLinkedBizName());
                // 测试用例名称
                linkedMap.put("caseName", report.getCaseName());
                // 用例描述/测试目的
                linkedMap.put("caseDesc", report.getCaseDesc());
                // 测试环境
                linkedMap.put("testEnvironment", report.getTestEnvironment());
                // 测试场景
                linkedMap.put("testScene", report.getTestScene());
                // 用例级别
                linkedMap.put("caseLevel", report.getCaseLevel());
                // 测试类型
                linkedMap.put("testType", report.getTestType());
                // 用例关键字
                linkedMap.put("caseKeyWord", report.getCaseKeyWord());
                // 预设条件
                linkedMap.put("preCondition", report.getPreCondition());
                // 测试输入
                linkedMap.put("testImport", report.getTestImport());
                // 测试步骤
                linkedMap.put("testStep", report.getTestStep());
                // 期望输出/预期结果
                linkedMap.put("testOutput", report.getTestOutput());
                // 用例编写人
                linkedMap.put("caseEditor", report.getCaseEditor());
                // 评审状态
                linkedMap.put("caseApproveStatus", report.getCaseApproveStatus());
                // 提出人
                linkedMap.put("caseAddUser", report.getCaseAddUser());
                // 提出日期
                linkedMap.put("caseAddTime", report.getCaseAddTime());
                list.add(linkedMap);
                num++;
            }
        }
        excelMap.put("测试用例管理", list);
        return excelMap;
    }

    private String getListToResultName(Object data) {
        if (data == null) {
            return null;
        }
        if (!(data instanceof List)) {
            return null;
        }
        List<String> dataList = new ArrayList<>();
        List<Object> dataObjList = StringUtil.to(data, List.class);
        if (CollectionUtils.isEmpty(dataObjList)) {
            return null;
        }
        for (Object dataObj : dataObjList) {
            if (dataObj instanceof String) {
                dataList.add(StringUtil.to(dataObj, String.class));
            } else if (dataObj instanceof TeIdName) {
                TeIdName idName = StringUtil.to(dataObj, TeIdName.class);
                if (idName == null || StringUtils.isEmpty(idName.getName())) {
                    continue;
                }
                dataList.add(idName.getName());
            } else if (dataObj instanceof TeUser) {
                TeUser idName = StringUtil.to(dataObj, TeUser.class);
                if (idName == null || StringUtils.isEmpty(idName.getUserName())) {
                    continue;
                }
                dataList.add(idName.getUserName() + "/" + idName.getLoginName());
            }
        }
        return StringUtil.join(dataList, ",");
    }

    private List<ObjectId> getParamList(Object data) {
        if (data == null) {
            return null;
        }
        if (!(data instanceof List)) {
            return null;
        }
        List<ObjectId> dataList = new ArrayList<>();
        if (data != null && data instanceof List) {
            List<Object> dataObjList = StringUtil.to(data, List.class);
            if (CollectionUtils.isNotEmpty(dataObjList)) {
                for (Object dataObj : dataObjList) {
                    dataList.add(StringUtil.toObjectId(dataObj));
                }
            }
        }
        return dataList;
    }

    @Override
    public List<Map> aiGenTestcases(TestcaseMgtAiGenVo vo) {
        log.info("-----------------------处理用户输入需求开始-----------------------");
        long start = System.currentTimeMillis();
        String llm = vo.getLlm();
        String url;
        if (LLmContants.Model.QWEN_PLUS.equals(llm) ||
                LLmContants.Model.QWEN2_5_14B.equals(llm) ||
                LLmContants.Model.QWEN2_5_72B.equals(llm) ||
                LLmContants.Model.QWEN3_32B.equals(llm)) {
            url = "http://linkus-ai/linkus-ai/ai/lsm/chat/general";
        } else {
            throw BusinessException.initExc("暂不支持的大模型");
        }

        ObjectId dialogId = vo.getDialogId();
        if (dialogId == null) {
            throw BusinessException.initExc("未获取会话信息");
        }

        // 所有的需求描述拼接
        Integer testcaseCount = vo.getTestcaseCount();
        StringBuilder requirementBuilder = new StringBuilder("\n用户需求如下，请根据用户输入生成" + (testcaseCount != null && testcaseCount > 0 ? testcaseCount + "个" : "") + "测试用例：\n");
        // 需求描述
        boolean hasRequirement = false;
        int reqNo = 1;
        String requirement = vo.getRequirement();
        if (StringUtil.isNotNull(requirement)) {
            hasRequirement = true;
            requirementBuilder.append("- 需求" + reqNo + "信息：").append("\n- 描述：\n").append(requirement).append("\n");
            reqNo++;
        }

        List<ObjectId> bizFileIds = new ArrayList<>();
        // 选择需求
        List<ObjectId> bizIds = vo.getBizIds();
        if (CollectionUtils.isNotEmpty(bizIds)) {
            List<TeBiz> bizs = bizDao.getBizsByIds(bizIds);
            if (CollectionUtils.isEmpty(bizs)) {
                throw BusinessException.initExc("业务不存在");
            }
            for (TeBiz biz : bizs) {
                requirementBuilder.append("\n" + "- 需求" + reqNo + "信息：\n");
                if (StringUtil.isNotNull(biz.getName())) {
                    requirementBuilder.append("- 名称:\n").append(biz.getName()).append("\n");
                }

                if (StringUtil.isNotNull(biz.getDesc())) {
                    hasRequirement = true;
                    requirementBuilder.append("- 描述:\n").append(biz.getDesc()).append("\n");
                }
                requirementBuilder.append("\n");
                reqNo++;
                // 查询业务附件信息
                List<ObjectId> fids = biz.getFile();
                if (CollectionUtils.isNotEmpty(fids)) {
                    bizFileIds.addAll(fids);
                }
            }
        }

        List<ObjectId> fileIds = vo.getFileIds();
        if (CollectionUtils.isNotEmpty(bizFileIds)) {
            fileIds = bizFileIds;
        }

        // 下载需求文件，并解析出文本信息
        if (CollectionUtils.isNotEmpty(fileIds)) {
            List<TeFile> files = fileDao.getFilesbyIds(fileIds);
            if (CollectionUtils.isNotEmpty(files)) {
                for (TeFile file : files) {
                    String filePath = file.getFilePath();
                    StorePath storePath = StorePath.parseFromUrl(filePath);
                    byte[] fileData = fastDFSClient.downloadFile(storePath.getFullPath());
                    // 调用HTTP接口解析文档内容
                    if (fileData == null) {
                        continue;
                    }
                    String fileName = file.getFileName();

                    log.warn("开始解析文档内容：fileId:{}, fileName:{}", file.getId().toString(), fileName);

                    // 使用远程转换服务解析
                    String fileParseUrl = fileParseServeUrl + "/api/v1/convert";
                    Map<String, String> headers = new HashMap<>();
                    Map<String, Object> params = new HashMap<>();
                    params.put("include_images", false);
                    params.put("include_styles", false);
                    String fileParseResponse = HttpRequest.uploadFile(fileParseUrl, fileName, fileData, headers, params);
                    log.warn("解析文档fileId:{}, fileName:{}， 返回结果:{}", file.getId().toString(), fileName, fileParseResponse);

                    // 尝试解析为JSON
                    if (StringUtil.isNotNull(fileParseResponse)) {
                        try {
                            Map<String, Object> jsonMap = JsonUtil.toMap(fileParseResponse);
                            if (jsonMap.containsKey("html_content")) {
                                String fileContent = (String) jsonMap.get("html_content");
                                if (StringUtil.isNotNull(fileContent)) {
                                    hasRequirement = true;
                                    requirementBuilder.append("\n" + "- 需求" + reqNo + "信息：\n").append("- 描述：\n").append(fileContent).append("\n");
                                    reqNo++;
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析文档fileId:{}, fileName:{}， 解析为JSON失败", file.getId().toString(), fileName, e);
                        }
                    }
                }
            }
        }

        log.info("-----------------------处理用户输入需求结束，耗时：{}ms-----------------------", (System.currentTimeMillis() - start));
        if (!hasRequirement) {
            throw BusinessException.initExc("请输入需求描述");
        }
        requirementBuilder.append("\n");
        // 设置需求描述
        vo.setRequirement(requirementBuilder.toString());
        log.info("-----------------------用户输入需求：{}-----------------------", requirementBuilder);

        String prompt = vo.getPrompt();
        log.info("-----------------------用户提示词：{}-----------------------", prompt);
        log.info("-----------------------调用大模型生成测试用例接口开始-----------------------");
        start = System.currentTimeMillis();
        Boolean useHistoryDialog = vo.getUseHistoryDialog();
        // 构建请求实体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("dialogId", vo.getDialogId().toString());
        requestBody.put("aiSceneId", SysDefConstants.AI_SCENE_TEST_CASE_GEN_ID.toString());
        requestBody.put("request", vo.getRequirement());
        requestBody.put("prompt", prompt);
        requestBody.put("useHistoryDialog", false);
        requestBody.put("model", llm);
        requestBody.put("stream", false);
        requestBody.put("isAutoSavePrompt", true);
        CommonResult response = null;
        try {
            response = restTemplate.postForEntity(url, requestBody, CommonResult.class).getBody();
        } catch (Exception e) {
            throw BusinessException.initExc(e.getMessage());
        }
        if (response == null) {
            throw BusinessException.initExc("未获取到大模型响应信息");
        }

        if (!response.isSuccess()) {
            String message = response.getMessage();
            if (StringUtil.isNull(message)) {
                message = "未获取到大模型响应信息";
            }
            throw BusinessException.initExc(message);
        }

        Object data = response.getData();
        if (data == null) {
            throw BusinessException.initExc("未获取到大模型响应信息");
        }
        Map<String, Object> responseData = (Map<String, Object>) data;
        String responseText = responseData.containsKey("content") ? String.valueOf(responseData.get("content")) : null;
        if (StringUtil.isNull(responseText)) {
            throw BusinessException.initExc("未获取到大模型响应信息");
        }
        log.info("-----------------------调用大模型生成测试用例接口结束，获取大模型响应耗时：{}ms-----------------------", (System.currentTimeMillis() - start));
        log.info("-----------------------调用大模型生成测试用例接口结束，获取大模型响应：\n{}\n-----------------------", responseText);
        start = System.currentTimeMillis();
        log.info("-----------------------开始处理大模型响应结果-----------------------");
        List<String> strs = extractCode(responseText);
        List<Map> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(strs)) {
            if (StringUtil.isNotNull(responseText)) {
                // 尝试按照json解析
                List<Map> list = JsonUtil.toList(responseText, Map.class);
                if (CollectionUtils.isNotEmpty(list)) {
                    result.addAll(list);
                }
            }
        } else {
            for (String str : strs) {
                List<Map> list = JsonUtil.toList(str, Map.class);
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                result.addAll(list);
            }
        }

        // 处理结果
        Map<String, String> keysMap = new HashMap<>();
        keysMap.put("用例名称", "name");
        keysMap.put("测试步骤", "steps");
        keysMap.put("前置条件", "preConditions");
        keysMap.put("预期结果", "expectedResults");
        keysMap.put("优先级", "priority");
        keysMap.put("用例编号", "caseCode");

        // 将result中的key替换为keysMap中相同key的value
        // 并且删除原来的key
        for (Map map : result) {
            for (Map.Entry<String, String> entry : keysMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                map.put(value, map.get(key));
                map.remove(key);
            }
        }
        log.info("-----------------------处理大模型响应结果结束，耗时：{}ms-----------------------", (System.currentTimeMillis() - start));
        return result;
    }


    @Override
    public ObjectId uploadRequirementFile(byte[] fileData, String fileName, TeUser uploadUser) {
        String filePath = fastDFSClient.uploadFile(fileData, fileName, null);
        // 保存到file集合
        TeFile teFile = new TeFile();
        teFile.setFileName(fileName);
        teFile.setFileSize(Double.valueOf(fileData.length));
        teFile.setFilePath(filePath);
        teFile.setIsValid(true);
        TeFile2User teFile2User = new TeFile2User();
        teFile2User.setUserId(uploadUser.getUserId());
        teFile2User.setUserName(uploadUser.getUserName());
        teFile2User.setLoginName(uploadUser.getLoginName());
        teFile.setUploadUser(teFile2User);
        teFile.setUploadTime(new Date());
        fileDao.insert(teFile);
        return teFile.getId();
    }

    private List<String> extractCode(String markdown) {
        try {
            Parser parser = Parser.builder().build();
            Node document = parser.parse(markdown);
            CodeVisitor visitor = new CodeVisitor();
            document.accept(visitor);
            return visitor.getCodes();
        } catch (Exception e) {
            log.error("解析模型响应异常：{}", e.getMessage());
            throw BusinessException.initExc("解析大模型响应markdown异常");
        }
    }

    private class CodeVisitor extends AbstractVisitor {
        private List<String> codes = new ArrayList<>();

        @Override
        public void visit(FencedCodeBlock code) {
            log.info("FencedCodeBlock..........");
            String literal = code.getLiteral();
            codes.add(literal);
            visitChildren(code);
        }

        @Override
        public void visit(IndentedCodeBlock code) {
            log.info("IndentedCodeBlock..........");
            String literal = code.getLiteral();
            codes.add(literal);
            visitChildren(code);
        }

        public List<String> getCodes() {
            return codes;
        }
    }
}
