package com.linkus.biz.report.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

@Data
public class ReqDemandBugOverviewVo {

	/**
	 * 目录ID
	 */
	@ExcelIgnore
	private ObjectId prdCtlgId;
	//业务id
	@ExcelIgnore
	private ObjectId bizId;
	@ExcelProperty("系统模块")
	private String prdCtlg;
	@ExcelProperty("需求名称")
	private String bizName;
	/**
	 * 排序no
	 */
	@ExcelIgnore
	private Integer uniNo;
	@ExcelProperty({"需求明细","功能点总数"})
	private int funPointCount;
	@ExcelProperty({"需求明细","开发完成数"})
	private int funPointDevedCount;
	@ExcelProperty({"需求明细","开发完成率"})
	private String funPointDevedRate;
	@ExcelProperty({"需求明细","开发中"})
	private int funPointDevingCount;
	@ExcelProperty({"需求明细","测试完成数"})
	private int funPointTestedCount;
	//功能测试中且有关联bug
	@ExcelIgnore
	private int funPointTestingWithBugCount;
	@ExcelProperty({"需求明细","测试完成率"})
	private String funPointTestedRate;
	@ExcelProperty({"需求明细","测试覆盖率"})
	private String funPointTestCoverRate;
	@ExcelProperty({"需求明细","执行通过率"})
	private String funPointTestPassRate;
	@ExcelProperty({"需求明细","测试中"})
	private int funPointTestingCount;
	@ExcelProperty({"需求明细","开发延期数"})
	private int funPointDevDelayCount;
	@ExcelProperty({"需求明细","测试延期数"})
	private int funPointTestDelayCount;
	@ExcelProperty({"需求明细","测试延期数(未关联BUG)"})
	private int funPointTestDelayCount2;
}
