package com.linkus.biz.ctrl.ai.service;

import com.linkus.biz.ctrl.ai.vo.ChatResponseVO;

public interface IAiPageFillService {
    /**
     * 根据需求文档和业务系统字段信息生成页面填充数据
     * @param requirementDoc 需求文档内容
     * @param pageFields 页面字段信息
     * @param model
     * @param tokenValue
     * @param dialogId
     */
    ChatResponseVO generatePageFillData(String requirementDoc, String pageFields, String model, String tokenValue, String dialogId);

    ChatResponseVO relateBiz(String name, String desc, String allBiz,String model);
}
