package com.linkus.biz.ctrl.ai.ctrl;

import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.shiro.JwtUtil;
import com.linkus.biz.ctrl.ai.service.IBizDocumentPageFillService;
import com.linkus.biz.ctrl.ai.vo.TeIdNameDesc;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/biz/ai/page-fill")
public class BizDocumentPageFillCtrl extends CommonController {
    @Autowired
    private IBizDocumentPageFillService bizDocumentPageFillService;
    /**上传并分析文档*/
    @PostMapping("/generate")
    public CommonResult<Map<String, Map<String, TeIdNameDesc>>> generatePageFillData(@RequestParam(value = "file",required = false) MultipartFile file, @RequestParam("prdId") ObjectId prdId,
                                                                                   @RequestParam("catalogId")ObjectId catalogId , @RequestParam("bizTypeId")ObjectId bizTypeId  ,
                                                                                   @RequestParam(value = "content",required = false)String content,
                                                                                   @RequestParam(value = "model",required = false)String model,
                                                                                   HttpServletRequest request
                                                                                   )  {
        String tokenValue = JwtUtil.getTokenValue(request);
        return CommonResult.success(bizDocumentPageFillService.generatePageFillDataFromDocument(prdId,catalogId,bizTypeId,file,model,content,tokenValue));
    }

    /**查询关联业务*/
    @GetMapping("/queryRelateBiz")
    public CommonResult<List<TeIdNameDesc>> queryRelateBiz(@RequestParam("prdId")ObjectId prdId , @RequestParam("bizTypeId")ObjectId bizTypeId  ,
                                                           @RequestParam(value = "name",required = false)String name,
                                                           @RequestParam(value = "desc",required = false)String desc,
                                                           @RequestParam(value = "model",required = false)String model)  {
        return CommonResult.success(bizDocumentPageFillService.queryRelateBiz(prdId,bizTypeId,name,desc, model));
    }



//
//    @Autowired
//    AiClient client;
//
//    @PostMapping("/demo")
//    public CommonResult<String> demo() throws JsonProcessingException {
//        ChatRequestVO request = ChatRequestBuilder.create()
//                .system("你是一个专业的业务系统页面填充助手，能够根据需求文档和字段信息生成准确的页面填充数据。不要凭空乱造")
//                .message("user", "你是谁")
//                .model("qwen-plus")
//                .build();
//        String s = client.chatFlux(request,"http://127.0.0.1:18115/linkus-ai/general-chat/chat/stream");
//        return CommonResult.success(s);
//    }
}