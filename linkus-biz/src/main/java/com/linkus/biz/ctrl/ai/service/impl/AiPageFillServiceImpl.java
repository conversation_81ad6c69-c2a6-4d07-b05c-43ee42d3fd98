package com.linkus.biz.ctrl.ai.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.shiro.JwtUtil;
import com.linkus.biz.ctrl.ai.service.IAiPageFillService;
import com.linkus.biz.ctrl.ai.util.ai.AiClient;
import com.linkus.biz.ctrl.ai.vo.AiLsmDialogCreateVO;
import com.linkus.biz.ctrl.ai.vo.ChatRequestVO;
import com.linkus.biz.ctrl.ai.vo.ChatResponseVO;
import com.linkus.biz.ctrl.ai.vo.TeAiLsmDialog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

/**
 * AI 服务类
 * 提供各种 AI 功能的业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AiPageFillServiceImpl implements IAiPageFillService {

    @Autowired
    private AiClient aiClient;

    @Value("${create.dialog.url:http://linkus-ai/linkus-ai//ai/lsm/dialog/create}")
    private String dialogUrl;

    /**
     * 根据需求文档和业务系统字段信息生成页面填充数据
     *
     * @param requirementDoc 需求文档内容
     * @param pageFields     页面字段信息
     * @param model
     * @param tokenValue
     * @param dialogId
     * @return 字段和值对应的填充数据
     */
    @Override
    public ChatResponseVO generatePageFillData(String requirementDoc, String pageFields, String model, String tokenValue, String dialogId) {
        try {
            // 构建提示词
            String prompt = String.format(
                    "作为专业的需求文档解析专家，请根据以下软件研发文档和页面字段信息，生成符合JSON格式的页面填充数据：\n\n" +
                            "需求文档：\n%s\n\n" +
                            "页面字段信息：\n%s\n\n" +
                            "请生成严格符合以下JSON结构的字段与值的对应关系：\n" +
                            "[\n" +
                            "  {\n" +
                            "    \"id\": \"fieldName1\",\n" +
                            "    \"name\": \"fieldName1\",\n" +
                            "    \"value\": \"value1\"\n" +
                            "    \"reason\": \"reason1\"\n" +
                            "  },\n" +
                            "  {\n" +
                            "    \"id\": \"fieldName2\",\n" +
                            "    \"name\": \"fieldName2\",\n" +
                            "    \"value\": \"value2\",\n" +
                            "    \"reason\": \"reason2\"\n" +
                            "    \"options\": [{\"id\":\"value2\",\"name\":\"value2\"}]\n" +
                            "  },\n" +
                            "  {\n" +
                            "    \"id\": \"fieldName3\",\n" +
                            "    \"name\": \"fieldName3\",\n" +
                            "    \"options\": [{\"id\":\"option1\",\"name\":\"option1\"}, {\"id\":\"option2\",\"name\":\"option2\"}, {\"id\":\"option3\",\"name\":\"option3\"}]\n" +
                            "  }\n" +
                            "]\n\n" +
                            "处理规则：\n" +
                            "### 核心原则\n" +
//                            "1. 数据真实性：所有内容必须完全来源于提供的需求文档，禁止编造数据，需求文档中没有的数据，无需给出示例值/假设值\n" +
                            "1. value数据真实性：其值必须完全来源于提供的需求文档，禁止编造数据，如果没值直接显示null\n" +
                            "2. 字段验证：每个字段值必须有明确的文档依据，需在文档中标记出处\n" +
                            "3. 格式严格性：输出必须严格符合指定的JSON结构，包括字段顺序和嵌套层级\n" +
                            "4. ID一致性：字段id必须与页面字段信息中的字段ID值完全一致，区分大小写\n" +
                            "\n" +
                            "### 文档解析规则\n" +
                            "1. 结构分析：\n" +
                            "   - 识别文档章节结构：标题级别（H1-H6）、编号体系（1.1, 1.1.1）\n" +
                            "   - 标记关键章节：引言、功能描述、非功能需求、约束条件\n" +
                            "2. 语义识别：\n" +
                            "   - 解析专业术语：需求规格、用例、用户故事、验收标准\n" +
                            "   - 区分功能性需求与非功能性需求\n" +
                            "3. 数据点提取：\n" +
                            "   - 识别明确的数据值：数值、日期、字符串\n" +
                            "   - 提取隐含数据：通过业务规则推导的值\n" +
                            "\n" +
                            "### 关键字段提取细则\n" +
                            "1. 名称（requirementName）：\n" +
                            "   - 提取规则：\n" +
                            "     • 优先提取文档标题行中'名称'字段的值\n" +
//                            "     • 若无明确标注，提取文档主标题（通常为H1级别）\n" +
                            "     • 其次是文档第一行数据\n" +
                            "     • 再次是总结文档内容作为标题\n" +
//                            "     • 去除编号前缀（如'1.2'、'[REQ-001]'）和标签（如'[新增]'）\n" +
//                            "   - 正例：\n" +
//                            "     • 文档标题：'1.2 系统管理-用户权限管理' → 值：'系统管理-用户权限管理'\n" +
//                            "     • 字段标注：'名称：订单管理系统V2.0' → 值：'订单管理系统V2.0'\n" +
//                            "   - 反例：\n" +
//                            "     • 包含编号：'1.2 系统管理-用户权限管理'（错误）\n" +
//                            "     • 截断名称：'用户权限'（原名为'系统管理-用户权限管理'）\n" +
                            "\n" +
                            "2. 需求描述（requirementDescription）：\n" +
                            "   - 提取规则：\n" +
                            "     • 提取'需求描述'、'功能说明'、'详细描述'章节内容\n" +
                            "     • 合并同一主题的多段落描述\n" +
//                            "     • 去除格式标记：HTML标签（<p>）、列表符号（•, -）\n" +
                            "   - 正例：\n" +
                            "     • 文档内容：\n" +
                            "       '2.1 需求描述\n" +
                            "       本功能允许管理员：\n" +
                            "       • 查看用户基本信息\n" +
                            "       • 修改用户权限\n" +
                            "       → 值：'本功能允许管理员：查看用户基本信息，修改用户权限'\n" +
                            "   - 反例：\n" +
                            "     • 遗漏段落：只提取第一段内容\n" +
                            "     • 保留格式：'• 查看用户基本信息'（应去除列表符号）\n" +
                            "\n" +
                            "3. 优先级（priority）：\n" +
                            "   - 提取规则：\n" +
                            "     • 识别字段：'优先级'、'Priority'、'重要性'\n" +
                            "     • 标准化映射：\n" +
                            "       | 文档术语          | 标准值 |\n" +
                            "       |-------------------|--------|\n" +
                            "       | 高、High、重要    | 高     |\n" +
                            "       | 中、Medium、一般  | 中     |\n" +
                            "       | 低、Low、次要     | 低     |\n" +
                            "     • 未明确标注时，默认值：'中'\n" +
                            "   - 正例：\n" +
                            "     • 文档标注：'优先级：High' → 值：'高'\n" +
                            "     • 无标注时 → 值：'中'\n" +
                            "   - 反例：\n" +
                            "     • 未映射：'High'（应映射为'高'）\n" +
                            "     • 错误映射：'Medium' → '高'（应映射为'中'）\n" +
                            "\n" +
                            "4. 紧急程度（urgency）：\n" +
                            "   - 提取规则：\n" +
                            "     • 识别字段：'紧急程度'、'Urgency'、'时间要求'\n" +
                            "     • 标准化映射：\n" +
                            "       | 文档术语          | 标准值 |\n" +
                            "       |-------------------|--------|\n" +
                            "       | 紧急、Urgent、马上| 紧急   |\n" +
                            "       | 高、High、尽快    | 高     |\n" +
                            "       | 中、Medium、正常  | 中     |\n" +
                            "       | 低、Low、稍后     | 低     |\n" +
                            "     • 未明确标注时，默认值：'中'\n" +
                            "   - 正例：\n" +
                            "     • 文档标注：'紧急程度：尽快' → 值：'高'\n" +
                            "     • 无标注时 → 值：'中'\n" +
                            "   - 反例：\n" +
                            "     • 未映射：'尽快'（应映射为'高'）\n" +
                            "     • 错误映射：'低优先级' → '低'（紧急程度与优先级无关）\n" +
                            "\n" +
                            "5. 需求类型（requirementType）：\n" +
                            "   - 提取规则：\n" +
                            "     • 识别字段：'需求类型'、'Type'\n" +
                            "     • 常见类型映射：\n" +
                            "       | 文档术语              | 标准值   |\n" +
                            "       |-----------------------|----------|\n" +
                            "       | 功能需求、功能性需求  | 功能需求 |\n" +
                            "       | 非功能需求、性能需求  | 非功能需求 |\n" +
                            "       | 业务需求              | 业务需求 |\n" +
                            "     • 未明确标注时，默认值：'功能需求'\n" +
                            "   - 正例：\n" +
                            "     • 文档标注：'需求类型：非功能需求' → 值：'非功能需求'\n" +
                            "   - 反例：\n" +
                            "     • 错误映射：'性能需求' → '功能需求'（应映射为'非功能需求'）\n" +
                            "\n" +
                            "6. 需求来源（requirementSource）：\n" +
                            "   - 提取规则：\n" +
                            "     • 识别字段：'需求来源'、'来源'\n" +
                            "     • 常见来源：用户、市场调研、法规、竞品分析\n" +
                            "     • 直接使用文档原文，保持大小写\n" +
                            "   - 正例：\n" +
                            "     • 文档标注：'需求来源：产品经理' → 值：'产品经理'\n" +
                            "   - 反例：\n" +
                            "     • 过度处理：'产品经理' → 'PM'（应保持原文）\n" +
                            "\n" +
                            "### 输出要求\n" +
//                            "1. 输出纯JSON格式，无任何注释或说明文字\n" +
                            "1. 输出纯JSON格式，无任何注释或说明文字，并附加取值理由\n" +
                            "2. 严格遵循提供的JSON结构模板，包括字段顺序\n" +
                            "3. 每个字段需包含完整的id、name、value属性\n" +
                            "4. 选项类型字段需包含options数组，格式为[{\"id\":\"值\",\"name\":\"显示文本\"}]\n",
                    requirementDoc, pageFields
            );

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(JwtUtil.jwtTokenCookieName,tokenValue);
            // 构建聊天请求
            ChatRequestVO build = ChatRequestVO.builder()
                    .dialogId(dialogId)
                    .aiSceneId(SysDefConstants.AI_SCENE_BIZ_ADD_ID.toHexString())
                    .request(prompt)
//                    .prompt("你是一个专业的业务系统页面填充助手，能够根据需求文档和字段信息生成准确的页面填充数据。")
                    .model(model)
                    .stream(false)
                    .build();
            return aiClient.chat(build,headers);
        } catch (Exception e) {
            log.error("生成页面填充数据失败", e);
            throw new RuntimeException("生成页面填充数据失败: " + e.getMessage());
        }
    }

//
//    @Override
//    public List<ChatResponseVO> generatePageFillDataWithRelateBiz(String requirementDoc, String pageFields, String model, String targetBiz) {
//        // 构建提示词
//        String prompt = String.format(
//                "请务必根据以下需求文档和业务系统字段信息，生成对应的页面填充数据：\n\n" +
//                        "需求文档：\n%s\n\n" +
//                        "页面字段信息：\n%s\n\n" +
//                        "请生成JSON格式的字段和值对应关系，格式如下：\n" +
//                        "[\n" +
//                        "  {\n" +
//                        "    \"id\": \"fieldName1\",\n" +
//                        "    \"name\": \"fieldName1\",\n" +
//                        "    \"value\": \"value1\",\n" +
//                        "  },\n" +
//                        "  {\n" +
//                        "    \"id\": \"fieldName2\",\n" +
//                        "    \"name\": \"fieldName2\",\n" +
//                        "    \"value\": \"value2\",\n" +
//                        "    \"options\": [{\"id\":\"value2\",\"name\":\"value2\"}]\n" +
//                        "  },\n" +
//                        "  {\n" +
//                        "    \"id\": \"fieldName3\",\n" +
//                        "    \"name\": \"fieldName3\",\n" +
//                        "    \"options\": [{\"id\":\"option1\",\"name\":\"option1\"}, {\"id\":\"option2\",\"name\":\"option2\"}, {\"id\":\"option3\",\"name\":\"option3\"}]\n" +
//                        "  }\n" +
//                        "]\n\n" +
//                        "规则如下：" +
//                        "## 核心原则\n" +
//                        "1. **绝对禁止编造数据** - 所有生成内容必须百分百源自提供的需求文档\n" +
//                        "2. **字段级验证** - 每个字段值必须有明确的文档依据\n" +
//                        "3. **格式严格性** - 输出必须完全符合指定JSON结构\n" +
//                        "## 输入处理规则\n" +
//                        "1. 仔细分析需求文档中的每个字段描述\n" +
//                        "2. 标记文档中所有可用的数据点和约束条件\n" +
//                        "3. 建立字段与文档内容的映射关系表\n" +
//                        "## 数据生成规范\n" +
//                        "1. **字段处理**：\n" +
//                        "   - 仅处理文档中明确描述的字段\n" +
//                        "   - 未提及的字段一律不生成\n" +
//                        "   - 必填字段若文档无数据则设为null\n" +
//                        "2. **值生成规则**：\n" +
//                        "   - 文本类型：直接引用文档中的描述文本\n" +
//                        "   - 数值类型：使用文档指定的范围和示例值\n" +
//                        "   - 选项类型：仅包含文档列出的选项\n" +
//                        "## 最终输出要求\n" +
//                        "1. 纯JSON格式，无任何附加文本\n" +
//                        "2. 完全遵循提供的结构模板\n",
//                requirementDoc, pageFields
//        );
//
//        String prompt2 = String.format(
//                "请根据以下规则处理文档匹配任务，并返回上述要求所有json数据：\n" +
//                        "原文档：{name:是id为5a43282dba4014a42391fa59对应的value值,desc:是id为5a432876ba4014a42391fa5a对应的value值}" +
//                        "目标文档集：%s" +
//                        "匹配要求如下：" +
//                        "**输入格式要求** \n" +
//                        "原文档：{name: \"关键词\", desc: \"描述文本\"}  \n" +
//                        "目标文档集：[{cid:1, name:\"名称A\", desc:\"描述A\"}, {cid:2, name:\"名称B\", desc:\"描述B\"}, ...]\n" +
//                        "**处理规则**  \n" +
//                        "1. 计算每个目标文档与原文档的复合相关性得分：  \n" +
//                        "   - name字段相似度 × 0.8（使用余弦相似度或Jaccard相似度）  \n" +
//                        "   - desc字段相似度 × 0.2  \n" +
//                        "   - 总分 = name_score*0.8 + desc_score*0.2  \n" +
//                        "2. 输出要求：  \n" +
//                        "   - 保持目标文档原始JSON结构  \n" +
//                        "   - 按复合得分降序排列  \n" +
//                        "   - 必须包含完整cid-name-desc三元组  \n" +
//                        "   - 只返回json结构数据，不返回其它数据 \n" +
//                        "**输出示例**\n" +
//                        "输入原文档：{name:\"苹果\", desc:\"水果\"}  \n" +
//                        "目标文档集：[{cid:1,name:\"苹果公司\",desc:\"科技企业\"}, {cid:2,name:\"红富士\",desc:\"苹果品种\"}]  \n" +
//                        "应返回：  \n" +
//                        "[  \n" +
//                        "  {cid:2, name:\"红富士\", desc:\"苹果品种\"},  // 名称匹配度更高  \n" +
//                        "  {cid:1, name:\"苹果公司\", desc:\"科技企业\"}  \n" +
//                        "]  \n" +
//                        "**特别说明**  \n" +
//                        "1. 若name完全匹配则直接置顶  \n" +
//                        "2. 描述文本需进行停用词过滤后计算相似度  \n" +
//                        "3. 数字cid保持原样不参与计算",
//                targetBiz
//        );
//
//
//        // 构建聊天请求
//        ChatRequestVO request = ChatRequestBuilder.create()
//                .system("你是一个专业的业务系统页面填充助手，能够根据需求文档和字段信息生成准确的页面填充数据。")
//                .message("user", prompt)
//                .message("assistant","以纯JSON格式解答，无任何附加文本")
//
////                .message("user", prompt2)
//                .message("user", "返回上述问题解答的所有数据和如下问题的答案："+prompt2)
//                .message("assistant","请综合回答")
//                .model(model)
//                .build();
//
//        // 调用AI客户端
//        return aiClient.chatList(request);
//    }
//
//    /**
//     * 根据需求文档和业务系统字段信息生成页面填充数据（带验证）
//     *
//     * @param requirementDoc  需求文档内容
//     * @param pageFields      页面字段信息
//     * @param validationRules 字段验证规则
//     * @return 字段和值对应的填充数据
//     */
//    @Override
//    public ChatResponseVO generatePageFillDataWithValidation(String requirementDoc, String pageFields, String validationRules) {
//        try {
//            // 构建带验证的提示词
//            String prompt = String.format(
//                    "请根据以下需求文档和业务系统字段信息，生成对应的页面填充数据：\n\n" +
//                            "需求文档：\n%s\n\n" +
//                            "页面字段信息：\n%s\n\n" +
//                            "字段验证规则：\n%s\n\n" +
//                            "请生成JSON格式的字段和值对应关系，确保所有数值都符合验证规则。\n" +
//                            "如果某个字段无法根据需求文档确定具体数值，请使用null或空字符串。\n\n" +
//                            "生成格式：\n" +
//                            "[\n" +
//                            "  {\n" +
//                            "    \"id\": \"fieldName1\",\n" +
//                            "    \"name\": \"fieldName1\",\n" +
//                            "    \"value\": \"value1\",\n" +
//                            "  },\n" +
//                            "  {\n" +
//                            "    \"id\": \"fieldName2\",\n" +
//                            "    \"name\": \"fieldName2\",\n" +
//                            "    \"value\": \"value2\",\n" +
//                            "    \"options\": [{\"id\":\"value2\",\"name\":\"value2\"}]\n" +
//                            "  },\n" +
//                            "  {\n" +
//                            "    \"id\": \"fieldName3\",\n" +
//                            "    \"name\": \"fieldName3\",\n" +
//                            "    \"value\": \"value3\",\n" +
//                            "    \"options\": [{\"id\":\"option1\",\"name\":\"option1\"}, {\"id\":\"option2\",\"name\":\"option2\"}, {\"id\":\"option3\",\"name\":\"option3\"}]\n" +
//                            "  }\n" +
//                            "]\n\n" +
//                            "请确保生成的数值符合字段的类型和约束条件。",
//                    requirementDoc, pageFields, validationRules
//            );
//
//            // 构建聊天请求
//            ChatRequestVO request = ChatRequestBuilder.create()
//                    .system("你是一个专业的业务系统页面填充助手，能够根据需求文档和字段信息生成准确的页面填充数据，并确保数据符合验证规则。")
//                    .message("user", prompt)
//                    .build();
//
//            // 调用AI客户端
//            return aiClient.chat(request);
//        } catch (Exception e) {
//            log.error("生成页面填充数据（带验证）失败", e);
//            throw new RuntimeException("生成页面填充数据（带验证）失败: " + e.getMessage());
//        }
//    }

    @Override
    public ChatResponseVO relateBiz(String name, String desc, String allBiz, String model) {
        String prompt = String.format(
                "请根据以下规则处理文档匹配任务：\n" +
                        "原文档：{name:%s,desc:%s}" +
                        "目标文档集：%s" +
                        "匹配要求如下：" +
                        "**输入格式要求** \n" +
                        "原文档：{name: \"关键词\", desc: \"描述文本\"}  \n" +
                        "目标文档集：[{cid:1, name:\"名称A\", desc:\"描述A\"}, {cid:2, name:\"名称B\", desc:\"描述B\"}, ...]\n" +
                        "**处理规则**  \n" +
                        "1. 计算每个目标文档与原文档的复合相关性得分：  \n" +
                        "   - name字段相似度 × 0.8（使用余弦相似度或Jaccard相似度）  \n" +
                        "   - desc字段相似度 × 0.2  \n" +
                        "   - 总分 = name_score*0.8 + desc_score*0.2  \n" +
                        "2. 输出要求：  \n" +
                        "   - 保持目标文档原始JSON结构  \n" +
                        "   - 按复合得分降序排列  \n" +
                        "   - 必须包含完整cid-name-desc三元组  \n" +
                        "   - 只返回json结构数据，不返回其它数据 \n" +
                        "**输出示例**\n" +
                        "输入原文档：{name:\"苹果\", desc:\"水果\"}  \n" +
                        "目标文档集：[{cid:1,name:\"苹果公司\",desc:\"科技企业\"}, {cid:2,name:\"红富士\",desc:\"苹果品种\"}]  \n" +
                        "应返回：  \n" +
                        "[  \n" +
                        "  {cid:2, name:\"红富士\", desc:\"苹果品种\"},  // 名称匹配度更高  \n" +
                        "  {cid:1, name:\"苹果公司\", desc:\"科技企业\"}  \n" +
                        "]  \n" +
                        "**特别说明**  \n" +
                        "1. 若name完全匹配则直接置顶  \n" +
                        "2. 描述文本需进行停用词过滤后计算相似度  \n" +
                        "3. 数字cid保持原样不参与计算",
                name, desc, allBiz
        );

        AiLsmDialogCreateVO body = AiLsmDialogCreateVO.builder().dialogName("新增业务").aiSceneId(SysDefConstants.AI_SCENE_BIZ_ADD_ID.toHexString()).build();
        TeAiLsmDialog dialog = aiClient.request(dialogUrl, body, null,new ParameterizedTypeReference<CommonResult<TeAiLsmDialog>>() {});

        if (dialog==null){
            throw BusinessException.initExc("创建会话失败，请重新操作");
        }
        // 构建聊天请求
        ChatRequestVO build = ChatRequestVO.builder()
                .dialogId(dialog.getId().toHexString())
                .aiSceneId(SysDefConstants.AI_SCENE_BIZ_ADD_ID.toHexString())
                .request(prompt)
//                    .prompt()
                .model(model)
                .stream(false)
                .build();

        // 调用AI客户端
        return aiClient.chat(build,null);
    }


} 