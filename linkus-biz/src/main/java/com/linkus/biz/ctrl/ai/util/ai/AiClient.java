package com.linkus.biz.ctrl.ai.util.ai;

import com.alibaba.fastjson.JSON;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.biz.ctrl.ai.vo.ChatRequestVO;
import com.linkus.biz.ctrl.ai.vo.ChatResponseVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI 访问客户端
 * 封装 RestTemplate 和 URL 配置，提供便捷的 AI 服务调用方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AiClient {

    @Autowired
    private RestTemplate restTemplate;

    /**
     * AI 服务基础 URL
     */
    @Value("${ai.service.base-url:http://linkus-ai/linkus-ai/ai/lsm/chat/general}")
    private String baseUrl;

    /**
     * AI 服务 API 密钥
     */
    @Value("${ai.service.api-key:}")
    private String apiKey;

    /**
     * 默认模型
     */
    @Value("${ai.service.default-model:gpt-3.5-turbo}")
    private String defaultModel;

    /**
     * 默认温度参数
     */
    @Value("${ai.service.default-temperature:0.0}")
    private Double defaultTemperature;

    /**
     * 默认最大 token 数
     */
    @Value("${ai.service.default-max-tokens:2000}")
    private Integer defaultMaxTokens;

    /**
     * 发送聊天请求
     *
     * @param request 聊天请求对象
     * @return 聊天响应对象
     */
    public ChatResponseVO chat(ChatRequestVO request,HttpHeaders header) {
        try {
            log.info("发送 AI 聊天请求到: {}", baseUrl);
            HttpHeaders headers=header==null?createHeaders():header;
            HttpEntity<ChatRequestVO> entity = new HttpEntity<>(request, headers);
            ParameterizedTypeReference<CommonResult<ChatResponseVO>> typeRef =
                    new ParameterizedTypeReference<CommonResult<ChatResponseVO>>() {
                    };
            ResponseEntity<CommonResult<ChatResponseVO>> response = restTemplate.exchange(
                    baseUrl,
                    HttpMethod.POST,
                    entity,
                    typeRef
            );
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("API请求失败，状态码: {}", response.getStatusCode());
                throw BusinessException.initExc("API请求异常: " + response.getStatusCode());
            }
            CommonResult<ChatResponseVO> result = response.getBody();
            if (result == null || !result.isSuccess()) {
                log.error("业务逻辑错误: {}", result != null ? result.getMessage() : "空响应");
                throw BusinessException.initExc(result != null ? result.getMessage() : "空响应");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("AI 聊天请求失败", e);
            return createErrorResponse("AI 服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 发送聊天请求
     *
     * @return 聊天响应对象
     */
    public <T>T request(String url, Object body, HttpHeaders header,ParameterizedTypeReference<CommonResult<T>> typeRef ) {
        HttpHeaders headers=header==null?createHeaders():header;
        HttpEntity<Object> entity = new HttpEntity<>(body, headers);

        ResponseEntity<CommonResult<T>> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                typeRef
        );
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("请求失败，状态码: {}", response.getStatusCode());
            throw BusinessException.initExc("API请求异常: " + response.getStatusCode());
        }
        CommonResult<T> result = response.getBody();
        if (result == null || !result.isSuccess()) {
            log.error("业务逻辑错误: {}", result != null ? result.getMessage() : "空响应");
            throw BusinessException.initExc(result != null ? result.getMessage() : "空响应");
        }
        return result.getData();
    }

    public List<ChatResponseVO> chatList(ChatRequestVO request) {
        log.info("发送 AI 聊天请求到: {}", baseUrl);
        HttpHeaders headers = createHeaders();
        HttpEntity<ChatRequestVO> entity = new HttpEntity<>(request, headers);
        ParameterizedTypeReference<CommonResult<List<ChatResponseVO>>> typeRef =
                new ParameterizedTypeReference<CommonResult<List<ChatResponseVO>>>() {
                };
        ResponseEntity<CommonResult<List<ChatResponseVO>>> response = restTemplate.exchange(
                baseUrl,
                HttpMethod.POST,
                entity,
                typeRef
        );
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("API请求失败，状态码: {}", response.getStatusCode());
            throw BusinessException.initExc("API请求异常: " + response.getStatusCode());
        }
        CommonResult<List<ChatResponseVO>> result = response.getBody();
        if (result == null || !result.isSuccess()) {
            log.error("业务逻辑错误: {}", result != null ? result.getMessage() : "空响应");
            throw BusinessException.initExc(result != null ? result.getMessage() : "空响应");
        }
        return result.getData();
    }

    private final OkHttpClient client = new OkHttpClient();

    public String chatFlux(ChatRequestVO request, String url) {

        Flux<Object> flux = Flux.create(sink -> {
            Request req = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), JSON.toJSONString(request)))
                    .build();

            try (Response response = client.newCall(req).execute()) {
                ResponseBody body = response.body();
                if (body != null) {
                    // 假设返回的是换行分隔的数据（如 "data1\ndata2\ndata3"）
                    BufferedReader reader = new BufferedReader(body.charStream());
                    String line;
                    while ((line = reader.readLine()) != null) {
                        sink.next(line); // 逐行发送数据
                    }
                }
                sink.complete();
            } catch (IOException e) {
                sink.error(e);
            }
        });
        List<Object> list = flux.collectList().block();
        StringBuilder sb = new StringBuilder();
        assert list != null;
        list.forEach(sb::append);
        return sb.toString();
    }


//    /**
//     * 快速发送聊天请求（使用默认参数）
//     *
//     * @param systemPrompt 系统提示
//     * @param userMessage  用户消息
//     * @return 聊天响应对象
//     */
//    public ChatResponseVO chat(String systemPrompt, String userMessage) {
//        ChatRequestVO request = ChatRequestBuilder.createWithSystemAndUser(
//                        defaultModel,
//                        systemPrompt,
//                        userMessage
//                )
//                .temperature(defaultTemperature)
//                .maxTokens(defaultMaxTokens)
//                .build();
//
//        return chat(request);
//    }
//
//    /**
//     * 快速发送聊天请求（指定模型）
//     *
//     * @param model        模型名称
//     * @param systemPrompt 系统提示
//     * @param userMessage  用户消息
//     * @return 聊天响应对象
//     */
//    public ChatResponseVO chat(String model, String systemPrompt, String userMessage) {
//        ChatRequestVO request = ChatRequestBuilder.createWithSystemAndUser(
//                        model,
//                        systemPrompt,
//                        userMessage
//                )
//                .temperature(defaultTemperature)
//                .maxTokens(defaultMaxTokens)
//                .build();
//
//        return chat(request);
//    }

    /**
     * 文档分析请求
     *
     * @param documentContent 文档内容
     * @param analysisType    分析类型（如：requirement, defect, issue）
     * @return 分析结果
     */
    public ChatResponseVO analyzeDocument(String documentContent, String analysisType) {
        try {
            String url = baseUrl + "/api/ai/document/analyze";
            log.info("发送文档分析请求到: {}", url);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("content", documentContent);
            requestBody.put("type", analysisType);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<ChatResponseVO> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    ChatResponseVO.class
            );

            log.info("文档分析请求成功");
            return response.getBody();
        } catch (Exception e) {
            log.error("文档分析请求失败", e);
            return createErrorResponse("文档分析失败: " + e.getMessage());
        }
    }

    /**
     * 代码生成请求
     *
     * @param requirement 需求描述
     * @param language    编程语言
     * @return 生成的代码
     */
    public ChatResponseVO generateCode(String requirement, String language) {
        try {
            String url = baseUrl + "/api/ai/code/generate";
            log.info("发送代码生成请求到: {}", url);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("requirement", requirement);
            requestBody.put("language", language);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<ChatResponseVO> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    ChatResponseVO.class
            );

            log.info("代码生成请求成功");
            return response.getBody();
        } catch (Exception e) {
            log.error("代码生成请求失败", e);
            return createErrorResponse("代码生成失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     *
     * @return 是否健康
     */
    public boolean healthCheck() {
        try {
            String url = baseUrl + "/api/ai/health";
            log.info("发送健康检查请求到: {}", url);

            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            boolean healthy = response.getStatusCode().is2xxSuccessful();
            log.info("AI 服务健康检查结果: {}", healthy);
            return healthy;
        } catch (Exception e) {
            log.error("AI 服务健康检查失败", e);
            return false;
        }
    }

    /**
     * 创建请求头
     *
     * @return HTTP 请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        if (apiKey != null && !apiKey.trim().isEmpty()) {
            headers.set("Authorization", "Bearer " + apiKey);
        }

        return headers;
    }

    /**
     * 创建错误响应
     *
     * @param errorMessage 错误信息
     * @return 错误响应对象
     */
    private ChatResponseVO createErrorResponse(String errorMessage) {
        ChatResponseVO response = new ChatResponseVO();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        response.setContent("");
        return response;
    }

}