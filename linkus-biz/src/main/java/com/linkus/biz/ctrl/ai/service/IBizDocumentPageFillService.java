package com.linkus.biz.ctrl.ai.service;

import com.linkus.biz.ctrl.ai.vo.TeIdNameDesc;
import org.bson.types.ObjectId;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface IBizDocumentPageFillService {

    Map<String, Map<String, TeIdNameDesc>> generatePageFillDataFromDocument(ObjectId prdId, ObjectId catalogId, ObjectId bizTypeId, MultipartFile file, String model, String content, String tokenValue) ;

    List<TeIdNameDesc> queryRelateBiz(ObjectId prdId, ObjectId bizTypeId, String name, String desc, String model);
}
