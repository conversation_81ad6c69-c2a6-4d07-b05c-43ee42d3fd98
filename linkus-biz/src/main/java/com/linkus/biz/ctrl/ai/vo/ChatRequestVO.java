package com.linkus.biz.ctrl.ai.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 通用Chat请求VO
 * <AUTHOR>
 */
@Data
@Builder
public class ChatRequestVO {

    /**
     * 对话ID
     */
    String dialogId;

    /**
     * 场景
     */
    String aiSceneId;

    /**
     * 用户问题
     */
    String request;

    /**
     * 使用历史会话
     */
    Boolean useHistoryDialog;

    /**
     * 用户提示词
     */
    String prompt;

    /**
     * 模型
     */
    String model;

    /**
     * 是否使用流式响应
     */
    Boolean stream;

} 