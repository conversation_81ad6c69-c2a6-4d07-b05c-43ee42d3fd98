package com.linkus.biz.ctrl.ai.vo;

import lombok.Data;

/**
 * 通用Chat响应VO
 * <AUTHOR>
 */
@Data
public class ChatResponseVO {

    /**
     * 响应内容
     */
    private String content;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 使用情况
     */
    private UsageInfo usage;

    /**
     * 是否成功
     */
    private Boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 使用情况信息
     */
    @Data
    public static class UsageInfo {
        /**
         * 总token数
         */
        private Integer totalTokens;

        /**
         * 提示token数
         */
        private Integer promptTokens;

        /**
         * 完成token数
         */
        private Integer completionTokens;
    }
} 