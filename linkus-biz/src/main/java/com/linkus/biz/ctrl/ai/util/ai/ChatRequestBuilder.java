//package com.linkus.biz.ctrl.ai.util.ai;
//
//
//import com.linkus.biz.ctrl.ai.vo.ChatRequestVO;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * Chat请求构建器
// * <AUTHOR>
// */
//public class ChatRequestBuilder {
//
//    private String model;
//    private List<ChatRequestVO.ChatMessage> messages = new ArrayList<>();
//    private Double temperature = 0.0;
//    private Integer maxTokens;
//    private Boolean stream = false;
//
//    /**
//     * 设置模型
//     */
//    public ChatRequestBuilder model(String model) {
//        this.model = model;
//        return this;
//    }
//
//    /**
//     * 设置温度参数
//     */
//    public ChatRequestBuilder temperature(Double temperature) {
//        this.temperature = temperature;
//        return this;
//    }
//
//    /**
//     * 设置最大token数
//     */
//    public ChatRequestBuilder maxTokens(Integer maxTokens) {
//        this.maxTokens = maxTokens;
//        return this;
//    }
//
//    /**
//     * 设置是否流式响应
//     */
//    public ChatRequestBuilder stream(Boolean stream) {
//        this.stream = stream;
//        return this;
//    }
//
//    /**
//     * 添加系统消息
//     */
//    public ChatRequestBuilder system(String content) {
//        messages.add(new ChatRequestVO.ChatMessage("system", content));
//        return this;
//    }
//
//    /**
//     * 添加用户消息
//     */
//    public ChatRequestBuilder user(String content) {
//        messages.add(new ChatRequestVO.ChatMessage("user", content));
//        return this;
//    }
//
//    /**
//     * 添加助手消息
//     */
//    public ChatRequestBuilder assistant(String content) {
//        messages.add(new ChatRequestVO.ChatMessage("assistant", content));
//        return this;
//    }
//
//    /**
//     * 添加消息
//     */
//    public ChatRequestBuilder message(String role, String content) {
//        messages.add(new ChatRequestVO.ChatMessage(role, content));
//        return this;
//    }
//
//    /**
//     * 构建ChatRequestVO
//     */
//    public ChatRequestVO build() {
//        ChatRequestVO request = new ChatRequestVO();
//        request.setModel(model);
//        request.setMessages(messages);
//        request.setTemperature(temperature);
//        request.setMaxTokens(maxTokens);
//        request.setStream(stream);
//        return request;
//    }
//
//    /**
//     * 创建构建器实例
//     */
//    public static ChatRequestBuilder create() {
//        return new ChatRequestBuilder();
//    }
//
//    /**
//     * 快速创建包含系统提示和用户消息的请求
//     */
//    public static ChatRequestBuilder createWithSystemAndUser(String systemPrompt, String userMessage) {
//        return create()
//                .system(systemPrompt)
//                .user(userMessage);
//    }
//
//    /**
//     * 快速创建包含系统提示和用户消息的请求（指定模型）
//     */
//    public static ChatRequestBuilder createWithSystemAndUser(String model, String systemPrompt, String userMessage) {
//        return create()
//                .model(model)
//                .system(systemPrompt)
//                .user(userMessage);
//    }
//}