package com.linkus.prj.vo;

import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.sys.model.PmsValueItemVo;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

@Data
public class PrjPreSalesVo {
    private ObjectId prjId;
    private String prjName;
    private String prjCode;
    private ObjectId pmUserId;
    private String pmUser;
    private TeUser preSaleUser;
    private String level;
    private String status;
    private ObjectId prjBmkVerId;
    private String prov;
    private String region;
    private String engDept;
    private String note;
    private Double score;
    //pso经理
    private TeUser psoManager;
    //pso总监
    private TeUser psoDirector;
    List<PmsValueItemVo> pmsValueItemVos;
}
