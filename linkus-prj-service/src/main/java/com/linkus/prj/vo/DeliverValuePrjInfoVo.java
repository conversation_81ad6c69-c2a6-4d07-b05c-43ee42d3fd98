package com.linkus.prj.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class DeliverValuePrjInfoVo {
    @ColumnWidth(15)
    @ExcelProperty("区域")
    private String region;
    @ColumnWidth(20)
    @ExcelProperty("工程部")
    private String engDept;
    @ColumnWidth(15)
    @ExcelProperty("省份")
    private String prov;
    @ExcelProperty("分类")
    private String level;
    @ColumnWidth(30)
    @ExcelProperty("项目集编码")
    private String prjCode;
    @ColumnWidth(30)
    @ExcelProperty("项目集名称")
    private String prjName;
    @ColumnWidth(15)
    @ExcelProperty("项目经理")
    private String pmUser;
    @ColumnWidth(15)
    @ExcelProperty("项目集状态")
    private String statusName;
    @ColumnWidth(15)
    @ExcelProperty("价值交付得分")
    private Double score;
}
