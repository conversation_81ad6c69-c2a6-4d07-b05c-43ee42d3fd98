package com.linkus.prj.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.constants.PrjBudgetConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.BatchCondsUpsert;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.*;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.*;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.dao.IBizDao;
import com.linkus.mail.param.MailInfo;
import com.linkus.mail.service.IMailService;
import com.linkus.prj.constant.EmpSkillTestConstant;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.IPrjInfoDao;
import com.linkus.prj.model.*;
import com.linkus.prj.model.vo.PrjDvtMgtCostOverABCGVo;
import com.linkus.prj.model.vo.PrjDvtMgtMailTemplateEnum;
import com.linkus.prj.model.vo.PrjDvtMgtMailVo;
import com.linkus.prj.service.IPrjCapabilityAssessService;
import com.linkus.prj.service.IPrjPreSalesService;
import com.linkus.prj.util.CellStyleHandler;
import com.linkus.prj.util.easyexcel.DvtABCGCellRule;
import com.linkus.prj.util.easyexcel.DvtCellStyleHandler;
import com.linkus.prj.util.easyexcel.ICellRule;
import com.linkus.prj.vo.*;
import com.linkus.sys.dao.IFileDao;
import com.linkus.sys.model.PmsValueItemVo;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeFile;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import com.mongodb.client.MongoCursor;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfig;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PrjPreSalesServiceImpl implements IPrjPreSalesService {
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private IPrjCapabilityAssessService prjCapabilityAssessService;
    @Autowired
    private ISysDefRoleUserDao sysDefRoleUserDao;
    @Autowired
    private IPrjInfoDao prjInfoDao;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IBizDao bizDao;
    @Autowired
    private ISysUserService sysUserService;
    @Resource
    private FreeMarkerConfig freeMarkerConfig;
    @Autowired
    private IMailService mailService;
    @Autowired
    private IFileDao fileDao;
    @Override
    public PageBean getPrjDeliverValueByType(PmsValueItemVo vo, TeSysUser loginUser) {
        PageBean pageBean = new PageBean();
        //获取权限下的项目集id
        List<ObjectId> prjIdList = getAuth(vo, loginUser);
        if (vo.getPrjId() != null){
            if (CollectionUtils.isEmpty(prjIdList)){
                prjIdList = new ArrayList<>();
                prjIdList.add(vo.getPrjId());
            }else if (prjIdList.contains(vo.getPrjId())){
                prjIdList.clear();
                prjIdList.add(vo.getPrjId());
            }else {
                throw BusinessException.initExc("您没有权限查看！");
            }
        }
        boolean needAuth = (BooleanUtils.isTrue(vo.getIsAll()) || (BooleanUtils.isNotTrue(vo.getIsAll())
                && PrjConstant.PMS_VALUE_ITEM_STATUS_Evaluation_Notice_ID.equals(vo.getPreSalesTypeId()))) ? true : false;

        //查询语句
        List<Document> sql = getSql(vo,prjIdList,needAuth ? false : true,loginUser);
        if (BooleanUtils.isTrue(vo.getIsCount())){
            int count = 0;
            List<Document> countSql = new ArrayList<>();
            //总行数
            countSql.addAll(sql);
            countSql.add(new Document("$count","count"));
            List<Document> countResult = prjInfoDao.aggregate(countSql);
            if (CollectionUtils.isNotEmpty(countResult)) {
                count = (int) countResult.get(0).get("count");
            }
            pageBean.setCount(count);
            return pageBean;
        }else {
            if (vo.getPageIndex() != null && vo.getPageSize() != null){
                sql.add(new Document("$skip",vo.getPageIndex()*vo.getPageSize()));
                sql.add(new Document("$limit", vo.getPageSize()));
            }
            MongoCursor<Document> cursor =mongoTemplate.getCollection(DBT.PRJINFO.n()).aggregate(sql, Document.class).allowDiskUse(true).cursor();
            List<PrjPreSalesVo> result = new ArrayList<>();
            while (cursor.hasNext()){
                PrjPreSalesVo salesVo = new PrjPreSalesVo();
                Document next = cursor.next();
                ObjectId prjId = next.getObjectId("prjId");
                salesVo.setPrjId(prjId);
                salesVo.setPrjName(next.getString("prjName"));
                salesVo.setPrjCode(next.getString("prjCode"));
                Document pmUser = (Document) next.get("pmUser");
                if (pmUser != null){
                    salesVo.setPmUserId(pmUser.getObjectId("userId"));
                    salesVo.setPmUser(StringUtil.getNotNullStr(pmUser.getString("userName"))+"/"+StringUtil.getNotNullStr(pmUser.getString("loginName")));
                }
                Document preSaleUser = (Document) next.get("preSaleUser");
                if (preSaleUser != null){
                    TeUser user = new TeUser(preSaleUser.getObjectId("userId"),
                            preSaleUser.getString("loginName"),preSaleUser.getString("userName"),preSaleUser.getString("jobCode"));
                    salesVo.setPreSaleUser(user);
                }
                salesVo.setLevel(next.getString("level"));
                salesVo.setStatus(next.getString("status"));
                salesVo.setPrjBmkVerId(StringUtil.toObjectId(next.getString("maxPrjBmkVerId")));
                salesVo.setProv(next.getString("prov"));
                salesVo.setRegion(next.getString("region"));
                salesVo.setEngDept(next.getString("engDept"));
                List<Document> psos = (List<Document>)next.get("pso");
                if (CollectionUtils.isNotEmpty(psos)){
                    for (Document pso : psos){
                        List<Document> roles = (List<Document>)pso.get("role");
                        if (CollectionUtils.isEmpty(roles)){
                            continue;
                        }
                        Document role = roles.get(0);
                        Document roleUser = (Document)pso.get("roleUser");
                        if (roleUser != null){
                            TeUser user = new TeUser(roleUser.getObjectId("userId"),
                                    roleUser.getString("loginName"),roleUser.getString("userName"),roleUser.getString("jobCode"));
                            if (PrjConstant.DEF_ID_MANAGER_ROLE.equals(role.getObjectId("roleId"))){
                                salesVo.setPsoManager(user);
                            }else if (PrjConstant.DEF_ID_DIRECTOR_ROLE.equals(role.getObjectId("roleId"))){
                                salesVo.setPsoDirector(user);
                            }
                        }
                    }
                }
                List<Document> bizs = (List<Document>)next.get("biz");
                List<PmsValueItemVo> pmsValueItemVos = new ArrayList<>();
                Map<Date,String> noteMap = new HashMap<>();
                double score = 100d;
                boolean has = false;
                for (Document biz : bizs){
                    PmsValueItemVo pmsValueItemVo = new PmsValueItemVo();
                    pmsValueItemVo.setId(biz.getObjectId("_id"));

                    Map<ObjectId,Object> custFieldInfo = biz.get("custFieldInfo", Map.class);
                    pmsValueItemVo.setValueItemName(biz.getString("name"));
                    pmsValueItemVo.setPrjId(prjId);
                    //价值项
                    pmsValueItemVo.setValueItem(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_VALUE_ITEM_ID.toString())));
                    //价值子项
                    pmsValueItemVo.setSubValueItem(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_SUB_VALUE_ID.toString())));
                    //分类
                    pmsValueItemVo.setClassify(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_CLASSIFY_ID.toString())));
                    //价值评判标准
                    pmsValueItemVo.setJudgeStand(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAND_ID.toString())));
                    //权重
                    pmsValueItemVo.setWeight(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_WEIGHT_ID.toString())));
                    //是否可量化
                    pmsValueItemVo.setQuantization(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_QUANTIZATION_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_QUANTIZATION_ID.toString())));
                    //积分标准
                    pmsValueItemVo.setIntegral(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_INTEGRAL_ID.toString())));
                    //评判阶段
                    pmsValueItemVo.setJudgeStage(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAGE_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAGE_ID.toString())));
                    //评判角色
                    pmsValueItemVo.setJudgeRole(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_ROLE_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_ROLE_ID.toString())));
                    //评判人
                    pmsValueItemVo.setJudgeEmp(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_EMP_ID.toString())));
                    //状态
                    pmsValueItemVo.setPreSalesStatus(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_STATUS_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_STATUS_ID.toString())));
                    //PM自评结果
                    pmsValueItemVo.setPmAchieve(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_Pm_Achieve_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_Pm_Achieve_ID.toString())));
                    //PM说明
                    pmsValueItemVo.setPmDescribe(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_Pm_Describe_ID.toString())));
                    //附件
                    List<Document> files = biz.get("files", List.class);
                    if (CollectionUtils.isNotEmpty(files)){
                        List<TeIdName> fileList = new ArrayList<>();
                        for (Document file : files){
                            Document fileDoc = file.get("file",Document.class);
                            if (fileDoc != null){
                                TeIdName filetn = new TeIdName(fileDoc.getObjectId("cid"),fileDoc.getString("name"));
                                fileList.add(filetn);
                            }
                        }
                        pmsValueItemVo.setFiles(fileList);
                    }
                    //售前评估结果
                    pmsValueItemVo.setPreSalesAchieve(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Achieve_ID.toString()) == null ?
                            null : convertObject((List<Document>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Achieve_ID.toString())));
                    //售前说明
                    pmsValueItemVo.setPreSalesDescribe(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Describe_ID.toString())));
                    //得分 = 100- ∑未达成项*权重（达成 0；未达成 1）
                    if (StringUtil.toDouble(pmsValueItemVo.getWeight()) != null){
                        double weight = StringUtil.toDouble(pmsValueItemVo.getWeight());
                        if (pmsValueItemVo.getPreSalesAchieve() != null){
                            if (PrjConstant.PMS_VALUE_ITEM_PreSale_Not_Achieve_ID.equals(pmsValueItemVo.getPreSalesAchieve().getCid())){
                                score -= weight;
                            }
                            has = true;
                        }
                    }
                    pmsValueItemVos.add(pmsValueItemVo);

                    //备注
                    List<Document> notes = (List<Document>)biz.get("notes");
                    if (CollectionUtils.isNotEmpty(notes)){
                        for (Document note : notes){
                            String desc = note.getString("desc");
                            Date addTime = note.getDate("addTime");
                            if (StringUtil.isNull(desc) || addTime == null){
                                continue;
                            }
                            noteMap.put(addTime,desc);
                        }
                    }
                }
                if (MapUtils.isNotEmpty(noteMap)){
                    StringBuilder noteStr = new StringBuilder();
                    Date date = noteMap.keySet().stream().max((v1, v2) -> v1.compareTo(v2)).get();
                    String desc = noteMap.get(date);
                    if (StringUtil.isNotNull(desc)){
                        Map<String, Object> map = JsonUtil.toMap(desc);
                        noteStr.append(StringUtil.getNotNullStr(map.get("desc")));
//                    if ("售前确认-打回".equals(map.get("title"))){
//                        noteStr.append(StringUtil.getNotNullStr(map.get("desc")));
//                    }
                    }
                    salesVo.setNote(noteStr.toString());
                }
                salesVo.setScore(has ? BigDecimalUtils.getDoubleHalfNum(score,2) : null);
                salesVo.setPmsValueItemVos(pmsValueItemVos);
                result.add(salesVo);
            }
            pageBean.setObjectList(result);
            return pageBean;
        }
    }
    private List<Document> getSql(PmsValueItemVo vo,List<ObjectId> prjIdList,Boolean isMark,TeSysUser loginUser){
        List<Document> sql = new ArrayList<>();
        Document prjMatchDoc = new Document();
        prjMatchDoc.append(DFN.common_isValid.n(),new Document("$ne",false))
                .append(DFN.prjInfo__sbuId.n(),new Document("$in",vo.getSbuIds()))
                .append(DFN.prjInfo__isPrjSet.n(),true)
                .append(DFN.prjInfo__prjBmks.dot(DFN.prjInfo__prjBmkVerId).n(),new Document("$ne",null));
        if (CollectionUtils.isNotEmpty(prjIdList)){
            prjMatchDoc.append(DFN.prjInfo__prjId.n(),new Document("$in",prjIdList));
        }
        if (vo.getProvId() != null){
            prjMatchDoc.append(DFN.prjInfo__prov.dot(DFN.common_cid).n(),vo.getProvId());
        }
        if (CollectionUtils.isNotEmpty(vo.getStatusIds())){
            prjMatchDoc.append(DFN.prjInfo__status.dot(DFN.common_cid).n(),new Document("$in", vo.getStatusIds()));
        }

        sql.add(new Document("$match",prjMatchDoc));
        //关联省份
        Document lookUpProv = new Document();
        lookUpProv.append("from","sysDef")
                .append("localField","prov.cid")
                .append("foreignField","_id")
                .append("as","prov");
        sql.add(new Document("$lookup",lookUpProv));
        sql.add(new Document("$unwind","$prov"));
        if (vo.getRegionId() != null){
            sql.add(new Document("$match",new Document("prov.cndtItems.cid",vo.getRegionId())));
        }
        //映射字段
        Document project = new Document();
        project.append("prjId","$prjId")
                .append("prjName","$prjName")
                .append("prjCode","$prjCode")
                .append("pmUser","$pmUser")
                .append("preSaleUser","$preSaleUser")
                .append("level","$level.name")
                .append("status","$status.name")
                .append("maxPrjBmkVerId",new Document("$toString",new Document("$last","$prjBmks.prjBmkVerId")))
                .append("approveTime",new Document("$last","$prjBmks.approveTime"))
                .append("provId","$prov._id")
                .append("prov","$prov.defName")
                .append("region",
                        new Document("$reduce",new Document(
                                "input","$prov.cndtItems")
                                .append("initialValue","")
                                .append("in",new Document(
                                        "$concat", Arrays.asList("$$value",
                                        new Document("$cond",
                                                new Document("if",
                                                        new Document("$eq",
                                                                Arrays.asList("$$this.codeName","abpRegion")))
                                                        .append("then","$$this.name").append("else","")))
                                ))
                        ))
                .append("engDept",
                        new Document("$reduce",new Document(
                                "input","$prov.cndtItems")
                                .append("initialValue","")
                                .append("in",new Document(
                                        "$concat", Arrays.asList("$$value",
                                        new Document("$cond",
                                                new Document("if",
                                                        new Document("$eq",
                                                                Arrays.asList("$$this.codeName","abpEngDept")))
                                                        .append("then","$$this.name").append("else","")))
                                ))
                        ));
        sql.add(new Document("$project",project));
        if (StringUtil.isNotNull(vo.getStartApproveTime()) && StringUtil.isNotNull(vo.getEndApproveTime())){
            Date startDate = DateUtil.parseDate(vo.getStartApproveTime(), DateUtil.DATE_FORMAT);
            Date endDate = DateUtil.parseDate(vo.getEndApproveTime(), DateUtil.DATE_FORMAT);
            sql.add(new Document("$match",
                        new Document("$and",
                                Arrays.asList(
                                        new Document("approveTime",new Document("$gte",startDate)),
                                        new Document("approveTime",new Document("$lte",endDate)))
            )));
        }
        //关联角色表，查询pso
        Document lookUpRoleUser = new Document();
        List<Document> roleUserPipelines = new ArrayList<>();
        roleUserPipelines.add(
                new Document("$match",new Document("$expr",
                                        new Document("$and",
                                                Arrays.asList(
                                                        new Document("$eq",Arrays.asList("$defId","$$prov_Id")),
                                                        new Document("$eq",Arrays.asList("$isValid",true)),
                                                        new Document("$or",Arrays.asList(
                                                                new Document("$in",Arrays.asList(PrjConstant.DEF_ID_MANAGER_ROLE,new Document("$ifNull",Arrays.asList("$role.roleId",Arrays.asList())))),
                                                                new Document("$in",Arrays.asList(PrjConstant.DEF_ID_DIRECTOR_ROLE,new Document("$ifNull",Arrays.asList("$role.roleId",Arrays.asList()))))
                                                        ))
                                                )))));
        roleUserPipelines.add(new Document("$project",new Document("role",1).append("roleUser",1)));
        lookUpRoleUser.append("from","sysDefRoleUser")
                .append("let",new Document("prov_Id","$provId"))
                .append("pipeline",roleUserPipelines).append("as","pso");
        sql.add(new Document("$lookup",lookUpRoleUser));

        if (BooleanUtils.isTrue(isMark)){
            Document psoMatch = new Document();
            if (PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID.equals(vo.getPreSalesTypeId())){
                psoMatch.append("pso",new Document("$elemMatch",new Document("role.roleId",PrjConstant.DEF_ID_MANAGER_ROLE).append("roleUser.userId",loginUser.getId())));
            }else if (PrjConstant.PMS_VALUE_ITEM_STATUS_MODIFY_ID.equals(vo.getPreSalesTypeId()) || PrjConstant.PMS_VALUE_ITEM_STATUS_PM_ID.equals(vo.getPreSalesTypeId())){
                psoMatch.append(DFN.prjInfo__pmUser.dot(DFN.common_userId).n(),loginUser.getId());
            }else if (PrjConstant.PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id.equals(vo.getPreSalesTypeId())){
                psoMatch.append("pso",new Document("$elemMatch",new Document("role.roleId",PrjConstant.DEF_ID_DIRECTOR_ROLE).append("roleUser.userId",loginUser.getId())));
            }
            if (!psoMatch.isEmpty()){
                sql.add(new Document("$match",psoMatch));
            }
        }
        //关联biz
        Document lookUpBiz = new Document();
        List<Document> bizSql = new ArrayList<>();
        bizSql.add(new Document("$eq",Arrays.asList("$prj.cid","$$prj_prjId")));
        bizSql.add(new Document("$eq",Arrays.asList("$isValid",true)));
        bizSql.add(new Document("$eq",Arrays.asList("$prd.cid", EmpSkillTestConstant.PRD_ID_AICM)));
        bizSql.add(new Document("$eq",Arrays.asList("$bizType.cid",PrjConstant.PMS_VALUE_ITEM_BIZ_TYPE_ID)));
        bizSql.add(new Document("$eq",Arrays.asList("$custFieldInfo.5c46d888e0ee77405a14a35a","$$prj_maxPrjBmkVerId")));
        Document bizMatch = new Document();
        if (CollectionUtils.isNotEmpty(vo.getJudgeStageIds())){
            List<Document> judgeStageDoc = new ArrayList<>();
            for (ObjectId judgeStageId : vo.getJudgeStageIds()){
                judgeStageDoc.add(new Document("custFieldInfo.66bb14514d11cd11735d4e01.cid",judgeStageId));
            }
            bizMatch.append("$or",judgeStageDoc);
        }
        if (vo.getPreSalesTypeId() != null){
            bizMatch.append("custFieldInfo.67779b06bc0f0b3441830bd5.cid",vo.getPreSalesTypeId());
        }
        List<Document> pipeline = new ArrayList<>();
        pipeline.add(new Document("$match", new Document("$expr", new Document("$and", bizSql))));
        pipeline.add(new Document("$project",new Document("custFieldInfo",1).append("_id",1).append("name",1).append("notes",1).append("files",1)));
        if (!bizMatch.isEmpty()){
            pipeline.add(new Document("$match",bizMatch));
        }
        lookUpBiz.append("from","biz")
                .append("let",new Document("prj_prjId","$prjId").append("prj_maxPrjBmkVerId","$maxPrjBmkVerId"))
                .append("pipeline",pipeline).append("as","biz");
        sql.add(new Document("$lookup",lookUpBiz));
        sql.add(new Document("$match",new Document("biz",new Document("$ne",Arrays.asList()))));
        return sql;
    }
    private TeIdName convertObject(List<Document> list){
        Document document = list.get(0);
        TeIdName teIdName = new TeIdName(document.getObjectId("cid"),document.getString("name"));
        return teIdName;
    }
    private List<ObjectId> getAuth(PmsValueItemVo vo,TeSysUser loginUser){
        List<String> sbuIds = new ArrayList<>();
        if (StringUtil.isNotNull(loginUser.getSbuId())){
            sbuIds.add(loginUser.getSbuId());
        }
        //角色
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
        //人员id
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //查询是否是bu运营管理员
        List<String> buPrjBudgetAdminUsers = prjCapabilityAssessService.getLoginUserAuth(loginUser);
        if (CollectionUtils.isNotEmpty(buPrjBudgetAdminUsers)){
            sbuIds.addAll(buPrjBudgetAdminUsers);
            vo.setSbuIds(sbuIds);
            return null;
        }
        if (CollectionUtils.isEmpty(sbuIds)){
            throw BusinessException.initExc("您没有权限查看！");
        }
        vo.setSbuIds(sbuIds);
        if (BooleanUtils.isTrue(vo.getIsAll())){
            //4.2 省份运营、PSO经理、PSO总监：可以看角色所在省份下所有项目集数据
            //SRD总监、经理：可以看项目设定经理总监是“我的”来取数据查看
            List<ObjectId> provIds = new ArrayList<>();
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
            List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.ABP_PROV);
            if (CollectionUtils.isNotEmpty(operateAdmins)){
                List<ObjectId> defIds = operateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(defIds)){
                    provIds.addAll(defIds);
                }
            }
            //Pso总监、pso经理
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
            roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
            List<TeSysDefRoleUser> directorOrmanagerList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(directorOrmanagerList)){
                List<ObjectId> defIds = directorOrmanagerList.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(defIds)){
                    provIds.addAll(defIds);
                }
            }
            //4.3 项目经理：可以看登录人是项目经理的项目集数据
            List<ObjectId> prjInfoIdList = getPrjInfoList(provIds, vo.getSbuIds(), loginUser.getId());
            if (CollectionUtils.isNotEmpty(prjInfoIdList)){
                return prjInfoIdList;
            }
            //4.5 其余人员没有数据查看权限
            throw BusinessException.initExc("您没有权限查看！");
        }
        return null;
    }
    private List<ObjectId> getPrjInfoList(List<ObjectId> provIds,List<String> sbuIds,ObjectId loginUserId){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_E(DFN.prjInfo__isPrjSet,true));
        conds.add(new DC_I<String>(DFN.prjInfo__sbuId,sbuIds));
        conds.add(new DC_E(DFN.prjInfo__prjBmks.dot(DFN.prjInfo__prjBmkVerId),null,true));
        List<IDbCondition> orConds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(provIds)){
            orConds.add(new DC_I<ObjectId>(DFN.prjInfo__prov.dot(DFN.common_cid),provIds));
        }
        orConds.add(new DC_E(DFN.prjInfo_srdRoleUser.dot(DFN.prjInfo_srdRoleUser_user).dot(DFN.common_userId),loginUserId));
        orConds.add(new DC_E(DFN.prjInfo__pmUser.dot(DFN.common_userId),loginUserId));
        orConds.add(new DC_E(DFN.prjInfo_preSaleUser.dot(DFN.common_userId),loginUserId));
        if (CollectionUtils.isNotEmpty(orConds)){
            conds.add(new DC_OR(orConds));
        }
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.prjInfo__prjId);
        List<TePrjInfo> prjInfoList = prjInfoDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isNotEmpty(prjInfoList)){
            List<ObjectId> prjIdList = prjInfoList.stream().filter(prj -> prj.getPrjId() != null).map(TePrjInfo::getPrjId).collect(Collectors.toList());
            return prjIdList;
        }
        return null;
    }
    private List<DeliverValuePrjInfoVo> converPrjInfoData(List<PrjPreSalesVo> dataList){
        List<DeliverValuePrjInfoVo> result = new ArrayList<>();
        for (PrjPreSalesVo vo : dataList){
            DeliverValuePrjInfoVo infoVo = new DeliverValuePrjInfoVo();
            infoVo.setRegion(vo.getRegion());
            infoVo.setEngDept(vo.getEngDept());
            infoVo.setProv(vo.getProv());
            infoVo.setLevel(vo.getLevel());
            infoVo.setPrjCode(vo.getPrjCode());
            infoVo.setPrjName(vo.getPrjName());
            infoVo.setPmUser(vo.getPmUser());
            infoVo.setStatusName(vo.getStatus());
            infoVo.setScore(BigDecimalUtils.getDoubleHalfNum(vo.getScore(),2));
            result.add(infoVo);
        }
        return result;
    }
    @Override
    public void exportPrjDeliverValue(PmsValueItemVo vo, TeSysUser loginUser, HttpServletResponse response) throws IOException {
        ObjectId preSalesTypeId = vo.getPreSalesTypeId();
        Boolean isAll = vo.getIsAll();
        PageBean pageBean = getPrjDeliverValueByType(vo, loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("价值交付.xlsx", "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        List<DeliverValuePrjInfoVo> prjInfoVoList = new ArrayList<>();
        if (pageBean != null && CollectionUtils.isNotEmpty(pageBean.getObjectList())){
            prjInfoVoList = converPrjInfoData((List<PrjPreSalesVo>)pageBean.getObjectList());
        }
        WriteSheet prjSheet = EasyExcel.writerSheet(0, "项目集信息")
                .head(DeliverValuePrjInfoVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(prjInfoVoList, prjSheet);
        if (BooleanUtils.isNotTrue(isAll)){
            if (PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID.equals(preSalesTypeId)
                    || PrjConstant.PMS_VALUE_ITEM_STATUS_MODIFY_ID.equals(preSalesTypeId)
                    || PrjConstant.PMS_VALUE_ITEM_STATUS_PM_ID.equals(preSalesTypeId)
            ){
                List<PrjPreSalesAndValueItemVo> preSales = new ArrayList<>();
                if (pageBean != null && CollectionUtils.isNotEmpty(pageBean.getObjectList())){
                    preSales = exportConvertObject((List<PrjPreSalesVo>)pageBean.getObjectList());
                }
                WriteSheet sheet = EasyExcel.writerSheet(1, "价值交付项明细")
                        .head(PrjPreSalesAndValueItemVo.class)
                        .registerWriteHandler(new CellStyleHandler())
                        .build();
                excelWriter.write(preSales, sheet);
            }else if (PrjConstant.PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id.equals(preSalesTypeId)){
                List<PrjValueItemPreSalesStatusVo> preSales = new ArrayList<>();
                if (pageBean != null && CollectionUtils.isNotEmpty(pageBean.getObjectList())){
                    preSales = exportConvertObjectForPreSale((List<PrjPreSalesVo>)pageBean.getObjectList());
                }
                WriteSheet sheet = EasyExcel.writerSheet(1, "价值交付项明细")
                        .head(PrjValueItemPreSalesStatusVo.class)
                        .registerWriteHandler(new CellStyleHandler())
                        .build();
                excelWriter.write(preSales, sheet);
            }else if (PrjConstant.PMS_VALUE_ITEM_STATUS_Evaluation_Notice_ID.equals(preSalesTypeId)){
                List<PrjValueItemAllVo> preSales = new ArrayList<>();
                if (pageBean != null && CollectionUtils.isNotEmpty(pageBean.getObjectList())){
                    preSales = exportConvertObjectForAll((List<PrjPreSalesVo>)pageBean.getObjectList());
                }
                WriteSheet sheet = EasyExcel.writerSheet(1, "价值交付项明细")
                        .head(PrjValueItemAllVo.class)
                        .registerWriteHandler(new CellStyleHandler())
                        .build();
                excelWriter.write(preSales, sheet);
            }
        }else {
            List<PrjValueItemAllVo> all = new ArrayList<>();
            if (pageBean != null && CollectionUtils.isNotEmpty(pageBean.getObjectList())){
                all = exportConvertObjectForAll((List<PrjPreSalesVo>)pageBean.getObjectList());
            }
            WriteSheet sheet = EasyExcel.writerSheet(1, "价值交付项明细")
                    .head(PrjValueItemAllVo.class)
                    .registerWriteHandler(new CellStyleHandler())
                    .build();
            excelWriter.write(all, sheet);
        }
        excelWriter.finish();
    }
    private List<PrjPreSalesAndValueItemVo> exportConvertObject(List<PrjPreSalesVo> dataList){
        List<PrjPreSalesAndValueItemVo> result = new ArrayList<>();
        for (PrjPreSalesVo vo : dataList){
            List<PmsValueItemVo> pmsValueItemVos = vo.getPmsValueItemVos();
            if (CollectionUtils.isNotEmpty(pmsValueItemVos)){
                for (PmsValueItemVo itemVo : pmsValueItemVos){
                    PrjPreSalesAndValueItemVo data = new PrjPreSalesAndValueItemVo();
                    data.setStatusName(itemVo.getPreSalesStatus() == null ? "" :StringUtil.getNotNullStr(itemVo.getPreSalesStatus().getName()));
                    data.setPsoManager(vo.getPsoManager() == null ? "" : StringUtil.getNotNullStr(vo.getPsoManager().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoManager().getLoginName()));
                    data.setPsoDirector(vo.getPsoDirector() == null ? "" : StringUtil.getNotNullStr(vo.getPsoDirector().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoDirector().getLoginName()));
                    data.setRegion(vo.getRegion());
                    data.setProv(vo.getProv());
                    data.setLevel(vo.getLevel());
                    data.setPrjCode(vo.getPrjCode());
                    data.setPrjName(vo.getPrjName());
                    data.setPmUser(vo.getPmUser());
                    data.setValueItem(itemVo.getValueItem());
                    data.setClassifySubValueItem(StringUtil.getNotNullStr(itemVo.getClassify())+"/"+StringUtil.getNotNullStr(itemVo.getSubValueItem()));
                    data.setValueItemName(itemVo.getValueItemName());
                    data.setJudgeStand(itemVo.getJudgeStand());
                    data.setWeight(itemVo.getWeight());
                    data.setQuantization(itemVo.getQuantization() == null ? "" : itemVo.getQuantization().getName());
                    data.setIntegral(itemVo.getIntegral());
                    data.setJudgeStage(itemVo.getJudgeStage() == null ? "" : itemVo.getJudgeStage().getName());
                    data.setJudgeRole(itemVo.getJudgeRole() == null ? "" : itemVo.getJudgeRole().getName());
                    data.setJudgeEmp(itemVo.getJudgeEmp());
                    result.add(data);
                }
            }
        }
        return result;
    }
    private List<PrjValueItemPreSalesStatusVo> exportConvertObjectForPreSale(List<PrjPreSalesVo> dataList){
        List<PrjValueItemPreSalesStatusVo> result = new ArrayList<>();
        for (PrjPreSalesVo vo : dataList){
            List<PmsValueItemVo> pmsValueItemVos = vo.getPmsValueItemVos();
            if (CollectionUtils.isNotEmpty(pmsValueItemVos)){
                for (PmsValueItemVo itemVo : pmsValueItemVos){
                    PrjValueItemPreSalesStatusVo data = new PrjValueItemPreSalesStatusVo();
                    data.setStatusName(itemVo.getPreSalesStatus() == null ? "" :StringUtil.getNotNullStr(itemVo.getPreSalesStatus().getName()));
                    data.setPsoManager(vo.getPsoManager() == null ? "" : StringUtil.getNotNullStr(vo.getPsoManager().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoManager().getLoginName()));
                    data.setPsoDirector(vo.getPsoDirector() == null ? "" : StringUtil.getNotNullStr(vo.getPsoDirector().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoDirector().getLoginName()));
                    data.setRegion(vo.getRegion());
                    data.setProv(vo.getProv());
                    data.setLevel(vo.getLevel());
                    data.setPrjCode(vo.getPrjCode());
                    data.setPrjName(vo.getPrjName());
                    data.setPmUser(vo.getPmUser());
                    data.setValueItem(itemVo.getValueItem());
                    data.setClassifySubValueItem(StringUtil.getNotNullStr(itemVo.getClassify())+"/"+StringUtil.getNotNullStr(itemVo.getSubValueItem()));
                    data.setValueItemName(itemVo.getValueItemName());
                    data.setJudgeStand(itemVo.getJudgeStand());
                    data.setWeight(itemVo.getWeight());
                    data.setQuantization(itemVo.getQuantization() == null ? "" : itemVo.getQuantization().getName());
                    data.setIntegral(itemVo.getIntegral());
                    data.setJudgeStage(itemVo.getJudgeStage() == null ? "" : itemVo.getJudgeStage().getName());
                    data.setJudgeRole(itemVo.getJudgeRole() == null ? "" : itemVo.getJudgeRole().getName());
                    data.setJudgeEmp(itemVo.getJudgeEmp());
                    data.setPmAchieve(itemVo.getPmAchieve() == null ? "" : itemVo.getPmAchieve().getName());
                    data.setPmDescribe(StringUtil.getNotNullStr(itemVo.getPmDescribe()));
                    List<TeIdName> files = itemVo.getFiles();
                    StringBuilder fileName = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(files)){
                        for (TeIdName file : files){
                            fileName.append(StringUtil.getNotNullStr(file.getName())+"、");
                        }
                        fileName.deleteCharAt(fileName.lastIndexOf("、"));
                    }
                    data.setFile(fileName.toString());
                    result.add(data);
                }
            }
        }
        return result;
    }
    private List<PrjValueItemAllVo> exportConvertObjectForAll(List<PrjPreSalesVo> dataList){
        List<PrjValueItemAllVo> result = new ArrayList<>();
        for (PrjPreSalesVo vo : dataList){
            List<PmsValueItemVo> pmsValueItemVos = vo.getPmsValueItemVos();
            if (CollectionUtils.isNotEmpty(pmsValueItemVos)){
                for (PmsValueItemVo itemVo : pmsValueItemVos){
                    PrjValueItemAllVo data = new PrjValueItemAllVo();
                    data.setStatusName(itemVo.getPreSalesStatus() == null ? "" :StringUtil.getNotNullStr(itemVo.getPreSalesStatus().getName()));
                    data.setPsoManager(vo.getPsoManager() == null ? "" : StringUtil.getNotNullStr(vo.getPsoManager().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoManager().getLoginName()));
                    data.setPsoDirector(vo.getPsoDirector() == null ? "" : StringUtil.getNotNullStr(vo.getPsoDirector().getUserName())+"/"+StringUtil.getNotNullStr(vo.getPsoDirector().getLoginName()));
                    data.setRegion(vo.getRegion());
                    data.setProv(vo.getProv());
                    data.setLevel(vo.getLevel());
                    data.setPrjCode(vo.getPrjCode());
                    data.setPrjName(vo.getPrjName());
                    data.setPmUser(vo.getPmUser());
                    data.setStatus(vo.getStatus());
                    data.setValueItem(itemVo.getValueItem());
                    data.setClassifySubValueItem(StringUtil.getNotNullStr(itemVo.getClassify())+"/"+StringUtil.getNotNullStr(itemVo.getSubValueItem()));
                    data.setValueItemName(itemVo.getValueItemName());
                    data.setJudgeStand(itemVo.getJudgeStand());
                    data.setWeight(itemVo.getWeight());
                    data.setQuantization(itemVo.getQuantization() == null ? "" : itemVo.getQuantization().getName());
                    data.setIntegral(itemVo.getIntegral());
                    data.setJudgeStage(itemVo.getJudgeStage() == null ? "" : itemVo.getJudgeStage().getName());
                    data.setJudgeRole(itemVo.getJudgeRole() == null ? "" : itemVo.getJudgeRole().getName());
                    data.setJudgeEmp(itemVo.getJudgeEmp());
                    data.setPmAchieve(itemVo.getPmAchieve() == null ? "" : itemVo.getPmAchieve().getName());
                    data.setPmDescribe(StringUtil.getNotNullStr(itemVo.getPmDescribe()));
                    List<TeIdName> files = itemVo.getFiles();
                    StringBuilder fileName = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(files)){
                        for (TeIdName file : files){
                            fileName.append(StringUtil.getNotNullStr(file.getName())+"、");
                        }
                        fileName.deleteCharAt(fileName.lastIndexOf("、"));
                    }
                    data.setFile(fileName.toString());
                    data.setPreSalesAchieve(itemVo.getPreSalesAchieve() == null ? "" : itemVo.getPreSalesAchieve().getName());
                    data.setPreSalesDescribe(StringUtil.getNotNullStr(itemVo.getPreSalesDescribe()));
                    result.add(data);
                }
            }
        }
        return result;
    }
    @Override
    public Map<ObjectId, Integer> countCornermark(String sbuId, TeSysUser loginUser) {
        Map<ObjectId,Integer> result = new HashMap<>();

        //待确认角标统计
        PmsValueItemVo vo1 = new PmsValueItemVo();
        vo1.setSbuIds(Arrays.asList(sbuId));
        vo1.setPreSalesTypeId(PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID);
        //权限
        //List<ObjectId> prjIdList = getAuth(vo1, loginUser);

        List<Document> beforeSql = getSql(vo1, null, true, loginUser);
        beforeSql.add(new Document("$count","count"));
        List<Document> before = prjInfoDao.aggregate(beforeSql);
        int beforeCount = 0;
        if (CollectionUtils.isNotEmpty(before)) {
            beforeCount = (int) before.get(0).get("count");
        }
        result.put(PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID,beforeCount);
        //待修改角标统计
        PmsValueItemVo vo2 = new PmsValueItemVo();
        vo2.setSbuIds(Arrays.asList(sbuId));
        vo2.setPreSalesTypeId(PrjConstant.PMS_VALUE_ITEM_STATUS_MODIFY_ID);
        List<Document> modifySql = getSql(vo2, null, true, loginUser);
        modifySql.add(new Document("$count","count"));
        List<Document> modify = prjInfoDao.aggregate(modifySql);
        int modifyCount = 0;
        if (CollectionUtils.isNotEmpty(modify)) {
            modifyCount = (int) modify.get(0).get("count");
        }
        result.put(PrjConstant.PMS_VALUE_ITEM_STATUS_MODIFY_ID,modifyCount);
        //PM自评
        PmsValueItemVo vo3 = new PmsValueItemVo();
        vo3.setSbuIds(Arrays.asList(sbuId));
        vo3.setPreSalesTypeId(PrjConstant.PMS_VALUE_ITEM_STATUS_PM_ID);
        List<Document> pmSql = getSql(vo3, null, true, loginUser);
        pmSql.add(new Document("$count","count"));
        List<Document> pm = prjInfoDao.aggregate(pmSql);
        int pmCount = 0;
        if (CollectionUtils.isNotEmpty(pm)) {
            pmCount = (int) pm.get(0).get("count");
        }
        result.put(PrjConstant.PMS_VALUE_ITEM_STATUS_PM_ID,pmCount);
        //售前评估
        PmsValueItemVo vo4 = new PmsValueItemVo();
        vo4.setSbuIds(Arrays.asList(sbuId));
        vo4.setPreSalesTypeId(PrjConstant.PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id);
        List<Document> preSaleSql = getSql(vo4, null, true, loginUser);
        preSaleSql.add(new Document("$count","count"));
        List<Document> preSale = prjInfoDao.aggregate(preSaleSql);
        int preSaleCount = 0;
        if (CollectionUtils.isNotEmpty(preSale)) {
            preSaleCount = (int) preSale.get(0).get("count");
        }
        result.put(PrjConstant.PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id,preSaleCount);
        return result;
    }

    @Override
    public void operateValueItem(PmsValueItemVo vo,TeSysUser loginUser) {
        String type = vo.getType();
        List<ObjectId> prjIds = vo.getPrjIds();
        //查询项目
        List<TePrjInfo> prjInfoList = prjInfoDao.findPrjInfosByPrjIds(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            throw BusinessException.initExc("项目集不存在");
        }
        //获取最新版本
        List<String> prjBmkVerIds = new ArrayList<>();
        List<ObjectId> prjIdList = new ArrayList<>();
        List<TePrjInfo> prjInfos = new ArrayList<>();
        for (TePrjInfo prjInfo : prjInfoList){
            List<TePrjInfoPrjBmks> prjBmks = prjInfo.getPrjBmks();
            if (CollectionUtils.isEmpty(prjBmks)){
                continue;
            }
            boolean present = prjBmks.stream().filter(bmk -> bmk.getPrjBmkVerNo() != null && bmk.getPrjBmkVerId() != null)
                    .max((v1, v2) -> v1.getPrjBmkVerNo().compareTo(v2.getPrjBmkVerNo())).isPresent();
            if (present){
                ObjectId prjBmkVerId = prjBmks.stream().filter(bmk -> bmk.getPrjBmkVerNo() != null && bmk.getPrjBmkVerId() != null)
                        .max((v1, v2) -> v1.getPrjBmkVerNo().compareTo(v2.getPrjBmkVerNo())).get().getPrjBmkVerId();
                prjBmkVerIds.add(prjBmkVerId.toString());
                prjIdList.add(prjInfo.getPrjId());
                prjInfos.add(prjInfo);
            }
        }
        if (CollectionUtils.isEmpty(prjBmkVerIds)){
            throw BusinessException.initExc("项目集不存在");
        }
        //查询价值交付数据
        List<IDbCondition> conds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vo.getBizIds())){
            conds.add(new DC_I<ObjectId>(DFN.common__id,vo.getBizIds()));
        }
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.biz_prd.dot(DFN.common_cid), EmpSkillTestConstant.PRD_ID_AICM));
        conds.add(new DC_I<ObjectId>(DFN.biz_prj.dot(DFN.common_cid),prjIdList));
        conds.add(new DC_E(DFN.biz_bizType.dot(DFN.common_cid),PrjConstant.PMS_VALUE_ITEM_BIZ_TYPE_ID));
        conds.add(new DC_I<String>(DFN.biz_custFieldInfo.dot(DFN.biz_custFieldInfo_VER_NO),prjBmkVerIds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.biz_custFieldInfo);
        fieldNames.add(DFN.biz_prj);
        fieldNames.add(DFN.biz_notes);
        fieldNames.add(DFN.biz_name);
        List<TeBiz> bizList = bizDao.findByFieldAndConds(conds,fieldNames);
        if (CollectionUtils.isEmpty(bizList)){
            return;
        }
        Map<ObjectId, List<TeBiz>> bizMap = bizList.stream().collect(Collectors.groupingBy(biz -> biz.getPrj().getCid()));
        //备注
        Map<String,String> noteMap = new HashMap<>();
        List<UpdataData> updataDataList= new ArrayList<>();
        //打回，将该项目集下价值交付项状态改为“待修改”
        if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type)){
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_MODIFY)));
            sendMailByValueItemStatus(prjInfos,bizMap,type);
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_REJECT);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_CONFIRM.equals(type)){
            //确认，将该项目集下价值交付项状态改为“已确认”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_CONFIRM)));
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_CONFIRM);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_SUBMIT.equals(type)){
            //提交，将该项目集下价值交付项状态改为“待售前确认”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE)));
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_SUBMIT);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEval.equals(type)){
            //“已确认”的价值交付项，状态变为“PM自评”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_PM)));
            sendMailByValueItemStatus(prjInfos,bizMap,type);
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PmEval);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmSelfEval.equals(type)){
            //pm自我评价
            List<ObjectId> fileIds = vo.getFileIds();
            List<TeldNameUploadFile> uploadFiles = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fileIds)){
                List<TeFile> teFiles = fileDao.getFilesbyIds(fileIds);
                if (CollectionUtils.isNotEmpty(teFiles)){
                    for (TeFile file : teFiles){
                        TeldNameUploadFile uploadFile = new TeldNameUploadFile();
                        uploadFile.setIsValid(true);
                        uploadFile.setFile(new TeIdName(file.getId(),file.getFileName()));
                        uploadFile.setUploadTime(new Date());
                        uploadFile.setUploadUser(loginUser.trans2User());
                        uploadFiles.add(uploadFile);
                    }
                }
            }
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_Pm_Achieve_ID),Arrays.asList(vo.getHasAchieve())));
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_Pm_Describe_ID),vo.getDescribe()));
            if (CollectionUtils.isNotEmpty(uploadFiles)){
                updataDataList.add(new UpdataData(DFN.biz_files,BsonTool.listConversionBsonArray(uploadFiles)));
            }
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEvalSubmit.equals(type)){
            //“PM自评”的价值交付项，状态变为“售前评估”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_PreSale_Evaluation)));
            sendMailByValueItemStatus(prjInfos,bizMap,type);
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PmEvalSubmit);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PreSaleSelfEval.equals(type)){
            //“售前评估”自我评价
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_PreSale_Achieve_ID),Arrays.asList(vo.getHasAchieve())));
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_PreSale_Describe_ID),vo.getDescribe()));
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PreSaleEvalSubmit.equals(type)){
            //“售前评估”的价值交付项，状态变为“评估通告”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_Evaluation_Notice)));
            sendMailByValueItemStatus(prjInfos,bizMap,type);
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PreSaleEvalSubmit);
            noteMap.put("desc",vo.getOperateDesc());
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_EvalNotice.equals(type)){
            //“评估通告”的价值交付项，状态变为“已考评”
            updataDataList.add(new UpdataData(DFN.biz_custFieldInfo.dot(PrjConstant.PMS_VALUE_ITEM_STATUS_ID),Arrays.asList(PrjConstant.PMS_VALUE_ITEM_STATUS_Evaluated)));
            sendPreSalesNoticeMail(prjInfos,bizMap);
            noteMap.put("title",PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_EvalNotice);
            noteMap.put("desc",vo.getOperateDesc());
        }
        if (CollectionUtils.isNotEmpty(updataDataList)){
            bizDao.updateByConds(conds,updataDataList);
        }
        //备注
        if (MapUtils.isNotEmpty(noteMap)){
            List<BatchCondsUpsert> batchCondsUpserts = new ArrayList<>();
            TeNote note = new TeNote();
            note.setDesc(JsonUtil.toJsonString(noteMap));
            note.setAddTime(DateUtil.now());
            note.setAddUser(new TeUser(loginUser.getId(), loginUser.getLoginName(), loginUser.getUserName(), loginUser.getJobCode()));
            for (TeBiz biz : bizList){
                List<TeNote> notes = (biz.getNotes() == null ? new ArrayList<>() : biz.getNotes());
                notes.add(note);
                List<IDbCondition> updateOrInsertConds = new ArrayList<>();
                updateOrInsertConds.add(new DC_E(DFN.common__id,biz.getId()));
                List<UpdataData> updates = new ArrayList<>();
                updates.add(new UpdataData(DFN.biz_notes, BsonTool.listConversionBsonArray(notes)));
                batchCondsUpserts.add(new BatchCondsUpsert(updateOrInsertConds,updates));
            }
            if (CollectionUtils.isNotEmpty(batchCondsUpserts)){
                bizDao.batchSaveOrUpdate(batchCondsUpserts);
            }
        }

    }

    private void sendMailByValueItemStatus(List<TePrjInfo> prjInfoList,Map<ObjectId, List<TeBiz>> bizMap,String type){
        List<ObjectId> userIds = null;
        Map<String, ObjectId> buCodeMap = new HashMap<>();
        Map<ObjectId, Set<TeSysDefRoleUser2User>> buUserMap = new HashMap<>();
        //邮件标题
        String subject = "";
        //邮件内容
        String content = "";
        if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type) || PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEval.equals(type)){
            //获取项目经理
            userIds = prjInfoList.stream().filter(prj -> prj.getPmUser() != null && prj.getPmUser().getUserId() != null).map(prj -> prj.getPmUser().getUserId()).collect(Collectors.toList());
            subject = PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type) ? "待修改的邮件通知" : "待PM自评的邮件通知";
            content = PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type) ? "内容被打回" : "待完成自评";
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEvalSubmit.equals(type)){
            //获取pso总监
            userIds = prjInfoList.stream().filter(prj -> prj.getPreSaleUser() != null && prj.getPreSaleUser().getUserId() != null).map(prj -> prj.getPreSaleUser().getUserId()).collect(Collectors.toList());
            List<ObjectId> provIds = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(provIds)){
                //Pso总监
                List<TeSysDefRoleUser> psoDirectList = sysDefRoleUserDao.getSysUserRole(null, Collections.singletonList(PrjConstant.DEF_ID_DIRECTOR_ROLE), provIds, SysDefTypeCodeName.AI_BU);
                if (CollectionUtils.isNotEmpty(psoDirectList)){
                    userIds = psoDirectList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                    buUserMap = psoDirectList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                            .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser(), Collectors.toSet())));
                }
            }
            subject = "待价值交付评估的邮件通知";
            content = "待完成价值交付评估";
        }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PreSaleEvalSubmit.equals(type)){
            //获取bu
            List<String> buCodes = prjInfoList.stream().filter(prj -> StringUtil.isNotNull(prj.getSbuId())).map(TePrjInfo::getSbuId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(buCodes)){
                return;
            }
            //查询bu
            List<SysDef> buList = sysDefService.getSysDefsByCodeNames(buCodes, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isEmpty(buList)){
                return;
            }
            buCodeMap = buList.stream().collect(Collectors.toMap(SysDef::getCodeName, SysDef::getId, (v1, v2) -> v2));
            List<ObjectId> buIds = buList.stream().map(SysDef::getId).collect(Collectors.toList());
            List<ObjectId> roleIds = new ArrayList<>();
            roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
            //获取BU运营管理员
            List<TeSysDefRoleUser> buOperateAdmins = sysDefRoleUserDao.getSysUserRole(null, roleIds, buIds, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(buOperateAdmins)){
                userIds = buOperateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                buUserMap = buOperateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser(), Collectors.toSet())));
            }
            subject = "待评估的邮件通知";
            content = "待完成评估通告";
        }else {
            return;
        }
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        //查询人员
        List<TeSysUser> userList = sysUserService.getUsersByIds(userIds);
        if (CollectionUtils.isEmpty(userList)){
            return;
        }
        //组合数据
        Map<ObjectId, String> userMailBoxMap = userList.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, TeSysUser::getMailBox));
        if (MapUtils.isEmpty(userMailBoxMap)){
            return;
        }
        List<MailInfo> mailInfoList = new ArrayList<>();
        for (TePrjInfo prjInfo : prjInfoList){
            List<TeUser> users = new ArrayList<>();
            if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type) || PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEval.equals(type)){
                TePrjInfo2User pmUser = prjInfo.getPmUser();
                if (pmUser == null || pmUser.getUserId() == null || StringUtil.isNull(userMailBoxMap.get(pmUser.getUserId()))){
                    continue;
                }
                TeUser user = new TeUser(pmUser.getUserId(), pmUser.getLoginName(), pmUser.getUserName(),pmUser.getJobCode());
                users.add(user);
            }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEvalSubmit.equals(type)){
                TeIdNameCn prov = prjInfo.getProv();
                if (prov == null || prov.getCid() == null || CollectionUtils.isEmpty(buUserMap.get(prov.getCid()))){
                    continue;
                }
                Set<TeSysDefRoleUser2User> psoDirectors = buUserMap.get(prov.getCid());
                for (TeSysDefRoleUser2User roleUser : psoDirectors){
                    TeUser user = new TeUser(roleUser.getUserId(), roleUser.getLoginName(), roleUser.getUserName(),roleUser.getJobCode());
                    users.add(user);
                }
            }else {
                String sbuId = prjInfo.getSbuId();
                if (StringUtil.isNull(sbuId) || buCodeMap.get(sbuId) == null){
                    continue;
                }
                ObjectId buDefId = buCodeMap.get(sbuId);
                Set<TeSysDefRoleUser2User> roleUsers = buUserMap.get(buDefId);
                if (CollectionUtils.isEmpty(roleUsers)){
                    continue;
                }
                for (TeSysDefRoleUser2User roleUser : roleUsers){
                    TeUser user = new TeUser(roleUser.getUserId(), roleUser.getLoginName(), roleUser.getUserName(),roleUser.getJobCode());
                    users.add(user);
                }
            }
            if (CollectionUtils.isEmpty(users)){
                return;
            }
            for (TeUser user : users){
                StringBuilder descStr = new StringBuilder();
                if (MapUtils.isNotEmpty(bizMap) && CollectionUtils.isNotEmpty(bizMap.get(prjInfo.getPrjId()))){
                    List<TeBiz> teBizs = bizMap.get(prjInfo.getPrjId());
                    //获取全部备注
                    Map<Date, List<TeNote>> notesMap = teBizs.stream().filter(biz -> CollectionUtils.isNotEmpty(biz.getNotes())).map(TeBiz::getNotes)
                            .flatMap(Collection::stream).filter(note -> note.getAddTime() != null)
                            .collect(Collectors.groupingBy(TeNote::getAddTime));
                    if (MapUtils.isNotEmpty(notesMap)){
                        List<Date> dates = new ArrayList<>(notesMap.keySet());
                        dates = dates.stream().sorted().collect(Collectors.toList());
                        for (Date date : dates){
                            //相同时间取一条
                            TeNote note = notesMap.get(date).get(0);
                            String desc = note.getDesc();
                            if (StringUtil.isNotNull(desc)){
                                Map<String, Object> map = JsonUtil.toMap(desc);
                                if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_REJECT.equals(type)){
                                    if (PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_REJECT.equals(map.get("title")) || "售前确认-打回".equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }
                                    /*if ("售前确认-打回".equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }*/
                                }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEval.equals(type)){
                                    if (PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PmEval.equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }
                                }else if (PrjConstant.VALUE_ITEM_OPERATE_TYPE_PmEvalSubmit.equals(type)){
                                    if (PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PmEvalSubmit.equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }
                                }else{
                                    if (PrjConstant.VALUE_ITEM_OPERATE_NOTE_NAME_PreSaleEvalSubmit.equals(map.get("title")) || "售前评估".equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }
                                    /*if ("售前评估".equals(map.get("title"))){
                                        descStr.append(StringUtil.getNotNullStr(map.get("desc")));
                                    }*/
                                }

                            }
                        }
                    }
                }
                String toListMailBox = userMailBoxMap.get(user.getUserId());
                //邮件内容
                PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
                mailVo.setPrjNameCode(StringUtil.getNotNullStr(prjInfo.getPrjCode()) + "/" + prjInfo.getPrjName());
                mailVo.setPmUserName(StringUtil.getNotNullStr(user.getUserName()));
                mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));
                mailVo.setDesc(StringUtil.isNotNull(descStr.toString()) ? "处理意见："+descStr.toString() : null);
                mailVo.setContent(content);
                //邮件
                MailInfo mailInfo = new MailInfo();
                mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
                mailInfo.setFromMailboxName("<EMAIL>");
                mailInfo.setToList(Collections.singletonList(toListMailBox));
                mailInfo.setSubject(subject);
                mailInfo.setSubsystem(DMPSubsystem.PMS);
                mailInfo.setContent(getMailContent(mailVo, "PrjValueItemModify.ftl"));
                mailInfoList.add(mailInfo);
            }
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
    }

    private void sendPreSalesNoticeMail(List<TePrjInfo> prjInfoList,Map<ObjectId, List<TeBiz>> bizMap){
        List<ObjectId> userIds = new ArrayList<>();
        List<ObjectId> provIds = new ArrayList<>();
        List<String> sbuIds = new ArrayList<>();
        for (TePrjInfo prjInfo : prjInfoList){
            //项目经理
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if (pmUser == null || pmUser.getUserId() == null){
                continue;
            }
            userIds.add(pmUser.getUserId());
            //售前人员
           /* TeUser preSaleUser = prjInfo.getPreSaleUser();
            if (preSaleUser != null && preSaleUser.getUserId() != null){
                userIds.add(preSaleUser.getUserId());
            }*/
            List<TePrjInfo2SrdRoleUser> srdRoleUser = prjInfo.getSrdRoleUser();
            if (CollectionUtils.isNotEmpty(srdRoleUser)){
                //分类
                TeIdName level = prjInfo.getLevel();
                List<ObjectId> userIdList = null;
                if (level != null && PrjConstant.PRJ_LEVEL_LIST_A_B.equals(level.getCid())){
                    //srd总监
                    userIdList = srdRoleUser.stream()
                            .filter(user -> user.getRole() != null && PrjConstant.DEF_ID_DIRECTOR_ROLE.equals(user.getRole().getCid()) &&
                                    user.getUser() != null && user.getUser().getUserId() != null).map(TePrjInfo2SrdRoleUser::getUser)
                            .map(TePrjInfo2SrdPlSm::getUserId).collect(Collectors.toList());
                    userIds.addAll(userIdList);
                }else {
                    //srd经理
                    userIdList = srdRoleUser.stream()
                            .filter(user -> user.getRole() != null && PrjConstant.DEF_ID_MANAGER_ROLE.equals(user.getRole().getCid()) &&
                                    user.getUser() != null && user.getUser().getUserId() != null).map(TePrjInfo2SrdRoleUser::getUser)
                            .map(TePrjInfo2SrdPlSm::getUserId).collect(Collectors.toList());
                    userIds.addAll(userIdList);
                }
                if (CollectionUtils.isNotEmpty(userIdList)){
                    userIds.addAll(userIdList);
                }
            }
            //省份
            TeIdNameCn prov = prjInfo.getProv();
            if (prov != null && prov.getCid() != null){
                provIds.add(prov.getCid());
            }
            //bu
            String sbuId = prjInfo.getSbuId();
            if (StringUtil.isNotNull(sbuId)){
                sbuIds.add(sbuId);
            }
        }
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        //角色
        List<ObjectId> roleIds = new ArrayList<>();
        //bu分组
        Map<String, ObjectId> buCodeMap = new HashMap<>();
        Map<ObjectId, Set<ObjectId>> buUserMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sbuIds)){
            //查询bu运营管理员
            List<SysDef> buList = sysDefService.getSysDefsByCodeNames(sbuIds, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(buList)){
                buCodeMap = buList.stream().collect(Collectors.toMap(SysDef::getCodeName, SysDef::getId, (v1, v2) -> v2));
                List<ObjectId> buIds = buList.stream().map(SysDef::getId).collect(Collectors.toList());
                roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
                //获取BU运营管理员
                List<TeSysDefRoleUser> buOperateAdmins = sysDefRoleUserDao.getSysUserRole(null, roleIds, buIds, SysDefTypeCodeName.AI_BU);
                if (CollectionUtils.isNotEmpty(buOperateAdmins)){
                    List<ObjectId> buAdminUserIds = buOperateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                            .map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(buAdminUserIds)){
                        userIds.addAll(buAdminUserIds);
                    }
                    buUserMap = buOperateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                            .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser().getUserId(), Collectors.toSet())));
                }
            }

        }
        //省份运营管理员
        Map<ObjectId, Set<ObjectId>> operateAdminMap = new HashMap<>();
        //Pso总监
        Map<ObjectId, Set<ObjectId>> directorMap = new HashMap<>();
        //Pso经理
        Map<ObjectId, Set<ObjectId>> managerMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(provIds)){
            //省份运营管理员
            roleIds.clear();
            roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
            List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.ABP_PROV);
            if (CollectionUtils.isNotEmpty(operateAdmins)){
                List<ObjectId> userIdList = operateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userIdList)){
                    userIds.addAll(userIdList);
                }
                //根据省份分组
                operateAdminMap = operateAdmins.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser().getUserId(), Collectors.toSet())));
            }
            //Pso总监
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
            List<TeSysDefRoleUser> directorList = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(directorList)){
                List<ObjectId> userIdList = directorList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userIdList)){
                    userIds.addAll(userIdList);
                }
                //根据省份分组
                directorMap = directorList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser().getUserId(), Collectors.toSet())));
            }
            //pso经理
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
            List<TeSysDefRoleUser> managerList = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(managerList)){
                List<ObjectId> userIdList = managerList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userIdList)){
                    userIds.addAll(userIdList);
                }
                //根据省份分组
                managerMap = managerList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                        .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId, Collectors.mapping(role -> role.getRoleUser().getUserId(), Collectors.toSet())));
            }
        }

        //查询人员
        List<TeSysUser> userList = sysUserService.getUsersByIds(userIds);
        if (CollectionUtils.isEmpty(userList)){
            return;
        }
        //组合数据
        Map<ObjectId, String> userMailBoxMap = userList.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, TeSysUser::getMailBox));
        if (MapUtils.isEmpty(userMailBoxMap)){
            return;
        }
        //邮件
        List<MailInfo> mailInfoList = new ArrayList<>();
        for (TePrjInfo prjInfo : prjInfoList){
            //收件人：项目经理
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if (pmUser == null || pmUser.getUserId() == null || StringUtil.isNull(userMailBoxMap.get(pmUser.getUserId()))){
                continue;
            }
            String toListMailBox = userMailBoxMap.get(pmUser.getUserId());
            if (MapUtils.isEmpty(bizMap) || CollectionUtils.isEmpty(bizMap.get(prjInfo.getPrjId()))){
                continue;
            }
            List<TeBiz> bizList = bizMap.get(prjInfo.getPrjId());
            //抄送人：售前、三位一体、BU运营管理员、省份运营管理员
            List<String> ccListMailBox = new ArrayList<>();
           /* TeUser preSaleUser = prjInfo.getPreSaleUser();
            if (preSaleUser != null && preSaleUser.getUserId() != null && StringUtil.isNull(userMailBoxMap.get(preSaleUser.getUserId()))){
                String mailBox = userMailBoxMap.get(preSaleUser.getUserId());
                ccListMailBox.add(mailBox);
            }*/
            //srd总监、经理
            List<TePrjInfo2SrdRoleUser> srdRoleUser = prjInfo.getSrdRoleUser();
            //分类
            TeIdName level = prjInfo.getLevel();
            if (CollectionUtils.isNotEmpty(srdRoleUser)){
                List<ObjectId> userIdList = null;
                if (level != null && PrjConstant.PRJ_LEVEL_LIST_A_B.equals(level.getCid())){
                    //srd总监
                    userIdList = srdRoleUser.stream()
                            .filter(user -> user.getRole() != null && PrjConstant.DEF_ID_DIRECTOR_ROLE.equals(user.getRole().getCid()) &&
                                    user.getUser() != null && user.getUser().getUserId() != null).map(TePrjInfo2SrdRoleUser::getUser)
                            .map(TePrjInfo2SrdPlSm::getUserId).collect(Collectors.toList());
                }else {
                    //srd经理
                    userIdList = srdRoleUser.stream()
                            .filter(user -> user.getRole() != null && PrjConstant.DEF_ID_MANAGER_ROLE.equals(user.getRole().getCid()) &&
                                    user.getUser() != null && user.getUser().getUserId() != null).map(TePrjInfo2SrdRoleUser::getUser)
                            .map(TePrjInfo2SrdPlSm::getUserId).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(userIdList)){
                    for (ObjectId userId : userIdList){
                        String mailBox = userMailBoxMap.get(userId);
                        if (StringUtil.isNotNull(mailBox) && !ccListMailBox.contains(mailBox)){
                            ccListMailBox.add(mailBox);
                        }
                    }
                }
            }
            //省份
            TeIdNameCn prov = prjInfo.getProv();
            if (prov != null && prov.getCid() != null){
                //省份运营管理员
                if (MapUtils.isNotEmpty(operateAdminMap) && CollectionUtils.isNotEmpty(operateAdminMap.get(prov.getCid()))){
                    Set<ObjectId> userIdSet = operateAdminMap.get(prov.getCid());
                    for (ObjectId userId : userIdSet){
                        String mailBox = userMailBoxMap.get(userId);
                        if (StringUtil.isNotNull(mailBox) && !ccListMailBox.contains(mailBox)){
                            ccListMailBox.add(mailBox);
                        }
                    }
                }
                Set<ObjectId> userIdSet = null;
                if (level != null && PrjConstant.PRJ_LEVEL_LIST_A_B.equals(level.getCid())){
                    //pso总监
                    if (MapUtils.isNotEmpty(directorMap) && CollectionUtils.isNotEmpty(directorMap.get(prov.getCid()))){
                        userIdSet = directorMap.get(prov.getCid());
                    }
                }else {
                    //pso经理
                    if (MapUtils.isNotEmpty(managerMap) && CollectionUtils.isNotEmpty(managerMap.get(prov.getCid()))){
                        userIdSet = managerMap.get(prov.getCid());
                    }
                }
                //pso总监、经理
                if (CollectionUtils.isNotEmpty(userIdSet)){
                    for (ObjectId userId : userIdSet){
                        String mailBox = userMailBoxMap.get(userId);
                        if (StringUtil.isNotNull(mailBox) && !ccListMailBox.contains(mailBox)){
                            ccListMailBox.add(mailBox);
                        }
                    }
                }
            }
            //bu
            String sbuId = prjInfo.getSbuId();
            if (StringUtil.isNotNull(sbuId) && MapUtils.isNotEmpty(buCodeMap) && MapUtils.isNotEmpty(buUserMap) && buCodeMap.get(sbuId) != null){
                ObjectId sbuDefId = buCodeMap.get(sbuId);
                if (CollectionUtils.isNotEmpty(buUserMap.get(sbuDefId))){
                    Set<ObjectId> userIdSet = buUserMap.get(sbuDefId);
                    for (ObjectId userId : userIdSet){
                        String mailBox = userMailBoxMap.get(userId);
                        if (StringUtil.isNotNull(mailBox) && !ccListMailBox.contains(mailBox)){
                            ccListMailBox.add(mailBox);
                        }
                    }
                }
            }

            List<PmsValueItemVo> pmsValueItemVos = new ArrayList<>();
            for (TeBiz biz : bizList){
                PmsValueItemVo pmsValueItemVo = new PmsValueItemVo();

                pmsValueItemVo.setValueItemName(biz.getName());
                Map<ObjectId, Object> custFieldInfo = biz.getCustFieldInfo();
                if (MapUtils.isNotEmpty(custFieldInfo)){
                    //价值评判标准
                    pmsValueItemVo.setJudgeStand(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAND_ID)));
                    //权重
                    pmsValueItemVo.setWeight(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_WEIGHT_ID)));
                    //评判阶段
                    if (custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAGE_ID) != null){
                        List<TeIdName> judgeSatges = (List<TeIdName>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_JUDGE_STAGE_ID);
                        pmsValueItemVo.setJudgeStage(judgeSatges.get(0));
                    }
                    //是否达成
                    if (custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Achieve_ID) != null){
                        List<TeIdName> hasAchieves = (List<TeIdName>)custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Achieve_ID);
                        pmsValueItemVo.setHasAchieve(hasAchieves.get(0));
                    }
                    //说明
                    pmsValueItemVo.setDescribe(StringUtil.getNotNullStr(custFieldInfo.get(PrjConstant.PMS_VALUE_ITEM_PreSale_Describe_ID)));
                }
                pmsValueItemVos.add(pmsValueItemVo);
            }
            //邮件内容
            PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
            mailVo.setPrjNameCode(StringUtil.getNotNullStr(prjInfo.getPrjCode()) + "/" + prjInfo.getPrjName());
            mailVo.setPmUserName(StringUtil.getNotNullStr(pmUser.getUserName()));
            mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));
            mailVo.setPmsValueItemVos(pmsValueItemVos);
            //邮件
            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(Collections.singletonList(toListMailBox));
            mailInfo.setCcList(ccListMailBox);
            mailInfo.setSubject("项目价值交付项评估结果的通告");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mailVo, "PreSalesNoticeMail.ftl"));
            mailInfoList.add(mailInfo);
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
    }
    /**
     * 获取邮件正文
     * @param mailVo
     * @return
     */
    protected String getMailContent(PrjDvtMgtMailVo mailVo, String templatename) {
        Template template = null;
        try {
            template = freeMarkerConfig.getConfiguration().getTemplate(templatename,"utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        Writer out = new StringWriter();
        try {
            // 输出数据到模板中，生成文件。
            template.process(mailVo, out);
            out.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toString();
    }

    @Override
    public void preSalesConfirmMail(List<ObjectId> prjIds) {
        SysDef sbu = sysDefService.getSysDefByCodeName(PrjConstant.BU_CODE_BSC, SysDefTypeCodeName.AI_BU);
        if (sbu == null){
            throw BusinessException.initExc("当前BU不存在");
        }
        //每月1日，定时任务发起售前确认的邮件提醒功能
        //收件人：售前,抄送人：BU运营
        PmsValueItemVo vo = new PmsValueItemVo();
        vo.setSbuIds(Arrays.asList(PrjConstant.BU_CODE_BSC));
        vo.setPreSalesTypeId(PrjConstant.PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID);
        //收件人id
        List<ObjectId> toListUserIds = new ArrayList<>();
        //获取查询语句
        List<Document> sql = getSql(vo, prjIds, null, null);
        //查询数据
        MongoCursor<Document> cursor =mongoTemplate.getCollection(DBT.PRJINFO.n()).aggregate(sql, Document.class).allowDiskUse(true).cursor();
        Map<ObjectId,List<PrjPreSalesVo>> preSaleUserMap = new HashMap<>();
        while (cursor.hasNext()){
            Document next = cursor.next();
            PrjPreSalesVo salesVo = new PrjPreSalesVo();
            salesVo.setPrjName(next.getString("prjName"));
            salesVo.setPrjCode(next.getString("prjCode"));
            Document pmUser = (Document) next.get("pmUser");
            if (pmUser != null){
                salesVo.setPmUser(StringUtil.getNotNullStr(pmUser.getString("userName")));
            }
           /* Document preSaleUser = (Document) next.get("preSaleUser");
            if (preSaleUser == null || preSaleUser.getObjectId("userId") == null){
                continue;
            }*/
            List<Document> psos = (List<Document>)next.get("pso");
            ObjectId userId = null;
            if (CollectionUtils.isNotEmpty(psos)){
                for (Document pso : psos){
                    List<Document> roles = (List<Document>)next.get("role");
                    if (CollectionUtils.isEmpty(roles)){
                        continue;
                    }
                    Document role = roles.get(0);
                    Document roleUser = (Document)pso.get("roleUser");
                    if (roleUser != null){
                        TeUser user = new TeUser(roleUser.getObjectId("userId"),
                                roleUser.getString("loginName"),roleUser.getString("userName"),roleUser.getString("jobCode"));
                        if (PrjConstant.DEF_ID_MANAGER_ROLE.equals(role.getObjectId("roleId"))){
                            userId = user.getUserId();
                            salesVo.setPsoManager(user);
                            toListUserIds.add(userId);
                        }
                    }
                }
            }
            if (userId == null){
                continue;
            }
            salesVo.setProv(next.getString("prov"));
            salesVo.setRegion(next.getString("region"));

            List<PrjPreSalesVo> orDefault = preSaleUserMap.getOrDefault(userId, new ArrayList<>());
            orDefault.add(salesVo);
            preSaleUserMap.put(userId,orDefault);
        }
        if (CollectionUtils.isEmpty(toListUserIds)){
            return;
        }
        List<TeSysUser> toList = sysUserService.getUsersByIds(toListUserIds);
        if (CollectionUtils.isEmpty(toList)){
            throw BusinessException.initExc("收件人不存在");
        }
        Map<ObjectId, TeSysUser> toListMap = toList.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, Function.identity()));
        //查询BU运营管理员
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(PrjBudgetConstants.PrjBudgetAuditRole.DEF_ID_BU_PRJ_BUDGET_ADMIN);
        List<TeSysDefRoleUser> buUsers = sysDefRoleUserDao.getUsers(roleIds, Collections.singletonList(sbu.getId()), SysDefTypeCodeName.AI_BU);
        //抄送人邮箱
        List<String> ccListMailBoxs = null;
        if (CollectionUtils.isNotEmpty(buUsers)){
            List<ObjectId> ccListIds = buUsers.stream().filter(user -> user.getRoleUser() != null && user.getRoleUser().getUserId() != null).map(user -> user.getRoleUser().getUserId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ccListIds)){
                List<TeSysUser> ccList = sysUserService.getUsersByIds(ccListIds);
                if (CollectionUtils.isNotEmpty(ccList)){
                    ccListMailBoxs = ccList.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).map(TeSysUser::getMailBox).collect(Collectors.toList());
                }
            }
        }
        List<MailInfo> mailInfoList = new ArrayList<>();
        for (ObjectId userId : preSaleUserMap.keySet()){
            List<PrjPreSalesVo> prjPreSalesVos = preSaleUserMap.get(userId);
            TeSysUser teSysUser = toListMap.get(userId);
            if (teSysUser == null || CollectionUtils.isEmpty(prjPreSalesVos)){
                continue;
            }
            PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
            mailVo.setPreSaleUser(StringUtil.getNotNullStr(teSysUser.getUserName())+"/"+StringUtil.getNotNullStr(teSysUser.getLoginName()));
            mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));
            List<PrjDvtMgtMailVo> mailVos = new ArrayList<>();
            for (PrjPreSalesVo salesVo : prjPreSalesVos){
                PrjDvtMgtMailVo saleMailVo = new PrjDvtMgtMailVo();
                saleMailVo.setRegion(salesVo.getRegion());
                saleMailVo.setProv(salesVo.getProv());
                saleMailVo.setPrjNameCode(StringUtil.getNotNullStr(salesVo.getPrjCode())+"/"+StringUtil.getNotNullStr(salesVo.getPrjName()));
                saleMailVo.setPmUserName(salesVo.getPmUser());
                mailVos.add(saleMailVo);
            }
            mailVo.setMailVos(mailVos);
            //邮件
            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(Collections.singletonList(teSysUser.getMailBox()));
            if (CollectionUtils.isNotEmpty(ccListMailBoxs)){
                mailInfo.setCcList(ccListMailBoxs);
            }
            mailInfo.setSubject("价值交付项确认的提醒");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mailVo, "prjPreSaleConfirm.ftl"));
            mailInfoList.add(mailInfo);
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
    }

    @Override
    public List<TeNote> getPrjPreSalesOperateHistory(ObjectId prjId, ObjectId prjBmkVerId,List<ObjectId> bizIds) {
        //查询价值交付数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<ObjectId>(DFN.common__id,bizIds));
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.biz_prd.dot(DFN.common_cid), EmpSkillTestConstant.PRD_ID_AICM));
        conds.add(new DC_E(DFN.biz_prj.dot(DFN.common_cid),prjId));
        conds.add(new DC_E(DFN.biz_bizType.dot(DFN.common_cid),PrjConstant.PMS_VALUE_ITEM_BIZ_TYPE_ID));
        conds.add(new DC_E(DFN.biz_custFieldInfo.dot(DFN.biz_custFieldInfo_VER_NO),prjBmkVerId.toString()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.biz_notes);
        List<TeBiz> bizList = bizDao.findByFieldAndConds(conds,fieldNames);

        List<TeNote> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bizList)){
            //获取全部备注
            Map<Date, List<TeNote>> notesMap = bizList.stream().filter(biz -> CollectionUtils.isNotEmpty(biz.getNotes())).map(TeBiz::getNotes)
                    .flatMap(Collection::stream).filter(note -> note.getAddTime() != null)
                    .collect(Collectors.groupingBy(TeNote::getAddTime));
            if (MapUtils.isNotEmpty(notesMap)){
                for (Date date : notesMap.keySet()){
                    //相同时间取一条
                    TeNote note = notesMap.get(date).get(0);
                    result.add(note);
                }
                result = result.stream().sorted(Comparator.comparing(TeNote::getAddTime).reversed()).collect(Collectors.toList());
            }
        }
        return result;
    }
}
