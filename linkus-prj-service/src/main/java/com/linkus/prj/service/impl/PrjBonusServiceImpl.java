package com.linkus.prj.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.BeanMapUtils;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.common.model.TeUser;
import com.linkus.itf.api.client.ItfAiBizTripApplyFeignClient;
import com.linkus.itf.api.model.vo.AiBizTripApplyVo;
import com.linkus.itf.api.model.vo.TripDays;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.IPfmIndexDao;
import com.linkus.prj.dao.IPrjEmpPfmApplyDao;
import com.linkus.prj.dao.IPrjEmpPfmDao;
import com.linkus.prj.model.TePfmIndex;
import com.linkus.prj.model.TePrjEmpPfm;
import com.linkus.prj.model.TePrjEmpPfmApply;
import com.linkus.prj.model.TePrjInfo;
import com.linkus.prj.model.TePrjInfo2User;
import com.linkus.prj.model.TePrjInfoSubPrj;
import com.linkus.prj.model.TePrjReview;
import com.linkus.prj.model.TeWorkScore;
import com.linkus.prj.model.vo.PrjBonusAnalyseVo;
import com.linkus.prj.model.vo.PrjBonusDurationVo;
import com.linkus.prj.model.vo.PrjBonusRoleReportVo;
import com.linkus.prj.model.vo.PrjEmpPfmApplyVo;
import com.linkus.prj.model.vo.PrjEmpPfmVo;
import com.linkus.prj.model.vo.PrjPhaseAndResultReviewVo;
import com.linkus.prj.service.IPrjBonusService;
import com.linkus.prj.service.IPrjInfoService;
import com.linkus.prj.service.IPrjReviewService;
import com.linkus.rms.api.client.RmsEmpDateFeignClient;
import com.linkus.rms.api.model.RmsEmpDate;
import com.linkus.sfltask.service.IMailSendTaskService;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysCal;
import com.linkus.sys.service.ISysDefCnfgService;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysApprStatusCnfgDao;
import com.linkus.sysuser.dao.ISysUserApplyDao;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeCheckEmp;
import com.linkus.sysuser.model.TePassStatus;
import com.linkus.sysuser.model.TeSysApprStatusCnfg;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.model.TeSysUserApply;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserApplyService;
import com.linkus.sysuser.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("prjBonusServiceImpl")
public class PrjBonusServiceImpl implements IPrjBonusService {
    @Autowired
    private IPrjInfoService prjInfoService;
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private IPrjEmpPfmApplyDao prjEmpPfmApplyDao;
    @Autowired
    private ISysApprStatusCnfgDao sysApprStatusCnfgDao;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysUserDao sysUserDao;
    @Autowired
    private ISysUserApplyDao sysUserApplyDao;
    @Autowired
    private RmsEmpDateFeignClient rmsEmpDateFeignClient;
    @Autowired
    private ISysCalDao sysCalDao;
    @Autowired
    private IPrjEmpPfmDao prjEmpPfmDao;
    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;
    @Autowired
    private ISysDefCnfgService sysDefCnfgService;
    @Autowired
    private ISysUserApplyService sysUserApplyService;
    @Autowired
    private IPrjReviewService prjReviewService;
    @Autowired
    private IPfmIndexDao pfmIndexDao;
    @Autowired
    private IMailSendTaskService mailSendTaskService;
    @Autowired
    private ItfAiBizTripApplyFeignClient aiBizTripApplyFeignClient;


    @Override
    public void initPrjBonus(ObjectId prjId, String startYm, String endYm, String site, TeSysUser loginUser) {
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null) {
            throw BusinessException.initExc("项目不存在");
        }
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isEmpty(subPrjs)){
            throw BusinessException.initExc("子项目不存在");
        }
        List<SysDef> perMmRatios = sysDefService.getSysDefsBySrc(PrjConstant.CMC_DEF_ID, SysDefTypeCodeName.PMS_PRJ_BONUS_RATIO_PER_EMP_MONTH);
        if (CollectionUtils.isEmpty(perMmRatios)) {
            throw BusinessException.initExc("每人月奖金系数配置为空");
        }
        List<SysDef> prjLevelRatios = sysDefService.getSysDefsBySrc(PrjConstant.PRJ_LEVEL_ID, SysDefTypeCodeName.OPTION);
        if (CollectionUtils.isEmpty(prjLevelRatios)) {
            throw BusinessException.initExc("项目等级配置为空");
        }
        TeSysApprStatusCnfg statusCnfg = sysApprStatusCnfgDao.queryApprStatusCnfg(PrjConstant.DEF_ID_BSC_PRJ_BONUS_APPLY, PrjConstant.DEF_ID_AMS_APPR_STATUS_START);
        Map<String, Object> keyWordHolder = new HashMap<>();
        keyWordHolder.put("SYS_APPR_APPLY_SRCDEF_CID", prjId);
        List<TeSysUser> toCheckUsers = sysUserService.getUsersBySysApprStatusCnfg(statusCnfg, keyWordHolder);
        if (CollectionUtils.isEmpty(toCheckUsers)) {
            throw BusinessException.initExc("审批人不能为空");
        }

        Map<ObjectId, String> prjLevelRatiosMap = prjLevelRatios.stream().filter(level->level.getValue()!=null)
                .collect(Collectors.toMap(SysDef::getId, SysDef::getValue));

        SysDef sysDef = sysDefService.getSysDefById(PrjConstant.DEF_ID_AMS_APPR_STATUS_START);
        TePrjEmpPfmApply pfmApply = new TePrjEmpPfmApply();
        pfmApply.setBuCode(PrjConstant.BU_CODE_BSC);
        pfmApply.setStatus(sysDef.trans2IdNameCn());
        TeIdNameCn prj = new TeIdNameCn();
        prj.setCid(prjInfo.getPrjId());
        prj.setName(prjInfo.getPrjName());
        prj.setCodeName(prjInfo.getPrjCode());
        pfmApply.setPrj(prj);
        pfmApply.setStartYm(startYm);
        pfmApply.setEndYm(endYm);
        pfmApply.setSite(site);
        pfmApply.setPrePrjMm(0D);
        pfmApply.setAddUser(loginUser.trans2User());
        pfmApply.setAddTime(new Date());
        pfmApply.setIsValid(true);
        pfmApply.setPerMmRatio(Double.valueOf(perMmRatios.get(0).getValue()));
        pfmApply.setPrjLevelRatio(Double.valueOf(prjLevelRatiosMap.get(prjInfo.getLevel().getCid())));
        TePrjEmpPfmApply empPfmApply = prjEmpPfmApplyDao.insert(pfmApply);


        SysDef pendingDef = sysDefService.getSysDefById(PrjConstant.DEF_ID_STATUS_PENDING);
        SysDef bscPrjBonusApplyDef = sysDefService.getSysDefById(PrjConstant.DEF_ID_BSC_PRJ_BONUS_APPLY);
        SysDef prjDef = sysDefService.getSysDefById(prjId);

        TeSysUserApply apply = new TeSysUserApply();
        apply.setRcdId(empPfmApply.getId());
        apply.setAllPassIsRequired(statusCnfg.getAllPassIsRequired());
        apply.setBuCode(PrjConstant.BU_CODE_BSC);
        apply.setType(bscPrjBonusApplyDef.trans2IdNameCn());
        apply.setApplyTime(new Date());
        apply.setAddTime(new Date());
        apply.setApplyUser(loginUser.trans2User());
        apply.setStatus(pendingDef.trans2IdNameCn());
        apply.setIsValid(true);
        apply.setSrcDef(prjDef.trans2IdNameCn());
        apply.setRcdStatus(statusCnfg.getStatus());
        List<TeCheckEmp> toCheckEmps = new ArrayList<>();
        for (TeSysUser toCheckUser : toCheckUsers) {
            TeCheckEmp emp = new TeCheckEmp();
            emp.setEmp(toCheckUser.trans2User());
            toCheckEmps.add(emp);
        }
        apply.setToCheckEmps(toCheckEmps);
        TeSysUserApply userApply = sysUserApplyDao.insert(apply);

        ArrayList<String> subPrjCodes = subPrjs.stream().map(TePrjInfoSubPrj::getCode).collect(Collectors.toCollection(ArrayList::new));
        ArrayList<ObjectId> subPrjIds = subPrjs.stream().map(TePrjInfoSubPrj::getCid).collect(Collectors.toCollection(ArrayList::new));
        String start = startYm+"-01";
        String end = DateUtil.getMonthLastDay(DateUtil.parseDate(endYm, DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_FORMAT);
        List<String> ymBetween = DateUtil.getYmBetween(startYm,endYm);
        Map<String,Integer> ymMap = new HashMap<>();
        for (String ym : ymBetween) {
            List<TeSysCal> sysCals = sysCalDao.getWorkDays(ym);
            ymMap.put(ym, sysCals.size());
        }
        List<RmsEmpDate> mdList = rmsEmpDateFeignClient.listPfmDataByDate(prjInfo.getBgId(), start,end, new ArrayList<>(), subPrjCodes).getData(true);

        // 按月份分组
        Map<String, Map<ObjectId, List<RmsEmpDate>>> rmsDateMap = mdList.stream()
                .collect(Collectors.groupingBy(project -> {
                            LocalDate date = LocalDate.parse(project.getDate());
                            return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        },
                        Collectors.groupingBy(project -> project.getEmp().getUserId())
                ));

        List<ObjectId> empIds = mdList.stream().map(RmsEmpDate::getEmp).map(TeUser::getUserId).distinct().collect(Collectors.toList());
        Map<ObjectId, RmsEmpDate> rmsUserMap = mdList.stream().collect(Collectors.toMap(
                rms -> rms.getEmp().getUserId(), rms->rms
                , (oldValue, newValue) -> newValue));

        List<SysDef> pmsPrjEmpTitleDef = sysDefService.getSysDefsBySrc(PrjConstant.CMC_DEF_ID, SysDefTypeCodeName.PMS_PRJ_EMP_TITLE);
        Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap = pmsPrjEmpTitleDef.stream().collect(Collectors.toMap(SysDef::getId, Function.identity()));

        List<IDbCondition> conds = new ArrayList<>();
        List<String> employeeIds = new ArrayList<>();
        employeeIds.addAll(SysDefConstants.EMPLOYEE_TYPE_FORMAL);
        employeeIds.addAll(SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE);
        conds.add(new DC_E(DFN.sysUser_sbuId, prjInfo.getSbuId()));
        conds.add(new DC_I<>(DFN.sysUser_employeeType, employeeIds));
        conds.add(new DC_I<>(DFN.sysUser_id,empIds));
        conds.add(new DC_E(DFN.sysUser_isValid, true));
        List<TeSysUser> sysUsers = sysUserDao.findByConds(conds, null);

        List<AiBizTripApplyVo> tripApplyVoList = aiBizTripApplyFeignClient.queryAiBizTripApply(subPrjIds, start, end,pfmApply.getSite()).getData(true);
        Map<ObjectId, AiBizTripApplyVo> applyVoMap = tripApplyVoList.stream().collect(Collectors.toMap(AiBizTripApplyVo::getUserId, Function.identity()));

        List<TeSysCal> totalWork = sysCalDao.getWorkDays(start, end);
        for (TeSysUser sysUser : sysUsers) {
            String employeeType = sysUser.getEmployeeType();
            TePrjEmpPfm prjEmpPfm = new TePrjEmpPfm();
            prjEmpPfm.setIsValid(true);
            prjEmpPfm.setEmployeeType(employeeType);
            prjEmpPfm.setIsPfmEmp(SysDefConstants.EMPLOYEE_TYPE_FORMAL.contains(employeeType));

            prjEmpPfm.setPfmApplyId(empPfmApply.getId());
            prjEmpPfm.setPrj(prjDef.trans2IdNameCn());
            prjEmpPfm.setEmp(sysUser.trans2User());
            prjEmpPfm.setManager(sysUser.getManager());
            prjEmpPfm.setAddUser(loginUser.trans2User());
            prjEmpPfm.setAddTime(new Date());

            if (null != rmsUserMap.get(sysUser.getId())){
                RmsEmpDate rmsEmpDate = rmsUserMap.get(sysUser.getId());
                ObjectId roleId = rmsEmpDate.getRole().getCid();
                setTittle(roleId, pmsPrjEmpTitleDefMap, prjEmpPfm);
                prjEmpPfm.setGroup(rmsEmpDate.getPrjGroup().getName());
            }
            double mm = 0D;
            int md = 0;

            for (String month : ymMap.keySet()) {
                Integer workDays = ymMap.get(month);
                Map<ObjectId, List<RmsEmpDate>> listMap = rmsDateMap.get(month);
                if (listMap != null && null != listMap.get(sysUser.getId())){
                    List<RmsEmpDate> dateList = listMap.get(sysUser.getId());
                    mm += BigDecimalUtils.divideDouble((double) dateList.size(), workDays, 2);
                    md += dateList.size();
                }
            }
            prjEmpPfm.setMm(mm);
            prjEmpPfm.setMdStd(md);
            prjEmpPfm.setMdAdjust(0);
            prjEmpPfm.setMdSum(md);
            prjEmpPfm.setDept(sysUser.getOrgName());

            setScore(prjId, startYm, endYm, sysUser, prjEmpPfm);
            setTrip(sysUser, applyVoMap, prjEmpPfm,totalWork.size());

            //时长系数 =总人天 /项目总时长
            double mdRatio = BigDecimalUtils.divideDouble((double)prjEmpPfm.getMdSum(),totalWork.size(),2);
            prjEmpPfm.setMdRatio(mdRatio);
            prjEmpPfmDao.insert(prjEmpPfm);
        }

        mailSendTaskService.prjBonusStartMail(userApply.getId(),prjInfo.getPrjCode());
    }

    private void setScore(ObjectId prjId, String startYm, String endYm, TeSysUser sysUser, TePrjEmpPfm prjEmpPfm) {
        List<TePfmIndex> pfmIndexList = pfmIndexDao.queryPfmIndexList(startYm, endYm, prjId, PrjConstant.MSMT_SYS_TYPE_PRJ
                , PrjConstant.EVALUATE_TYPE_PFM_MSMT_INDEX, sysUser.getId());
        double workEffortScore = 0D;
        double workQualityScore = 0D;
        if (CollectionUtils.isNotEmpty(pfmIndexList)){
            List<String> workIds = new ArrayList<>();
            workIds.add(PrjConstant.DEV_WORKLOAD);
            workIds.add(PrjConstant.TEST_WORKLOAD);
            workIds.add(PrjConstant.BA_WORKLOAD);

            List<TePfmIndex> workPmfIndexList = pfmIndexList.stream().filter(pfm -> workIds
                    .contains(pfm.getIndex().getCodeName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(workPmfIndexList)){
                Double sum = workPmfIndexList.stream().filter(Objects::nonNull)
                        .filter(index-> null != index.getScore()
                                && index.getScore()>0).mapToDouble(TePfmIndex::getScore).sum();
                TeWorkScore workScore = new TeWorkScore();
                double score = BigDecimalUtils.divideDouble(sum, workPmfIndexList.size(), 2);
                workEffortScore = score;
                workScore.setScore(score);
                workScore.setScoreInitial(score);
                prjEmpPfm.setWorkEffortScore(workScore);
            }

            List<String> qualityIds = new ArrayList<>();
            qualityIds.add(PrjConstant.DEV_QUALITY);
            qualityIds.add(PrjConstant.TEST_QUALITY);
            qualityIds.add(PrjConstant.BA_QUALITY);

            List<TePfmIndex> qualityPfmIndexList = pfmIndexList.stream().filter(pfm -> qualityIds
                    .contains(pfm.getIndex().getCodeName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(qualityPfmIndexList)){
                Double sum = qualityPfmIndexList.stream().filter(Objects::nonNull)
                        .filter(index-> null != index.getScore()
                                && index.getScore()>0).mapToDouble(TePfmIndex::getScore).sum();
                double score = BigDecimalUtils.divideDouble(sum, qualityPfmIndexList.size(), 2);
                workQualityScore = score;
                TeWorkScore qualityScore = new TeWorkScore();
                qualityScore.setScore(score);
                qualityScore.setScoreInitial(score);
                prjEmpPfm.setWorkQualityScore(qualityScore);
            }
            //基础得分 =（工作量*0.5+工作质量*0.5）*0.8 + （积极性*0.5+沟通能力*0.2+吃苦耐劳*0.3）*0.2
            double workScore = (workEffortScore * 0.5 + workQualityScore * 0.5) * 0.8;
            double initiativeScore = prjEmpPfm.getInitiativeScore() == null ? 0 : prjEmpPfm.getInitiativeScore();
            double comSkillScore = prjEmpPfm.getComSkillScore() == null ? 0 : prjEmpPfm.getComSkillScore();
            double hardWorkScore = prjEmpPfm.getHardWorkScore() == null ? 0 :prjEmpPfm.getHardWorkScore();
            double otherScore = (initiativeScore * 0.5 + comSkillScore * 0.2 + hardWorkScore * 0.3) * 0.2;
            prjEmpPfm.setBaseScore(BigDecimalUtils.getDoubleHalfNum(workScore + otherScore,2));
        }
    }

    private void setTrip(TeSysUser sysUser, Map<ObjectId, AiBizTripApplyVo> applyVoMap, TePrjEmpPfm prjEmpPfm,int totalWorkDays) {
        AiBizTripApplyVo applyVo = applyVoMap.get(sysUser.getId());
        if (applyVo != null) {
            Integer tripTimes = applyVo.getTripTimes();
            prjEmpPfm.setTripTimes(tripTimes);
            List<TripDays> tripDays = applyVo.getTripDays();
            //int workdays = 0;
            int natureDays = 0;
            for (TripDays tripDay : tripDays) {
                Date startDate = tripDay.getStartDate();
                Date endDate = tripDay.getEndDate();
                //DMP_REQ_11708 项目绩效&激励，项目时长系数、差旅时长系数逻辑调整
                /*List<TeSysCal> wrok = sysCalDao.getWorkDays(DateUtil.formatDate2Str(startDate,DateUtil.DATE_FORMAT)
                        , DateUtil.formatDate2Str(endDate,DateUtil.DATE_FORMAT));
                workdays += wrok.size();*/


                List<TeSysCal> nature = sysCalDao.getNatureDays(DateUtil.formatDate2Str(startDate,DateUtil.DATE_FORMAT)
                        , DateUtil.formatDate2Str(endDate,DateUtil.DATE_FORMAT));

                natureDays += nature.size();

                prjEmpPfm.setTripDays(natureDays);
                //prjEmpPfm.setOnSiteDays(workdays);
            }
            if (tripTimes ==1 ){
                prjEmpPfm.setTripRatio(1D);
            } else {
                //根据“累计差旅天数/累计差旅次数”，若小于15天系数为0.7，大于等于15天小于25天，系数为0.9，大于等于25天，系数为1。不出差人员系数为1
                double tripRatio = (double) prjEmpPfm.getTripDays() / prjEmpPfm.getTripTimes();
                if(tripRatio < 15){
                    prjEmpPfm.setTripRatio(0.7);
                }else if(tripRatio >= 15 && tripRatio < 25){
                    prjEmpPfm.setTripRatio(0.9);
                }else{
                    prjEmpPfm.setTripRatio(1D);
                }
            }
        } else {
            prjEmpPfm.setTripRatio(1D);
            prjEmpPfm.setTripDays(null);
            prjEmpPfm.setTripTimes(null);
        }
    }

    @Override
    public List<PrjEmpPfmVo> queryPrjBonus(ObjectId prjId, ObjectId pfmApplyId, boolean all, TeSysUser loginUser
            , String emp, String dept, String title, String group,Boolean employeeType,Boolean isPfmEmp) {
        List<PrjEmpPfmVo> result = new ArrayList<>();
        TePrjInfo info = prjInfoService.getTePrjInfoById(prjId.toHexString());
        TePrjInfo2User pmUser = info.getPmUser();
        Sort sort = Sort.by(Sort.Direction.DESC, DbFieldName.prjEmpPfm_isPfmEmp.n(),DbFieldName.prjEmpPfm_group.n(), DbFieldName.prjEmpPfm_title.n());
        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, sort);
        if (StringUtil.isNotNull(emp)) {
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && null != empPfm.getEmp()
                    && (empPfm.getEmp().getUserName().contains(emp) || empPfm.getEmp().getLoginName().contains(emp))).collect(Collectors.toList());
        }
        if (Boolean.TRUE.equals(isPfmEmp)){
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && empPfm.getIsPfmEmp()).collect(Collectors.toList());
        } else if (Boolean.FALSE.equals(isPfmEmp)){
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && !empPfm.getIsPfmEmp()).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(dept)) {
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && empPfm.getDept().contains(dept)).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(title)) {
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && null != empPfm.getTitle()
                    && (empPfm.getTitle().getName().contains(title) || empPfm.getTitle().getCodeName().contains(title))).collect(Collectors.toList());
        }
        if (StringUtil.isNotNull(group)) {
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && empPfm.getGroup().contains(group)).collect(Collectors.toList());
        }
        if (Boolean.TRUE.equals(employeeType)){
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && SysDefConstants.EMPLOYEE_TYPE_FORMAL.contains(empPfm.getEmployeeType())).collect(Collectors.toList());
        } else if (Boolean.FALSE.equals(employeeType)){
            tePrjEmpPfms = tePrjEmpPfms.stream().filter(empPfm -> null != empPfm && SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(empPfm.getEmployeeType())).collect(Collectors.toList());
        }
        for (TePrjEmpPfm tePrjEmpPfm : tePrjEmpPfms) {
            PrjEmpPfmVo vo = new PrjEmpPfmVo();
            BeanUtils.copyProperties(tePrjEmpPfm, vo);
            if (SysDefConstants.EMPLOYEE_TYPE_FORMAL.contains(tePrjEmpPfm.getEmployeeType())){
                vo.setEmployeeType(SysDefConstants.EMPLOYEE_TYPE_FORMAL_NAME);
            } else if (SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(tePrjEmpPfm.getEmployeeType())){
                vo.setEmployeeType(SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE_NAME);
            } else {
                vo.setEmployeeType(SysDefConstants.EMPLOYEE_TYPE_FORMAL_NAME);
            }
            result.add(vo);
        }
        if (all ||(null != pmUser && pmUser.getUserId().equals(loginUser.getId()))) {
            return result;
        } else {
            //各打分人，仅能看到有权限打分的人员相关信息
            result = result.stream().filter(empPfm ->null != empPfm && null != empPfm.getCheckPsn() && empPfm.getCheckPsn().getUserId().equals(loginUser.getId())).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> exportPrjBonus(ObjectId prjId, ObjectId pfmApplyId, boolean all
            , TeSysUser loginUser, String emp, String dept, String title, String group,Boolean employeeType,Boolean isPfmEmp) {
        List<Map<String, Object>> result = new ArrayList<>();
        int no = 0;
        List<PrjEmpPfmVo> list = queryPrjBonus(prjId, pfmApplyId, all, loginUser, emp, dept, title, group,employeeType,isPfmEmp);
        for (PrjEmpPfmVo vo : list) {
            no++;
            Map<String, Object> map = BeanMapUtils.beanToMap(vo);
            map.put("no",no);
            map.put("isPfmEmp",vo.getIsPfmEmp()?"是":"否");
            map.put("emp",vo.getEmp().getUserName()+"/"+vo.getEmp().getLoginName());
            if (StringUtil.isNotNull(vo.getTitle())){
                map.put("title",vo.getTitle().getName());
            }
            if (StringUtil.isNotNull(vo.getCheckPsn())) {
                map.put("checkPsn",vo.getCheckPsn().getUserName()+"/"+vo.getCheckPsn().getLoginName());
            }
            if (StringUtil.isNotNull(vo.getWorkEffortScore())) {
                map.put("workEffortScore",vo.getWorkEffortScore().getScore());
            }
            if (StringUtil.isNotNull(vo.getWorkQualityScore())) {
                map.put("workQualityScore",vo.getWorkQualityScore().getScore());
            }
            if (StringUtil.isNotNull(vo.getBonusDue()) && StringUtil.isNotNull(vo.getBonusActual())) {
                double bonusActual = Double.parseDouble(vo.getBonusActual());
                double bonusDue = Double.parseDouble(vo.getBonusDue());
                double actualPlanDeviationRate = bonusActual - bonusDue;
                map.put("actualPlanDeviationRate", actualPlanDeviationRate);
                map.put("deviation", BigDecimalUtils.getDoubleHalfNum(actualPlanDeviationRate / bonusDue*100,2)+"%");
            }
            result.add(map);
        }
        return result;
    }

    @Override
    public List<PrjEmpPfmApplyVo> listPrjEmpPfmApplyByPrjId(ObjectId prjId) {
        List<PrjEmpPfmApplyVo> result = new ArrayList<>();
        List<TePrjEmpPfmApply> tePrjEmpPfmApplies = prjEmpPfmApplyDao.listPrjEmpPfmApplyByPrjId(prjId);
        for (TePrjEmpPfmApply tePrjEmpPfmApply : tePrjEmpPfmApplies) {
            PrjEmpPfmApplyVo vo = new PrjEmpPfmApplyVo();
            BeanUtils.copyProperties(tePrjEmpPfmApply, vo);
            result.add(vo);
        }
        return result;
    }

    @Override
    public void updatePrjBonus(TePrjEmpPfm empPfm) {
        ObjectId id = empPfm.getId();
        if (null == id) {
            throw BusinessException.initExc("id为空");
        }
        ObjectId prjId = empPfm.getPrj().getCid();
        TePrjInfo info = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (null == info) {
            throw BusinessException.initExc("项目不存在");
        }
        TePrjEmpPfmApply pfmApply = prjEmpPfmApplyDao.findById(empPfm.getPfmApplyId());

        String start = pfmApply.getStartYm()+"-01";
        String end = DateUtil.getMonthLastDay(DateUtil.parseDate(pfmApply.getEndYm(), DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_FORMAT);
        List<TeSysCal> totalWork = sysCalDao.getWorkDays(start, end);

        Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap = getTittleDefMap();
        TeIdNameCn title = empPfm.getTitle();
        if (null != title) {
            ObjectId cid = title.getCid();
            SysDef def = pmsPrjEmpTitleDefMap.get(cid);
            if (null == def) {
                throw BusinessException.initExc("岗位角色不存在");
            }
            empPfm.setTitleRatio(def.getValue());
            //仅技术总监、项目总监、项目经理有该系数
            //项目管理系数，增加角色：子项目经理、技术经理，即针对技术总监、项目总监、项目经理、子项目经理、技术经理有该系数
            if (cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_TECH_SM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PM)
                    || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PRJ_SM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_SUB_PM)
                    || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_TECH_MGT)) {
                ObjectId levelId = info.getLevel().getCid();
                if (levelId.equals(PrjConstant.PRJ_LEVEL_E)|| levelId.equals(PrjConstant.PRJ_LEVEL_F)) {
                    List<PrjPhaseAndResultReviewVo> prjPhaseAndResultReviewEF = prjReviewService.getPrjPhaseAndResultReviewEF(prjId, PrjConstant.RESULT_REVIEW_4EF_ID);
                    if (CollectionUtils.isNotEmpty(prjPhaseAndResultReviewEF)) {
                        Double smScore = prjPhaseAndResultReviewEF.get(0).getReviewPoint();
                        smScore = getPrjMgtRatio(smScore);
                        empPfm.setPrjMgtRatio(smScore);
                    }  else {
                        empPfm.setPrjMgtRatio(1D);
                    }
                } else {
                    TePrjReview review = prjReviewService.getPrjFinalReview(prjId);
                    if (null != review) {
                        Double pointSm = review.getOverallPointSm();
                        if (null != pointSm) {
                            pointSm = getPrjMgtRatio(pointSm);
                            empPfm.setPrjMgtRatio(pointSm);
                        } else {
                            empPfm.setPrjMgtRatio(1D);
                        }
                    } else {
                        empPfm.setPrjMgtRatio(1D);
                    }
                }
            }
            if (cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_EMP) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP)) {
                com.linkus.base.db.mongo.model.TeUser checkPsn = empPfm.getCheckPsn();
                String group = empPfm.getGroup();
                if (null != checkPsn && StringUtil.isNotNull(group)) {
                    List<TePrjEmpPfm> list = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, empPfm.getPfmApplyId(), null);
                    list.stream().filter(pfm -> pfm.getTitle() != null)
                            .filter(pfm->pfm.getTitle().getCid().equals(PrjConstant.PMS_PRJ_EMP_TITLE_EMP)
                            || pfm.getTitle().getCid().equals(PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP))
                            .filter(pfm -> pfm.getGroup() != null && group.equals(pfm.getGroup()))
                            .forEach(prjEmpPfm -> prjEmpPfm.setCheckPsn(checkPsn));

                    List<DbFieldName> updateFields = new ArrayList<>();
                    updateFields.add(DFN.prjEmpPfm_checkPsn);
                    prjEmpPfmDao.updateByFileds(list,updateFields,null);
                }
            }
        }
        Integer mdAdjust = empPfm.getMdAdjust();
        if (null != mdAdjust && 0 < mdAdjust) {
            empPfm.setMdSum(mdAdjust+empPfm.getMdStd());

            //时长系数 =总人天 /项目总时长
            double mdRatio = BigDecimalUtils.divideDouble((double)empPfm.getMdSum(),totalWork.size(),2);
            empPfm.setMdRatio(mdRatio);
        }
        String bonusActualStr = empPfm.getBonusActual();
        if (StringUtil.isNotNull(bonusActualStr)) {
            Double bonusActual = Double.parseDouble(bonusActualStr);
            String bonusDueStr = empPfm.getBonusDue();
            Double bonusDue = StringUtil.isNull(bonusDueStr) ? 0D : Double.parseDouble(bonusDueStr);
            Double bonusAdjust = bonusActual - bonusDue;
            empPfm.setBonusAdjust(bonusAdjust.toString());
            empPfm.setBonusDue(bonusDue.toString());
            empPfm.setBonusActual(bonusActual.toString());
        }
        if (StringUtil.isNotNull(empPfm.getBonusAdjustDesc())) {
            empPfm.setBonusAdjustDesc(empPfm.getBonusAdjustDesc());
        }
        if (StringUtil.isNotNull(empPfm.getScoreDesc())) {
            empPfm.setScoreDesc(empPfm.getScoreDesc());
        }
        prjEmpPfmDao.updateById(id, empPfm,true);


        List<TePrjEmpPfm> list = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, empPfm.getPfmApplyId(), null);
        setBonus(pfmApply,list);
    }

    @Override
    public void bacthUpdatePrjBonus(List<TePrjEmpPfm> empPfms) {
        TePrjEmpPfm pfm = empPfms.get(0);
        ObjectId id = pfm.getId();
        if (null == id) {
            throw BusinessException.initExc("id为空");
        }
        ObjectId prjId = pfm.getPrj().getCid();
        TePrjInfo info = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (null == info) {
            throw BusinessException.initExc("项目不存在");
        }
        TePrjEmpPfmApply pfmApply = prjEmpPfmApplyDao.findById(pfm.getPfmApplyId());

        String start = pfmApply.getStartYm()+"-01";
        String end = DateUtil.getMonthLastDay(DateUtil.parseDate(pfmApply.getEndYm(), DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_FORMAT);
        List<TeSysCal> totalWork = sysCalDao.getWorkDays(start, end);

        List<TePrjEmpPfm> list = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfm.getPfmApplyId(), null);

        Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap = getTittleDefMap();
        for (TePrjEmpPfm empPfm : empPfms) {
            TeIdNameCn title = empPfm.getTitle();
            if (null != title) {
                ObjectId cid = title.getCid();
                SysDef def = pmsPrjEmpTitleDefMap.get(cid);
                if (null == def) {
                    throw BusinessException.initExc("岗位角色不存在");
                }
                empPfm.setTitleRatio(def.getValue());
                //仅技术总监、项目总监、项目经理有该系数
                if (cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_TECH_SM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PRJ_SM)) {
                    ObjectId levelId = info.getLevel().getCid();
                    if (levelId.equals(PrjConstant.PRJ_LEVEL_E)|| levelId.equals(PrjConstant.PRJ_LEVEL_F)) {
                        List<PrjPhaseAndResultReviewVo> prjPhaseAndResultReviewEF = prjReviewService.getPrjPhaseAndResultReviewEF(prjId, PrjConstant.RESULT_REVIEW_4EF_ID);
                        if (CollectionUtils.isNotEmpty(prjPhaseAndResultReviewEF)) {
                            Double smScore = prjPhaseAndResultReviewEF.get(0).getReviewPoint();
                            smScore = getPrjMgtRatio(smScore);
                            empPfm.setPrjMgtRatio(smScore);
                        }  else {
                            empPfm.setPrjMgtRatio(1D);
                        }
                    } else {
                        TePrjReview review = prjReviewService.getPrjFinalReview(prjId);
                        if (null != review) {
                            Double pointSm = review.getOverallPointSm();
                            if (null != pointSm) {
                                pointSm = getPrjMgtRatio(pointSm);
                                empPfm.setPrjMgtRatio(pointSm);
                            } else {
                                empPfm.setPrjMgtRatio(1D);
                            }
                        } else {
                            empPfm.setPrjMgtRatio(1D);
                        }
                    }
                }
                if (cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_EMP) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP)) {
                    com.linkus.base.db.mongo.model.TeUser checkPsn = empPfm.getCheckPsn();
                    String group = empPfm.getGroup();
                    if (null != checkPsn && StringUtil.isNotNull(group)) {
                        empPfms.stream().filter(p -> p.getTitle() != null)
                                .filter(p->p.getTitle().getCid().equals(PrjConstant.PMS_PRJ_EMP_TITLE_EMP)
                                        || pfm.getTitle().getCid().equals(PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP))
                                .filter(p -> p.getGroup() != null && group.equals(pfm.getGroup()))
                                .forEach(prjEmpPfm -> prjEmpPfm.setCheckPsn(checkPsn));
                    }
                }
            }
            Integer mdAdjust = empPfm.getMdAdjust();
            if (null != mdAdjust && 0 < mdAdjust) {
                empPfm.setMdSum(mdAdjust+empPfm.getMdStd());

                //时长系数 =总人天 /项目总时长
                double mdRatio = BigDecimalUtils.divideDouble((double)empPfm.getMdSum(),totalWork.size(),2);
                empPfm.setMdRatio(mdRatio);
            }
            String bonusActualStr = empPfm.getBonusActual();
            if (StringUtil.isNotNull(bonusActualStr)) {
                Double bonusActual = Double.parseDouble(bonusActualStr);
                String bonusDueStr = empPfm.getBonusDue();
                Double bonusDue = StringUtil.isNull(bonusDueStr) ? 0D : Double.parseDouble(bonusDueStr);
                Double bonusAdjust = bonusActual - bonusDue;
                empPfm.setBonusAdjust(bonusAdjust.toString());
                empPfm.setBonusDue(bonusDue.toString());
                empPfm.setBonusActual(bonusActual.toString());
            }
            if (StringUtil.isNotNull(empPfm.getBonusAdjustDesc())) {
                empPfm.setBonusAdjustDesc(empPfm.getBonusAdjustDesc());
            }
            if (StringUtil.isNotNull(empPfm.getScoreDesc())) {
                empPfm.setScoreDesc(empPfm.getScoreDesc());
            }
        }
        List<DbFieldName> updateFields = new ArrayList<DbFieldName>();
        updateFields.add(DFN.prjEmpPfm_checkPsn);
        updateFields.add(DFN.prjEmpPfm_group);
        updateFields.add(DFN.prjEmpPfm_bonusDue);
        updateFields.add(DFN.prjEmpPfm_bonusAdjust);
        updateFields.add(DFN.prjEmpPfm_bonusActual);
        updateFields.add(DFN.prjEmpPfm_bonusAdjustDesc);
        updateFields.add(DFN.prjEmpPfm_prjMgtRatio);
        updateFields.add(DFN.prjEmpPfm_titleRatio);
        updateFields.add(DFN.prjEmpPfm_mdRatio);
        updateFields.add(DFN.prjEmpPfm_title);
        updateFields.add(DFN.prjEmpPfm_mdSum);
        updateFields.add(DFN.prjEmpPfm_workQualityScore);
        updateFields.add(DFN.prjEmpPfm_workEffortScore);
        updateFields.add(DFN.prjEmpPfm_workEffortScore);
        updateFields.add(DFN.prjEmpPfm_empPfmRatio);
        updateFields.add(DFN.prjEmpPfm_baseScore);
        updateFields.add(DFN.prjEmpPfm_comSkillScore);
        updateFields.add(DFN.prjEmpPfm_isPfmEmp);
        updateFields.add(DFN.prjEmpPfm_initiativeScore);
        updateFields.add(DFN.prjEmpPfm_hardWorkScore);

        prjEmpPfmDao.updateByFileds(empPfms,updateFields,null);
        setBonus(pfmApply,list);
    }

    @Override
    public TePrjEmpPfmApply updatePrjEmpPfmApply(ObjectId pfmApplyId,Double prePrjMm, Double bonusExtra) {
        if (null == prePrjMm && null == bonusExtra) {
            throw BusinessException.initExc("售前金额与特批额度不能为空");
        }
        TePrjEmpPfmApply pfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (pfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        if (null != prePrjMm) {
            pfmApply.setPrePrjMm(prePrjMm);
            //参与激励(人月)=资源实际-不参与激励分配工时
            Double notPfmMm = pfmApply.getNotPfmMm() == null ? 0D:pfmApply.getNotPfmMm();
            pfmApply.setPfmMm(BigDecimalUtils.getDoubleHalfNum(pfmApply.getMm()-notPfmMm+prePrjMm,1));
            //bonusDue = pfmMm * perMmRatio * prjLevelRatio * pmsEvalScore
            Double perMmRatio = pfmApply.getPerMmRatio();
            Double prjLevelRatio = pfmApply.getPrjLevelRatio();
            Double pmsEvalScore = pfmApply.getPmsEvalScore();
            Double bonusDue = pfmApply.getPfmMm() * perMmRatio * prjLevelRatio * pmsEvalScore/100;
            pfmApply.setBonusDue(BigDecimalUtils.getDoubleHalfNum(bonusDue,0, BigDecimal.ROUND_HALF_DOWN).toString());
        }
        if (null != bonusExtra) {
            pfmApply.setBonusExtra(bonusExtra.toString());
            String bonusDue = pfmApply.getBonusDue();
            bonusDue = StringUtil.isNull(bonusDue) ? "0" : bonusDue;
            pfmApply.setBonusActual(BigDecimalUtils.getDoubleHalfNum(Double.parseDouble(bonusDue)+bonusExtra,0, BigDecimal.ROUND_HALF_DOWN).toString());
            pfmApply.setBonusDue(bonusDue);
        }
        prjEmpPfmApplyDao.updateById(pfmApplyId, pfmApply);

        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(pfmApply.getPrj().getCid(), pfmApplyId, null);
        setBonus(pfmApply,tePrjEmpPfms);
        return pfmApply;
    }

    @Override
    public void setPrjEmpPfmApplyScoreAndTrip(ObjectId prjId,ObjectId pfmApplyId, ObjectId userId) {
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null) {
            throw BusinessException.initExc("项目不存在");
        }
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        List<TePrjEmpPfm> prjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, null);
        if (null != userId) {
            prjEmpPfms = prjEmpPfms.stream().filter(pfm -> pfm.getEmp().getUserId().equals(userId)).collect(Collectors.toList());
        }
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();

        ArrayList<ObjectId> subPrjIds = subPrjs.stream().map(TePrjInfoSubPrj::getCid).collect(Collectors.toCollection(ArrayList::new));

        String start = prjEmpPfmApply.getStartYm()+"-01";
        String end = DateUtil.getMonthLastDay(DateUtil.parseDate(prjEmpPfmApply.getEndYm(), DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_FORMAT);
        List<TeSysCal> totalWork = sysCalDao.getWorkDays(start, end);

        List<AiBizTripApplyVo> tripApplyVoList = aiBizTripApplyFeignClient.queryAiBizTripApply(subPrjIds, start, end,prjEmpPfmApply.getSite()).getData(true);
        Map<ObjectId, AiBizTripApplyVo> applyVoMap = tripApplyVoList.stream().collect(Collectors.toMap(AiBizTripApplyVo::getUserId, Function.identity()));

        List<ObjectId> userIds = prjEmpPfms.stream().map(TePrjEmpPfm::getEmp).map(com.linkus.base.db.mongo.model.TeUser::getUserId).collect(Collectors.toList());
        List<TeSysUser> sysUsers = sysUserService.getUsersByIds(userIds);
        Map<ObjectId, TeSysUser> userMap = sysUsers.stream().collect(Collectors.toMap(TeSysUser::getId, Function.identity()));
        for (TePrjEmpPfm prjEmpPfm : prjEmpPfms) {
            TeSysUser sysUser = userMap.get(prjEmpPfm.getEmp().getUserId());
            if (null == sysUser) {
                throw BusinessException.initExc("人员不存在");
            }
            setTrip(sysUser, applyVoMap, prjEmpPfm,totalWork.size());
            setScore(prjId, prjEmpPfmApply.getStartYm(), prjEmpPfmApply.getEndYm(), sysUser, prjEmpPfm);

            //时长系数 =总人天 /项目总时长
            double mdRatio = BigDecimalUtils.divideDouble((double)prjEmpPfm.getMdSum(),totalWork.size(),2);
            prjEmpPfm.setMdRatio(mdRatio);

            if (prjEmpPfm.getIsPfmEmp()){
                Double empPfmRatio = getEmpPfmRatio(prjEmpPfm);
                prjEmpPfm.setEmpPfmRatio(BigDecimalUtils.getDoubleHalfNum(empPfmRatio,2));
            }


            prjEmpPfmDao.updateById(prjEmpPfm.getId(), prjEmpPfm,true);
        }

        if (prjEmpPfms.size() > 1) {
            setBonus(prjEmpPfmApply,prjEmpPfms);
        }

    }

    private static Double getPrjMgtRatio(Double smScore) {
        if (null != smScore) {
            if (smScore >=95){
                smScore = 1D;
            } else if (smScore >=85){
                smScore = 0.8;
            } else if (smScore >=75){
                smScore = 0.6;
            } else {
                smScore = 0D;
            }
        }
        return smScore;
    }

    @Override
    public void deletePrjBonus(List<ObjectId> prjEmpPfmIds) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_I<>(DFN.common__id, prjEmpPfmIds));

        List<UpdataData> updataData = new ArrayList<>();
        updataData.add(new UpdataData(DFN.common_isValid,false));
        prjEmpPfmDao.updateByConds(conds,updataData);
    }

    private Map<ObjectId, SysDef> getTittleDefMap() {
        List<SysDef> pmsPrjEmpTitleDef = sysDefService.getSysDefsBySrc(PrjConstant.CMC_DEF_ID, SysDefTypeCodeName.PMS_PRJ_EMP_TITLE);
        Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap = pmsPrjEmpTitleDef.stream().collect(Collectors.toMap(SysDef::getId, Function.identity()));
        return pmsPrjEmpTitleDefMap;
    }

    @Override
    public List<TePrjEmpPfm> insertPrjBonus(List<TePrjEmpPfm>prjEmpPfms,TeSysUser loginUser) {
        TePrjEmpPfm empPfm = prjEmpPfms.get(0);
        ObjectId prjId = empPfm.getPrj().getCid();
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null) {
            throw BusinessException.initExc("项目不存在");
        }
        ObjectId pfmApplyId = empPfm.getPfmApplyId();
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        SysDef prjDef = sysDefService.getSysDefById(prjId);
        List<TePrjEmpPfm> result = new ArrayList<>();
        String start = prjEmpPfmApply.getStartYm()+"-01";
        String end = DateUtil.getMonthLastDay(DateUtil.parseDate(prjEmpPfmApply.getEndYm(), DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_FORMAT);
        List<String> ymBetween = DateUtil.getYmBetween(prjEmpPfmApply.getStartYm(),prjEmpPfmApply.getEndYm());
        Map<String,Integer> ymMap = new HashMap<>();
        for (String ym : ymBetween) {
            List<TeSysCal> sysCals = sysCalDao.getWorkDays(ym);
            ymMap.put(ym, sysCals.size());
        }

        ArrayList<String> subPrjCodes = prjInfo.getSubPrjs().stream().map(TePrjInfoSubPrj::getCode).collect(Collectors.toCollection(ArrayList::new));
        ArrayList<ObjectId> subPrjIds = prjInfo.getSubPrjs().stream().map(TePrjInfoSubPrj::getCid).collect(Collectors.toCollection(ArrayList::new));
        List<ObjectId> userIds = prjEmpPfms.stream().map(TePrjEmpPfm::getEmp).map(com.linkus.base.db.mongo.model.TeUser::getUserId).collect(Collectors.toList());
        List<RmsEmpDate> mdList = rmsEmpDateFeignClient.listPfmDataByDate(prjInfo.getBgId(), start
                ,end, userIds, subPrjCodes).getData(true);

        Map<ObjectId, RmsEmpDate> rmsUserMap = mdList.stream().collect(Collectors.toMap(
                rms -> rms.getEmp().getUserId(), rms->rms
                , (oldValue, newValue) -> newValue));
        Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap = getTittleDefMap();

        // 按月份分组
        Map<String, Map<ObjectId, List<RmsEmpDate>>> rmsDateMap = mdList.stream()
                .collect(Collectors.groupingBy(project -> {
                            LocalDate date = LocalDate.parse(project.getDate());
                            return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                        },
                        Collectors.groupingBy(project -> project.getEmp().getUserId())
                ));

        List<AiBizTripApplyVo> tripApplyVoList = aiBizTripApplyFeignClient.queryAiBizTripApply(subPrjIds, start, end,prjEmpPfmApply.getSite()).getData(true);
        Map<ObjectId, AiBizTripApplyVo> applyVoMap = tripApplyVoList.stream().collect(Collectors.toMap(AiBizTripApplyVo::getUserId, Function.identity()));

        List<TeSysCal> totalWork = sysCalDao.getWorkDays(start, end);

        List<TeSysUser> sysUsers = sysUserService.getUsersByIds(userIds);
        Map<ObjectId, TeSysUser> userMap = sysUsers.stream().collect(Collectors.toMap(TeSysUser::getId, Function.identity()));

        for (TePrjEmpPfm prjEmpPfm : prjEmpPfms) {
            ObjectId userId = prjEmpPfm.getEmp().getUserId();
            TeSysUser sysUser = userMap.get(userId);
            if (null == sysUser) {
                throw BusinessException.initExc("人员不存在");
            }
            prjEmpPfm.setEmp(sysUser.trans2User());
            prjEmpPfm.setPrj(prjDef.trans2IdNameCn());

            String employeeType = sysUser.getEmployeeType();
            prjEmpPfm.setEmployeeType(employeeType);
            prjEmpPfm.setIsPfmEmp(SysDefConstants.EMPLOYEE_TYPE_FORMAL.contains(employeeType));
            prjEmpPfm.setManager(sysUser.getManager());
            prjEmpPfm.setAddUser(loginUser.trans2User());
            prjEmpPfm.setAddTime(new Date());

            TeIdNameCn title = prjEmpPfm.getTitle();
            if (null != title) {
                ObjectId cid = title.getCid();
                SysDef def = pmsPrjEmpTitleDefMap.get(cid);
                if (null == def) {
                    throw BusinessException.initExc("岗位角色不存在");
                }
                prjEmpPfm.setTitleRatio(def.getValue());
                //仅技术总监、项目总监、项目经理有该系数
                if (cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_TECH_SM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PM) || cid.equals(PrjConstant.PMS_PRJ_EMP_TITLE_PRJ_SM)) {
                    ObjectId levelId = prjInfo.getLevel().getCid();
                    if (levelId.equals(PrjConstant.PRJ_LEVEL_E)|| levelId.equals(PrjConstant.PRJ_LEVEL_F)) {
                        List<PrjPhaseAndResultReviewVo> prjPhaseAndResultReviewEF = prjReviewService.getPrjPhaseAndResultReviewEF(prjId, PrjConstant.RESULT_REVIEW_4EF_ID);
                        if (CollectionUtils.isNotEmpty(prjPhaseAndResultReviewEF)) {
                            Double smScore = prjPhaseAndResultReviewEF.get(0).getSmScore();
                            if (null != smScore) {
                                smScore = getPrjMgtRatio(smScore);
                                empPfm.setPrjMgtRatio(smScore);
                            } else {
                                empPfm.setPrjMgtRatio(1D);
                            }
                        } else {
                            empPfm.setPrjMgtRatio(1D);
                        }
                    } else {
                        TePrjReview review = prjReviewService.getPrjFinalReview(prjId);
                        if (null != review) {
                            Double pointSm = review.getOverallPointSm();
                            if (null != pointSm) {
                                pointSm = getPrjMgtRatio(pointSm);
                                empPfm.setPrjMgtRatio(pointSm);
                            } else {
                                empPfm.setPrjMgtRatio(1D);
                            }
                        } else {
                            empPfm.setPrjMgtRatio(1D);
                        }
                    }
                }
            }
            if (null != rmsUserMap.get(sysUser.getId())){
                RmsEmpDate rmsEmpDate = rmsUserMap.get(sysUser.getId());
                prjEmpPfm.setGroup(rmsEmpDate.getPrjGroup().getName());
            }
            double mm = 0D;
            int md = 0;
            for (String month : ymBetween) {
                Integer workDays = ymMap.get(month);
                Map<ObjectId, List<RmsEmpDate>> listMap = rmsDateMap.get(month);
                if (listMap != null && null != listMap.get(sysUser.getId())) {
                    List<RmsEmpDate> dateList = listMap.get(sysUser.getId());
                    mm += BigDecimalUtils.divideDouble((double) dateList.size(), workDays, 2);
                    md += dateList.size();
                }
            }
            prjEmpPfm.setMm(mm);
            prjEmpPfm.setMdStd(md);
            Integer mdAdjust = prjEmpPfm.getMdAdjust() == null ? 0 : prjEmpPfm.getMdAdjust();
            prjEmpPfm.setMdSum(md+ mdAdjust);
            prjEmpPfm.setDept(sysUser.getOrgName());

            setTrip(sysUser, applyVoMap, prjEmpPfm,totalWork.size());
            setScore(prjId, prjEmpPfmApply.getStartYm(), prjEmpPfmApply.getEndYm(), sysUser, prjEmpPfm);

            //时长系数 =总人天 /项目总时长
            double mdRatio = BigDecimalUtils.divideDouble((double)prjEmpPfm.getMdSum(),totalWork.size(),2);
            prjEmpPfm.setMdRatio(mdRatio);
            prjEmpPfm.setIsValid(true);
            TePrjEmpPfm insert = prjEmpPfmDao.insert(prjEmpPfm);
            result.add(insert);
        }

        return result;
    }

    @Override
    public void submitPrjBonus(ObjectId prjId, ObjectId pfmApplyId,String desc,int passBranch
            ,TeSysUser loginUser) {
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null) {
            throw BusinessException.initExc("项目不存在");
        }
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, Sort.by(Sort.Direction.ASC, DbFieldName.common_addTime.n()));
        if (CollectionUtils.isEmpty(tePrjEmpPfms)) {
            throw BusinessException.initExc("项目人员绩效数据为空");
        }
        TeIdNameCn status = prjEmpPfmApply.getStatus();

        TeSysUserApply apply = sysUserApplyDao.findByRcdIdAndRcdStatus(pfmApplyId, status.getCid());
        TeSysApprStatusCnfg statusCnfg = sysApprStatusCnfgDao.queryApprStatusCnfg(apply.getType().getCid(), apply.getRcdStatus().getCid());

        List<TePassStatus> passStatusList = statusCnfg.getPassStatus();
        TePassStatus passStatus = passStatusList.get(passBranch);
        statusCnfg = sysApprStatusCnfgDao.queryApprStatusCnfg(apply.getType().getCid(), passStatus.getStatus().getCid());

        Map<String, Object> keyWordHolder = new HashMap<>();
        keyWordHolder.put("SYS_APPR_APPLY_SRCDEF_CID", prjId);
        List<ObjectId> toUserIds = new ArrayList<>();
        if (status.getCid().equals(PrjConstant.DEF_ID_AMS_APPR_STATUS_START)) {
            validStart(prjId, tePrjEmpPfms, prjInfo,false);
            toUserIds = tePrjEmpPfms.stream().map(TePrjEmpPfm::getCheckPsn)
                    .map(com.linkus.base.db.mongo.model.TeUser::getUserId).collect(Collectors.toList());



        } else if (status.getCid().equals(PrjConstant.DEF_ID_AMS_APPR_STATUS_SUBMIT)) {
            validStart(prjId, tePrjEmpPfms, prjInfo,true);
            Double mm = 0D;
            Double prjMd = 0D;
            Double notPfmMm = 0D;
            for (TePrjEmpPfm pfm : tePrjEmpPfms) {
                Double baseScore = pfm.getBaseScore();
                if (baseScore == null) {
                    throw BusinessException.initExc(pfm.getEmp().getUserName()+"基础考评分为空");
                }
                Boolean isPfmEmp = pfm.getIsPfmEmp();
                if (!isPfmEmp) {
                    if (!SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(pfm.getEmployeeType())) {
                        notPfmMm+=pfm.getMm();
                    }
                }
                if (!SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(pfm.getEmployeeType())) {
                    mm+=pfm.getMm();
                    prjMd+=pfm.getMdSum();
                }

            }
            prjEmpPfmApply.setPrjMd(BigDecimalUtils.getDoubleHalfNum(prjMd,1));
            prjEmpPfmApply.setMm(BigDecimalUtils.getDoubleHalfNum(mm,1));
            prjEmpPfmApply.setNotPfmMm(BigDecimalUtils.getDoubleHalfNum(notPfmMm,1));
            ObjectId levelId = prjInfo.getLevel().getCid();
            if (levelId.equals(PrjConstant.PRJ_LEVEL_E)|| levelId.equals(PrjConstant.PRJ_LEVEL_F)) {
                List<PrjPhaseAndResultReviewVo> prjPhaseAndResultReviewEF = prjReviewService.getPrjPhaseAndResultReviewEF(prjId, PrjConstant.RESULT_REVIEW_4EF_ID);
                if (CollectionUtils.isNotEmpty(prjPhaseAndResultReviewEF)) {
                    Double reviewPoint = prjPhaseAndResultReviewEF.get(0).getReviewPoint();
                    prjEmpPfmApply.setPmsEvalScore(reviewPoint);
                } else {
                    prjEmpPfmApply.setPmsEvalScore(100D);
                }
            } else {
                TePrjReview review = prjReviewService.getPrjFinalReview(prjId);
                if (null != review) {
                    Double point = review.getOverallPointBase();
                    prjEmpPfmApply.setPmsEvalScore(point);
                } else {
                    prjEmpPfmApply.setPmsEvalScore(100D);
                }
            }
            prjEmpPfmApplyDao.updateById(pfmApplyId, prjEmpPfmApply);
            prjEmpPfmApply = updatePrjEmpPfmApply(pfmApplyId, 0D, 0D);
        } else if (status.getCid().equals(PrjConstant.DEF_ID_AMS_APPR_STATUS_QUOTA)) {
            for (TePrjEmpPfm tePrjEmpPfm : tePrjEmpPfms) {
                String bonusDue = tePrjEmpPfm.getBonusDue();
                tePrjEmpPfm.setBonusActual(bonusDue);
            }
            List<DbFieldName> updateFields = new ArrayList<>();
            updateFields.add(DFN.prjEmpPfm_bonusActual);
            prjEmpPfmDao.updateByFileds(tePrjEmpPfms,updateFields,null);
        } else if (status.getCid().equals(PrjConstant.DEF_ID_AMS_APPR_STATUS_PM)){
            validStart(prjId, tePrjEmpPfms, prjInfo,true);
        }

        if (!status.getCid().equals(PrjConstant.DEF_ID_AMS_APPR_STATUS_SAVE)) {
            List<TeSysUser> toCheckUsers = sysUserService.getUsersBySysApprStatusCnfg(statusCnfg, keyWordHolder);
            if (CollectionUtils.isEmpty(toCheckUsers)) {
                throw BusinessException.initExc("下个节点审批人查询为空");
            }
            TeIdNameCn pass = sysUserApplyService.pass(apply.getId(), desc, passBranch, toCheckUsers, loginUser);
            prjEmpPfmApply.setStatus(pass);
            prjEmpPfmApplyDao.updateById(pfmApplyId, prjEmpPfmApply);
            mailSendTaskService.prjBonusApprovalNotifyMail(apply.getId(),prjInfo.getPrjCode(),pass.getCid(),true,desc,loginUser,toUserIds);
        } else {
            TeIdNameCn pass = sysUserApplyService.pass(apply.getId(), desc, passBranch, null, loginUser);
            prjEmpPfmApply.setStatus(pass);
            prjEmpPfmApplyDao.updateById(pfmApplyId, prjEmpPfmApply);
            mailSendTaskService.prjBonusEndMail(prjInfo.getPrjCode());
        }
    }

    private void setBonus(TePrjEmpPfmApply prjEmpPfmApply, List<TePrjEmpPfm> tePrjEmpPfms) {
        String bonusActualStr = prjEmpPfmApply.getBonusActual();
        if (StringUtil.isNotNull(bonusActualStr)) {
            double bonusActual = Double.parseDouble(bonusActualStr);
            double remainingBonus = bonusActual;
            double sum = 0D;
            for (TePrjEmpPfm empPfm : tePrjEmpPfms) {
                if (empPfm.getIsPfmEmp()){
                    Double empPfmRatio = getEmpPfmRatio(empPfm);
                    empPfm.setEmpPfmRatio(BigDecimalUtils.getDoubleHalfNum(empPfmRatio,2));
                    sum+=empPfm.getEmpOverallRatio();
                } else {
                    empPfm.setBonusDue(null);
                    empPfm.setBonusActual(null);
                    prjEmpPfmDao.updateById(empPfm.getId(), empPfm,true);
                }
            }
            for (TePrjEmpPfm empPfm : tePrjEmpPfms) {
                if (empPfm.getIsPfmEmp()){
                    Double empOverallRatio = empPfm.getEmpOverallRatio();
                    Double bonusDue = BigDecimalUtils.getDoubleHalfNum(empOverallRatio / sum * bonusActual, 0,BigDecimal.ROUND_HALF_DOWN);
                    remainingBonus -= bonusDue;
                    empPfm.setBonusDue(bonusDue.toString());
                }
            }
            if (remainingBonus > 0){
                TePrjEmpPfm prjEmpPfm = tePrjEmpPfms.stream().filter(TePrjEmpPfm::getIsPfmEmp).findFirst().orElse(null);
                if (null != prjEmpPfm) {
                    String bonusDue = prjEmpPfm.getBonusDue();
                    double bonus = Double.parseDouble(bonusDue) + remainingBonus;
                    prjEmpPfm.setBonusDue(String.valueOf(bonus));
                }
            }
            List<DbFieldName> updateFields = new ArrayList<>();
            updateFields.add(DFN.prjEmpPfm_bonusDue);
            updateFields.add(DFN.prjEmpPfm_empPfmRatio);
            updateFields.add(DFN.prjEmpPfm_empOverallRatio);
            prjEmpPfmDao.updateByFileds(tePrjEmpPfms,updateFields,null);
        }

        prjEmpPfmApply.setBonusActual(bonusActualStr);
    }

    private Double getEmpPfmRatio(TePrjEmpPfm empPfm) {
        double mdRatio = empPfm.getMdRatio() == null ? 0D : empPfm.getMdRatio();
        double tripRatio = empPfm.getTripRatio() == null ? 1D : empPfm.getTripRatio();
        String titleRatio = StringUtil.isNull(empPfm.getTitleRatio()) ? "0" : empPfm.getTitleRatio();
        double baseScore = empPfm.getBaseScore() == null ? 0D : empPfm.getBaseScore();
        Double empPfmRatio = baseScore * Double.parseDouble(titleRatio) * mdRatio * tripRatio;
        Double prjMgtRatio = empPfm.getPrjMgtRatio();
        if (null != prjMgtRatio) {
            Double empOverallRatio = prjMgtRatio * empPfmRatio;
            if (SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(empPfm.getEmployeeType())) {
                empPfm.setEmpOverallRatio(BigDecimalUtils.getDoubleHalfNum(empOverallRatio*0.5,2));
            } else {
                empPfm.setEmpOverallRatio(BigDecimalUtils.getDoubleHalfNum(empOverallRatio,2));
            }
            return empOverallRatio;
        } else {
            if (SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(empPfm.getEmployeeType())) {
                empPfm.setEmpOverallRatio(BigDecimalUtils.getDoubleHalfNum(empPfmRatio*0.5,2));
            } else {
                empPfm.setEmpOverallRatio(BigDecimalUtils.getDoubleHalfNum(empPfmRatio,2));
            }
        }
        return empPfmRatio;
    }

    @Override
    public void rejectPrjBonus(ObjectId prjId, ObjectId pfmApplyId, String desc, int passBranch, TeSysUser loginUser) {
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null) {
            throw BusinessException.initExc("项目不存在");
        }
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        TeIdNameCn status = prjEmpPfmApply.getStatus();
        TeSysUserApply apply = sysUserApplyDao.findByRcdIdAndRcdStatus(pfmApplyId, status.getCid());

        TeSysApprStatusCnfg statusCnfg = sysApprStatusCnfgDao.queryApprStatusCnfg(apply.getType().getCid(), apply.getRcdStatus().getCid());
        List<TePassStatus> noPassStatusList = statusCnfg.getNoPassStatus();
        TePassStatus noPassStatus = noPassStatusList.get(passBranch);
        statusCnfg = sysApprStatusCnfgDao.queryApprStatusCnfg(apply.getType().getCid(), noPassStatus.getStatus().getCid());

        Map<String, Object> keyWordHolder = new HashMap<>();
        keyWordHolder.put("SYS_APPR_APPLY_SRCDEF_CID", prjId);

        List<TeSysUser> toCheckUsers = sysUserService.getUsersBySysApprStatusCnfg(statusCnfg, keyWordHolder);
        if (CollectionUtils.isEmpty(toCheckUsers)) {
            throw BusinessException.initExc("下个节点审批人查询为空");
        }

        TeIdNameCn noPass = sysUserApplyService.reject(apply.getId(), desc, passBranch, toCheckUsers, loginUser);
        prjEmpPfmApply.setStatus(noPass);
        prjEmpPfmApplyDao.updateById(pfmApplyId, prjEmpPfmApply);

        mailSendTaskService.prjBonusApprovalNotifyMail(apply.getId(),prjInfo.getPrjCode(),noPass.getCid()
                ,false,desc,loginUser,null);
    }

    @Override
    public List<PrjBonusDurationVo> prjBonusDuration(ObjectId prjId, ObjectId pfmApplyId,boolean actual) {
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, null);
        double bonus;
        if (actual) {
            bonus = tePrjEmpPfms.stream().filter(pfm-> pfm.getIsPfmEmp() &&
                    StringUtil.isNotNull(pfm.getBonusActual()))
                    .mapToDouble(pfm -> Double.parseDouble(pfm.getBonusActual())).max().getAsDouble();
        } else {
            bonus = tePrjEmpPfms.stream().filter(pfm-> pfm.getIsPfmEmp() &&
                            StringUtil.isNotNull(pfm.getBonusDue()))
                    .mapToDouble(pfm -> Double.parseDouble(pfm.getBonusDue())).max().getAsDouble();
        }
        String startYm = prjEmpPfmApply.getStartYm();
        String endYm = prjEmpPfmApply.getEndYm();
        List<TeSysCal> cals = sysCalDao.getWorkDays(startYm + "-01" , endYm + "-31");
        Integer md = cals.size();
        // mm除以5尾数为0取整，分5个档
        List<PrjBonusDurationVo> result = new ArrayList<>();
        int groupCount = 5;
        // 1. 先算出每档的最小宽度
        double per = Math.ceil((double) md / groupCount);
        // 2. 向上取整到10的倍数
        int step = (int) (Math.ceil(per / 10) * 10);

        List<String> tittle = new ArrayList<>();
        for (int j = 1; j <= groupCount; j++) {
            // 1. 先算出每档的最小宽度
            double bonusPer = Math.ceil(bonus / groupCount);
            // 2. 向上取整到1000的倍数
            int bonusStep = (int) (Math.ceil(bonusPer / 1000) * 1000);
            tittle.add(String.valueOf(bonusStep*j));
        }

        for (int i = 0; i <= 5; i++) {
            PrjBonusDurationVo vo = new PrjBonusDurationVo();
            vo.setIndex(i);
            result.add(vo);
            if (i == 0){
                vo.setPrjDuration("时长/激励");
                vo.setBonus(tittle);
                continue;
            }
            int prjDuration = Math.max((int) (Math.ceil((double) md / 10) * 10) - step * (i-1), 0);
            vo.setPrjDuration(String.valueOf(prjDuration));
            String bonusTitle1 = tittle.get(0);
            String bonusTitle2 = tittle.get(1);
            String bonusTitle3 = tittle.get(2);
            String bonusTitle4 = tittle.get(3);
            String bonusTitle5 = tittle.get(4);

            int bonusValue1 = 0;
            int bonusValue2 = 0;
            int bonusValue3 = 0;
            int bonusValue4 = 0;
            int bonusValue5 = 0;
            int index = i;
            List<TePrjEmpPfm> empPfms = tePrjEmpPfms.stream().filter(pfm -> pfm.getIsPfmEmp()
                    && pfm.getMdSum() < prjDuration && pfm.getMdSum() >= (prjDuration-step)).collect(Collectors.toList());
            for (TePrjEmpPfm empPfm : empPfms) {
                double pfmBonus;
                if (actual) {
                    String bonusActual = empPfm.getBonusActual();
                    if (StringUtil.isNotNull(bonusActual)) {
                        pfmBonus = Double.parseDouble(bonusActual);
                    } else {
                        pfmBonus = 0D;
                    }

                } else {
                    if (StringUtil.isNotNull(empPfm.getBonusDue())) {
                        pfmBonus = Double.parseDouble(empPfm.getBonusDue());
                    } else {
                        pfmBonus = 0D;
                    }
                }
                if (pfmBonus<Integer.parseInt(bonusTitle5) && pfmBonus>=Integer.parseInt(bonusTitle4)){
                    bonusValue5++;
                } else if (pfmBonus<Integer.parseInt(bonusTitle4) && pfmBonus>=Integer.parseInt(bonusTitle3)){
                    bonusValue4++;
                } else if (pfmBonus<Integer.parseInt(bonusTitle3) && pfmBonus>=Integer.parseInt(bonusTitle2)){
                    bonusValue3++;
                } else if (pfmBonus<Integer.parseInt(bonusTitle2) && pfmBonus>=Integer.parseInt(bonusTitle1)){
                    bonusValue2++;
                } else {
                    bonusValue1++;
                }
            }
            tePrjEmpPfms.removeAll(empPfms);
            List<String> values = new ArrayList<>();
            values.add(Integer.toString(bonusValue1));
            values.add(Integer.toString(bonusValue2));
            values.add(Integer.toString(bonusValue3));
            values.add(Integer.toString(bonusValue4));
            values.add(Integer.toString(bonusValue5));
            vo.setBonus(values);
            if (prjDuration == 0){
                break;
            }
        }
        return result;
    }

    @Override
    public List<PrjBonusAnalyseVo> prjBonusAnalyse(ObjectId prjId, ObjectId pfmApplyId) {
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, null);
        tePrjEmpPfms = tePrjEmpPfms.stream().filter(pfm -> pfm.getIsPfmEmp() && StringUtil.isNotNull(pfm.getBonusActual())).collect(Collectors.toList());
        double bonusActual = tePrjEmpPfms.stream().mapToDouble(pfm -> Double.parseDouble(pfm.getBonusActual())).max().getAsDouble();;

        int step = (int) (Math.ceil((bonusActual / 5.0) / 1000) * 1000); // 6000
        List<PrjBonusAnalyseVo> result = new ArrayList<>();
        for (int i = 5; i > 0; i--) {
            PrjBonusAnalyseVo vo = new PrjBonusAnalyseVo();
            int upper = i*step;
            int lower = upper - step;
            if (lower < 0) lower = 0;

            vo.setInterval(String.valueOf(upper));
            int size = tePrjEmpPfms.size();

            int bonusDueCount = 0;
            int bonusActualCount = 0;
            for (TePrjEmpPfm tePrjEmpPfm : tePrjEmpPfms) {
                if (tePrjEmpPfm.getIsPfmEmp()){
                    double bonusDue = Double.parseDouble(tePrjEmpPfm.getBonusDue());
                    double actual = Double.parseDouble(tePrjEmpPfm.getBonusActual());
                    if (bonusDue < upper && bonusDue >= lower) {
                        bonusDueCount++;
                    }
                    if (actual < upper && actual >= lower) {
                        bonusActualCount++;
                    }
                }
            }
            vo.setDueNum(bonusDueCount);
            vo.setDueRatio(BigDecimalUtils.divideDouble((double) bonusDueCount,size,4));
            vo.setActualNum(bonusActualCount);
            vo.setActualRatio(BigDecimalUtils.divideDouble((double) bonusActualCount,size,4));
            result.add(vo);
        }

        return result;
    }

    @Override
    public List<PrjBonusRoleReportVo> prjBonusRoleReport(ObjectId prjId, ObjectId pfmApplyId, boolean isGroup) {
        List<PrjBonusRoleReportVo> result = new ArrayList<>();
        TePrjEmpPfmApply prjEmpPfmApply = prjEmpPfmApplyDao.findById(pfmApplyId);
        if (prjEmpPfmApply == null) {
            throw BusinessException.initExc("项目考评不存在");
        }
        List<TePrjEmpPfm> tePrjEmpPfms = prjEmpPfmDao.listPrjEmpPfmByPrjIdAndPfmApplyId(prjId, pfmApplyId, null);
        Map<String, Map<ObjectId, List<TePrjEmpPfm>>> groupMap = new HashMap<>();
        if (isGroup){
            //先按group分组，再按title.cid分组
            for (TePrjEmpPfm pfm : tePrjEmpPfms) {
                String group = pfm.getGroup();
                ObjectId titleCid = pfm.getTitle().getCid();
                groupMap.computeIfAbsent(group, k -> new HashMap<>()).computeIfAbsent(titleCid, k -> new ArrayList<>()).add(pfm);
            }
        } else {
            for (TePrjEmpPfm pfm : tePrjEmpPfms) {
                String dept = pfm.getDept();
                ObjectId titleCid = pfm.getTitle().getCid();
                groupMap.computeIfAbsent(dept, k -> new HashMap<>()).computeIfAbsent(titleCid, k -> new ArrayList<>()).add(pfm);
            }
        }
        for (String tag : groupMap.keySet()) {
            PrjBonusRoleReportVo vo = new PrjBonusRoleReportVo();
            result.add(vo);
            vo.setTag(tag);
            double total = 0d;
            Map<ObjectId, List<TePrjEmpPfm>> listMap = groupMap.get(tag);
            for (ObjectId id : listMap.keySet()) {
                List<TePrjEmpPfm> list = listMap.get(id);
                if (PrjConstant.PMS_PRJ_EMP_TITLE_EMP.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setRegularEmp(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setKeyEmp(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_TECH_MGT.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setTechMgt(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_SUB_PM.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setSubPm(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_BIG_GROUP_RESP.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setBigGroupResp(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_MODULE_RESP.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setModuleResp(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_TECH_EXPERT.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setTechExpert(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_PM.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setPm(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_PRJ_SM.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setPrjSm(String.valueOf(sum));
                } else if (PrjConstant.PMS_PRJ_EMP_TITLE_TECH_SM.equals(id)){
                    double sum = list.stream().filter(pfm->StringUtil.isNotNull(pfm.getBonusActual()))
                            .mapToDouble(pfm->Double.parseDouble(pfm.getBonusActual())).sum();
                    total += sum;
                    vo.setTechSm(String.valueOf(sum));
                }
                vo.setTotal(String.valueOf(total));
            }
        }
        return result;
    }

    private void validStart(ObjectId prjId, List<TePrjEmpPfm> tePrjEmpPfms, TePrjInfo prjInfo,boolean flag) {
        int techMgt = 0;
        int subPm = 0;
        int keyEmp = 0;
        int regularEmp = 0;
        double mdOut = 0;
        double pfmOut = 0;
        for (TePrjEmpPfm pfm : tePrjEmpPfms) {
            Boolean isPfmEmp = pfm.getIsPfmEmp();
            if (isPfmEmp != null && isPfmEmp && SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(pfm.getEmployeeType())) {
                pfmOut++;
            }
            if (SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE.contains(pfm.getEmployeeType()) && pfm.getMdStd() != null && pfm.getMdStd() >0){
                mdOut++;
            }

            com.linkus.base.db.mongo.model.TeUser emp = pfm.getEmp();

            TeWorkScore workEffortScore = pfm.getWorkEffortScore();
            TeWorkScore workQualityScore = pfm.getWorkQualityScore();
            if (flag){
                if (isPfmEmp != null && isPfmEmp && null != workEffortScore && null != workEffortScore.getScore() && workEffortScore.getScore() < 60){
                    throw BusinessException.initExc(emp.getUserName()+"工作量低于60分，不符合发放条件，请改为不参与激励再次提交！");
                }
                if (isPfmEmp != null && isPfmEmp && null != workQualityScore && null != workQualityScore.getScore() && workQualityScore.getScore() < 60){
                    throw BusinessException.initExc(emp.getUserName()+"工作质量低于60分，不符合发放条件，请改为不参与激励再次提交！");
                }
            }

            TeIdNameCn title = pfm.getTitle();
            if (title == null) {
                throw BusinessException.initExc(emp.getUserName()+"岗位为空，请修改后再次提交");
            }
            String group = pfm.getGroup();
            if (StringUtil.isNull(group)) {
                throw BusinessException.initExc(emp.getUserName()+"归属组为空，请修改后再次提交");
            }
            com.linkus.base.db.mongo.model.TeUser checkPsn = pfm.getCheckPsn();
            if (checkPsn == null) {
                throw BusinessException.initExc(emp.getUserName()+"打分人为空，请修改后再次提交");
            }
            Integer mdAdjust = pfm.getMdAdjust();
            if (mdAdjust == null) {
                throw BusinessException.initExc(emp.getUserName()+"增补工时为空，请修改后再次提交");
            }
            String mdAdjustDesc = pfm.getMdAdjustDesc();
            if (!mdAdjust.equals(0) && StringUtil.isNull(mdAdjustDesc)) {
                throw BusinessException.initExc(emp.getUserName()+"增补工时说明为空，请修改后再次提交");
            }
            if (PrjConstant.PMS_PRJ_EMP_TITLE_TECH_MGT.equals(title.getCid())) {
                techMgt++;
            } else if (PrjConstant.PMS_PRJ_EMP_TITLE_SUB_PM.equals(title.getCid())) {
                subPm++;
            } else if (PrjConstant.PMS_PRJ_EMP_TITLE_KEY_EMP.equals(title.getCid())) {
                keyEmp++;
            } else if (PrjConstant.PMS_PRJ_EMP_TITLE_EMP.equals(title.getCid())) {
                regularEmp++;
            }
            //若角色设置为“技术专家”，则需要该人员是特战队成员，否则提示“技术专家，仅限于特战队成员，请修改后再次提交！”
            if (PrjConstant.PMS_PRJ_EMP_TITLE_TECH_EXPERT.equals(title.getCid())) {
                TeSysDefRoleUser sysDefRoleUser = sysDefRoleUserService.querySysDefRoleUser(prjId, PrjConstant.DEF_ID_ELITE_UNIT_ID, emp.getUserId());
                if (sysDefRoleUser == null) {
                    throw BusinessException.initExc("技术专家，仅限于特战队成员，请修改后再次提交！");
                }
            }
        }
        if (pfmOut /mdOut > 0.3){
            throw BusinessException.initExc("参与激励的外包人员超过外包人员的30%，请修改后再次提交！");
        }

        //若项目集分类=A/A+  ，或项目集分类 = B/B+且所有子项目下存在多个产品线，则技术经理个数需≤2；其他类 技术经理个数需≤ 1。否则提示“技术经理：A类及非单一产品线B类不超过2个；其他类项目都是1个。请修改后再次提交！”
        //若项目集分类=A/A+，子项目经理个数≤2；分类= B/B+，子项目经理个数≤1；其余项目不允许设置子项目经理。否则提示“子项目经理：A类不超过2个，B类最多1个，其他类不设子项目经理。请修改后再次提交！”
        //核心骨干/成员*100% > 30%，则提示“核心骨干比例不允许超过成员30%，请修改后再次提交！”
        if (PrjConstant.PRJ_LEVEL_A.equals(prjInfo.getLevel().getCid()) || PrjConstant.PRJ_LEVEL_A_PLUS.equals(prjInfo.getLevel().getCid())) {
            if (techMgt > 2) {
                throw BusinessException.initExc("技术经理：A类及非单一产品线B类不超过2个；其他类项目均为1个。请修改后再次提交！");
            }
            if (subPm > 2) {
                throw BusinessException.initExc("子项目经理：A类不超过2个请修改后再次提交！");
            }

        } else if (PrjConstant.PRJ_LEVEL_B.equals(prjInfo.getLevel().getCid()) || PrjConstant.PRJ_LEVEL_B_PLUS.equals(prjInfo.getLevel().getCid())) {
            if (techMgt > 2) {
                throw BusinessException.initExc("技术经理：A类及非单一产品线B类不超过2个；其他类项目均为1个。请修改后再次提交！");
            } else {
                boolean pls = true;
                if (CollectionUtils.isNotEmpty(prjInfo.getSubPrjs())) {
                    ArrayList<ObjectId> subPrjIds = prjInfo.getSubPrjs().stream().map(TePrjInfoSubPrj::getCid).collect(Collectors.toCollection(ArrayList::new));
                    List<TePrjInfo> subPrjs = prjInfoService.getNotSuspendPrjByPrjIds(subPrjIds);
                    for (TePrjInfo subPrj : subPrjs) {
                        if (subPrj.getPls().size() > 1) {
                            pls = false;
                        }
                    }
                }
                if (pls) {
                    if (techMgt > 1) {
                        throw BusinessException.initExc("技术经理：A类及非单一产品线B类不超过2个；其他类项目均为1个。请修改后再次提交！");
                    }
                }
            }
            if (subPm > 1) {
                throw BusinessException.initExc("子项目经理：B类最多1个请修改后再次提交！");
            }
        } else {
            if (techMgt > 1) {
                throw BusinessException.initExc("技术经理：其他类项目仅允许设置1个。请修改后再次提交！");
            }
            if (subPm > 0) {
                throw BusinessException.initExc("子项目经理：其他类不设子项目经理。请修改后再次提交！");
            }
        }
        if (keyEmp > regularEmp * 0.3) {
            throw BusinessException.initExc("核心骨干比例不允许超过成员30%，请修改后再次提交！");
        }
    }

    private void setTittle(ObjectId roleId, Map<ObjectId, SysDef> pmsPrjEmpTitleDefMap, TePrjEmpPfm prjEmpPfm) {
        if (null != roleId) {
            if (PrjConstant.PRJ_USER_ROLE_PM.equals(roleId)) {
                SysDef def = pmsPrjEmpTitleDefMap.get(PrjConstant.PMS_PRJ_EMP_TITLE_PM);
                prjEmpPfm.setTitle(def.trans2IdNameCn());
                prjEmpPfm.setTitleRatio(def.getValue());
            } else if (PrjConstant.PRJ_USER_ROLE_TECHNICAL_MANAGER.equals(roleId)) {
                SysDef def = pmsPrjEmpTitleDefMap.get(PrjConstant.PMS_PRJ_EMP_TITLE_TECH_MGT);
                prjEmpPfm.setTitle(def.trans2IdNameCn());
                prjEmpPfm.setTitleRatio(def.getValue());
            } else if (PrjConstant.PRJ_USER_ROLE_GROUP_RESP.equals(roleId)) {
                SysDef def = pmsPrjEmpTitleDefMap.get(PrjConstant.PMS_PRJ_EMP_TITLE_BIG_GROUP_RESP);
                prjEmpPfm.setTitle(def.trans2IdNameCn());
                prjEmpPfm.setTitleRatio(def.getValue());
            } else if (PrjConstant.PRJ_USER_ROLE_DEV_MANAGER.equals(roleId)||PrjConstant.PRJ_USER_ROLE_TEST_MANAGER.equals(roleId)) {
                SysDef def = pmsPrjEmpTitleDefMap.get(PrjConstant.PMS_PRJ_EMP_TITLE_MODULE_RESP);
                prjEmpPfm.setTitle(def.trans2IdNameCn());
                prjEmpPfm.setTitleRatio(def.getValue());
            } else if (PrjConstant.PRJ_USER_ROLE_ENGINEER.equals(roleId)||PrjConstant.PRJROLE_PMASSISTANT.equals(roleId)) {
                SysDef def = pmsPrjEmpTitleDefMap.get(PrjConstant.PMS_PRJ_EMP_TITLE_EMP);
                prjEmpPfm.setTitle(def.trans2IdNameCn());
                prjEmpPfm.setTitleRatio(def.getValue());
            }
        }
    }


}