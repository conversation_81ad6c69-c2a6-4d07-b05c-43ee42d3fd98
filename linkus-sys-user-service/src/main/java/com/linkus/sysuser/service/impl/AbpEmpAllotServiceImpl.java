package com.linkus.sysuser.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_L;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.BeanMapUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.dao.SysDefTypeDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.model.po.TeSysDefType;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.IAbpEmpAllotDao;
import com.linkus.sysuser.dao.ISysAiccDao;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.dao.ISysUserSstDao;
import com.linkus.sysuser.model.TeAbpEmpAllot;
import com.linkus.sysuser.model.TeAllotInfo;
import com.linkus.sysuser.model.TeSysAicc;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2DefType;
import com.linkus.sysuser.model.TeSysDefRoleUser2Role;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.model.TeSysUserSst;
import com.linkus.sysuser.service.IAbpEmpAllotService;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.sysuser.vo.AbpEmpAllotApprovalVo;
import com.linkus.sysuser.vo.AbpEmpAllotVo;
import com.linkus.sysuser.vo.EmpRoleUserVo;
import com.linkus.sysuser.vo.EmpTrackVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service("AbpEmpAllotServiceImpl")
public class AbpEmpAllotServiceImpl implements IAbpEmpAllotService {
    private static final String CTC_ID = "121";

    private static final ObjectId ABP_OTHER_ALLOT_UNIT = new ObjectId("640938ea64586deda45fbcee");
    private static final ObjectId DEV_PUBLIC = new ObjectId("64093a7064586deda45fcb26");
    private static final ObjectId REGION = new ObjectId("64093a5564586deda45fca10");
    private static final ObjectId CENTER = new ObjectId("6411230e64586deda49cb19b");
    private static final ObjectId SALE = new ObjectId("640939f164586deda45fc681");
    private static final ObjectId PUBLIC = new ObjectId("641122dc64586deda49cac3a");
    private static final String IS_EDITING = "isEditing";
    private static final String IS_INITIATED = "isInitiated";
    private static final String IS_PENDING = "isPending";
    private static final String IS_APPROVED = "isApproved";
    private static final String IS_FAILED = "isFailed";

    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;
    @Autowired
    private SysDefTypeDao sysDefTypeDao;
    @Autowired
    private IAbpEmpAllotDao abpEmpAllotDao;
    @Autowired
    private ISysUserSstDao sysUserSstDao;
    @Autowired
    private SysDefDao sysDefDao;
    @Autowired
    private ISysAiccDao sysAiccDao;
    @Autowired
    private ISysDefRoleUserDao sysDefRoleUserDao;
    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public boolean checkEmpRecord(String sbuId,String ym) {
        //即判断“abpEmpAllot”中是否存在isValid为true下的且ym为该年月下的且sbu.codeName为该BU编码下的记录
        List<IDbCondition> conds = new ArrayList<>();
        getConds(sbuId, ym, null,conds);
        long count = abpEmpAllotDao.countByConds(conds);
        if (count > 0){
            return false;
        }
        return true;
    }

    @Override
    public void initEmp(TeSysUser sysUser, String ym) {
        //sysUserSst中isValid为true下的且sbu.codeName为该BU编码下的且ym为该年月下的记录
        List<IDbCondition> conds = new ArrayList<>();
        getConds(sysUser.getSbuId(), ym,null, conds);
        List<TeSysUserSst> sysUserSsts = sysUserSstDao.findByConds(conds, null);
        if (sysUserSsts.isEmpty()){
            throw new BaseException("请先导入NameList");
        }

        conds.clear();
        //获取上个月的数据
        getConds(sysUser.getSbuId(), DateUtil.getNextMonth(DateUtil.parseDate(ym,DateUtil.DATE_MONTH_FOTMAT2),-1)
                ,SysDefConstants.PROV_INVENTORY_CN, conds);
        List<TeAbpEmpAllot> lastProvList = abpEmpAllotDao.findByConds(conds, null);
        Map<ObjectId,TeAbpEmpAllot> allotProvMap = new HashMap<>(32);
        if (!lastProvList.isEmpty()){
            for (TeAbpEmpAllot allot : lastProvList) {
                allotProvMap.put(allot.getEmp().getUserId(),allot);
            }
        }
        conds.clear();
        getConds(sysUser.getSbuId(), DateUtil.getNextMonth(DateUtil.parseDate(ym,DateUtil.DATE_MONTH_FOTMAT2),-1)
                ,SysDefConstants.SRD_INVENTORY_CN, conds);
        List<TeAbpEmpAllot> lastSrdList = abpEmpAllotDao.findByConds(conds, null);
        Map<ObjectId,TeAbpEmpAllot> allotSrdMap = new HashMap<>(32);
        if (!lastSrdList.isEmpty()){
            for (TeAbpEmpAllot allot : lastSrdList) {
                allotSrdMap.put(allot.getEmp().getUserId(),allot);
            }
        }

        Map<String, TeSysDef> ccMap = getDefMap(sysUser.getSbuId(),SysDefTypeConstants.DEPT_DEF_CODENAME,null);
        Map<String, TeSysDef> prdMap = getDefMap(sysUser.getSbuId(),SysDefTypeConstants.PRD_LINE_DEF_CODENAME,null);
        Map<String, TeSysDef> regionMap = getDefMap(sysUser.getSbuId(),SysDefTypeConstants.DEPT_DEF_CODENAME,SysDefTypeConstants.REGION_DEF_CODENAME);
        SysDef isInitiated = sysDefService.getSysDefById(SysDefConstants.IS_INITIATED_DEF_ID);
        SysDef provEmpAllot = sysDefService.getSysDefById(SysDefConstants.PROV_INVENTORY_DEF_ID);
        SysDef srdEmpAllot = sysDefService.getSysDefById(SysDefConstants.SRD_INVENTORY_DEF_ID);
        List<TeAbpEmpAllot> list = new ArrayList<>();
        for (TeSysUserSst sysUserSst : sysUserSsts) {
            TeAbpEmpAllot allot = new TeAbpEmpAllot();
            allot.setValid(true);
            allot.setLocked(false);
            allot.setStatus(isInitiated.trans2IdNameCn());
            allot.setYm(ym);
            allot.setSbu(sysUserSst.getSbu());
            allot.setEmp(sysUserSst.getUser());
            allot.setManager(sysUserSst.getManager());
            allot.setEmployeeType(sysUserSst.getEmployeeType());
            allot.setAddTime(new Date());
            allot.setAddUser(sysUser.trans2User());

            List<TeAllotInfo> allotInfo = new ArrayList<>();
            TeAllotInfo teAllotInfo = new TeAllotInfo();
            teAllotInfo.setPl(new TeIdNameCn());
            teAllotInfo.setProv(new TeIdNameCn());
            teAllotInfo.setCc(new TeIdNameCn());
            teAllotInfo.setRegion(new TeIdNameCn());
            teAllotInfo.setNum(0D);
            allotInfo.add(teAllotInfo);
            allotInfo.add(teAllotInfo);
            allot.setAllotInfo(allotInfo);


            TeAbpEmpAllot allot2 = new TeAbpEmpAllot();
            BeanUtils.copyProperties(allot,allot2);

            if (StringUtil.isNotNull(sysUserSst.getCcId())){
                allot.setCcId(sysUserSst.getCcId());
                TeSysDef def = ccMap.get(sysUserSst.getCcId());
                if (null != def){
                    if (def.getCodeName().equals("bigRegion")){
                        allot.setBigRegion(SysDef.converSysDef(def).trans2IdNameCn());
                    } else {
                        allot.setRegion(SysDef.converSysDef(def).trans2IdNameCn());
                    }
                    if (null != def.getParentDefId()){
                        SysDef bigRegion = sysDefService.getSysDefById(def.getParentDefId());
                        allot.setBigRegion(bigRegion.trans2IdNameCn());
                    }
                    //省份盘点
                    allot.setType(provEmpAllot.trans2IdNameCn());
                    if (!allotProvMap.isEmpty()){
                        if (null != allotProvMap.get(sysUserSst.getUser().getUserId())){
                            allot.setProv(allotProvMap.get(sysUserSst.getUser().getUserId()).getProv());
                            List<TeAllotInfo> infoList = allotProvMap.get(sysUserSst.getUser().getUserId()).getAllotInfo();
                            if (null != infoList){
                                for (TeAllotInfo info : infoList) {
                                    info.setCheckUser(null);
                                    info.setCheckDesc(null);
                                }
                            }
                            allot.setAllotInfo(infoList);
                        }
                    }
                    list.add(allot);
                } else {
                    TeSysDef region = regionMap.get(sysUserSst.getCcId());
                    if (null != region){
                        //省份盘点
                        allot.setType(provEmpAllot.trans2IdNameCn());
                        allot.setRegion(SysDef.converSysDef(region).trans2IdNameCn());
                        allot.setProv(null);
                        allot.setToProv(null);
                        list.add(allot);
                    }
                    if (!allotProvMap.isEmpty()){
                        if (null != allotProvMap.get(sysUserSst.getUser().getUserId())){
                            List<TeAllotInfo> infoList = allotProvMap.get(sysUserSst.getUser().getUserId()).getAllotInfo();
                            if (null != infoList){
                                for (TeAllotInfo info : infoList) {
                                    info.setCheckUser(null);
                                    info.setCheckDesc(null);
                                }
                            }
                            allot.setAllotInfo(infoList);
                        }
                    }
                }

                allot2.setCcId(sysUserSst.getCcId());
                TeSysDef prdLine = prdMap.get(sysUserSst.getCcId());
                if (null != prdLine){
                    allot2.setPl(SysDef.converSysDef(prdLine).trans2IdNameCn());
                }

                //SRD盘点
                allot2.setType(srdEmpAllot.trans2IdNameCn());
                if (!allotSrdMap.isEmpty()){
                    if (null != allotSrdMap.get(sysUserSst.getUser().getUserId())){
                        List<TeAllotInfo> infoList = allotSrdMap.get(sysUserSst.getUser().getUserId()).getAllotInfo();
                        if (null != infoList){
                            for (TeAllotInfo info : infoList) {
                                info.setCheckUser(null);
                                info.setCheckDesc(null);
                            }
                        }
                        allot2.setAllotInfo(infoList);
                        allot2.setR2dNum(allotSrdMap.get(sysUserSst.getUser().getUserId()).getR2dNum());
                    }
                }
                list.add(allot2);
            }


        }
        abpEmpAllotDao.batchSave(list);
    }

    @Override
    public List<Map> queryEmp(TeSysUser sysUser) {
        Criteria criteria = new Criteria();
        criteria.and(DFN.common_isValid.getName()).is(true);
        criteria.and(DFN.prjHealth_sbu.dot(DFN.common_cn).getName()).is(sysUser.getSbuId());
        Sort sort = Sort.by(Sort.Direction.ASC, DFN.common_addTime.n());
        GroupOperation operation = Aggregation.group(DFN.prjInfo__ym.getName())
                .first(DFN.common_addUser.getName()).as(DFN.common_addUser.getName())
                .first(DFN.prjHealth_sbu.getName()).as(DFN.prjHealth_sbu.getName())
                .first(DFN.biz_status.getName()).as(DFN.biz_status.getName())
                .first(DFN.common_addTime.getName()).as(DFN.common_addTime.getName())
                .first(DFN.biz_isLocked.getName()).as(DFN.biz_isLocked.getName());
        Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),  Aggregation.sort(sort),operation);
        List<Map> abpEmpAllot = mongoTemplate.aggregate(agg, "abpEmpAllot", Map.class).
                getMappedResults();
        return abpEmpAllot;
    }

    @Override
    public List<AbpEmpAllotVo> queryEmpRecord(TeSysUser sysUser, String ym, String typeCodeName,Boolean isLocked,String employeeType) {
        List<IDbCondition> conds = getEmpRecordCondition(sysUser, ym, typeCodeName, isLocked, employeeType);
        List<TeAbpEmpAllot> list = abpEmpAllotDao.findByConds(conds, Sort.by(Sort.Direction.DESC
                , DFN.sysUserPfm_emp.dot(DFN.common_name).getName()));

        List<String> ccId = list.stream().map(TeAbpEmpAllot::getCcId).distinct().collect(Collectors.toList());
        List<TeSysAicc> ccList = sysAiccDao.findSysAiccByCcIdList(ccId);
        Map<String, TeUser> userMap = ccList.stream().collect(Collectors.toMap(TeSysAicc::getCcId, TeSysAicc::getCcOwner));

        List<AbpEmpAllotVo> result = new ArrayList<>();
        list.stream().forEach(allot->{
            Double total = 0D;
            AbpEmpAllotVo vo = new AbpEmpAllotVo();
            BeanUtils.copyProperties(allot,vo);
            List<TeAllotInfo> allotInfo = allot.getAllotInfo();
            for (TeAllotInfo teAllotInfo : allotInfo) {
                Double num = teAllotInfo.getNum();
                total += num;
            }
            vo.setTotal(total);
            vo.setCcOwner(userMap.get(vo.getCcId()));
            result.add(vo);
        });

        return result;
    }

    private List<IDbCondition> getEmpRecordCondition(TeSysUser sysUser, String ym, String typeCodeName, Boolean isLocked, String employeeType) {
        List<IDbCondition> conds = new ArrayList<>();
        getConds(sysUser.getSbuId(), ym, typeCodeName, conds);

        TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
        List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
        TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
        role.setRoleCodeName(SysDefConstants.BUOMADMIN_CODENAME);
        roles.add(role);
        sysDefRoleUser.setRole(roles);
        TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
        roleUser.setLoginName(sysUser.getLoginName());
        sysDefRoleUser.setRoleUser(roleUser);
        TeSysDefRoleUser2DefType type = new TeSysDefRoleUser2DefType();
        if (SysDefConstants.PROV_INVENTORY_CN.equals(typeCodeName)){
            type.setDefTypeCodeName(SysDefTypeConstants.DEPT_DEF_CODENAME);
        } else {
            type.setDefTypeCodeName(SysDefTypeConstants.PRD_LINE_DEF_CODENAME);
        }
        sysDefRoleUser.setDefType(type);
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.querySysUserDistinctColl(sysDefRoleUser);
        if (roleUsers.isEmpty()){
            //查询CCowner为自己的CCId
            List<IDbCondition> conds2 = new ArrayList<>();
            conds2.add(new DC_E(DFN.sysAicc_ccOwner.dot(DFN.common_userId), sysUser.getId()));
            List<TeSysAicc> teSysAiccs = sysAiccDao.findByConds(conds2, null);
            List<String> ccIds = new ArrayList<>();
            for (TeSysAicc teSysAicc : teSysAiccs) {
                ccIds.add(teSysAicc.getCcId());
            }
            conds.add(new DC_I<>(DFN.SysUser.ccId, ccIds));
        } else {
            List<ObjectId> defIds = new ArrayList<>();
            for (TeSysDefRoleUser user : roleUsers) {
                defIds.add(user.getDefId());
            }
            if (SysDefConstants.PROV_INVENTORY_CN.equals(typeCodeName)){
                List<IDbCondition> or = new ArrayList<>();
                or.add(new DC_I<>(DFN.omsFault_prov.dot(DFN.common_cid), defIds));
                or.add(new DC_I<>(DFN.omsFault_region.dot(DFN.common_cid), defIds));
                or.add(new DC_I<>(DFN.omsFault_bigRegion.dot(DFN.common_cid), defIds));
                conds.add(new DC_OR(or));
            } else {
                conds.add(new DC_I<>(DFN.prjInfo__pl.dot(DFN.common_cid), defIds));
            }
        }
        if (null != isLocked){
            conds.add(new DC_E(DFN.omsCheckRcd_isLocked, isLocked));
        }
        if (StringUtil.isNotNull(employeeType)){
            conds.add(new DC_E(DFN.sysUser_employeeType, employeeType));
        }
        return conds;
    }

    @Override
    public PageBean queryEmpRecord(TeSysUser sysUser, String ym, String typeCodeName, Boolean isLocked, String employeeType, Integer pageIndex, Integer pageSize) {
        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = getEmpRecordCondition(sysUser, ym, typeCodeName, isLocked, employeeType);
        long count = abpEmpAllotDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count == 0){
            return pageBean;
        }

        Pager pager = null;
        if (null != pageIndex && null != pageSize){
            pager = new Pager(pageIndex-1, pageSize);
        }
        List<TeAbpEmpAllot> list = abpEmpAllotDao.findByConds(conds, Sort.by(Sort.Direction.DESC
                , DFN.sysUserPfm_emp.dot(DFN.common_name).getName()),pager);
        List<String> ccId = list.stream().map(TeAbpEmpAllot::getCcId).distinct().collect(Collectors.toList());
        List<TeSysAicc> ccList = sysAiccDao.findSysAiccByCcIdList(ccId);
        Map<String, TeUser> userMap = ccList.stream().collect(Collectors.toMap(TeSysAicc::getCcId, TeSysAicc::getCcOwner));

        List<AbpEmpAllotVo> result = new ArrayList<>();
        list.stream().forEach(allot->{
            AbpEmpAllotVo vo = new AbpEmpAllotVo();
            BeanUtils.copyProperties(allot,vo);
            vo.setCcOwner(userMap.get(vo.getCcId()));
            result.add(vo);
        });
        pageBean.setObjectList(result);
        return pageBean;
    }

    @Override
    public void submitEmpRecord(TeSysUser sysUser, String ym, String typeCodeName) {
        List<AbpEmpAllotVo> list = queryEmpRecord(sysUser, ym, typeCodeName,false,null);
        list.stream().map(AbpEmpAllotVo::getDesc).forEach(desc -> {
            if (!Optional.ofNullable(desc).isPresent()){
                throw BusinessException.initExc("工作内容及投入项目字段不能为空");
            }
        });
        SysDef isPending = sysDefService.getSysDefById(SysDefConstants.IS_PENDING_DEF_ID);
        SysDef isApproved = sysDefService.getSysDefById(SysDefConstants.IS_APPROVED_DEF_ID);
        for (AbpEmpAllotVo vo : list) {
            TeAbpEmpAllot empAllot = new TeAbpEmpAllot();
            BeanUtils.copyProperties(vo,empAllot);
            //1、更新状态
            empAllot.setStatus(isPending.trans2IdNameCn());
            empAllot.setLocked(true);
            if (null == empAllot.getRegion() || null == empAllot.getBigRegion()){
                throw BusinessException.initExc(empAllot.getEmp().getUserName()+"区域或大区为空");
            }
            List<TeAllotInfo> allotInfo = empAllot.getAllotInfo();
            double total = 0;
            //2、生成待审批人
            for (TeAllotInfo teAllotInfo : allotInfo) {
                if (null != teAllotInfo.getOtherAllotUnit()){
                    continue;
                }
                total += teAllotInfo.getNum();
                StringBuilder sb = new StringBuilder();
                //如果type.cid为ObjectId("5fa3bd174396e16e7e92df3c")即省份人员盘点，再判断cc信息是否为空。如果不为空，则表示是省份盘点到SRD的产品线下的CC下的，直接取“sysAicc”中该CC下的ccOwner.userId为待评审人。
                if (empAllot.getType().getCid().equals(SysDefConstants.PROV_INVENTORY_DEF_ID)){
                    if (null != teAllotInfo.getCc() && null != teAllotInfo.getCc().getCid()){
                        List<IDbCondition> conds = new ArrayList<>();
                        conds.add(new DC_E(DFN.sysAicc_ccId,teAllotInfo.getCc().getCodeName()));
                        List<TeSysAicc> sysAiccs = sysAiccDao.findByConds(conds, null);
                        if (!sysAiccs.isEmpty()){
                            sb.append(",").append(sysAiccs.get(0).getCcOwner().getUserId().toHexString());
                        }
                        teAllotInfo.setToCheckUsers(sb.toString());
                    } else {
                        // 如果为空，则取产品线下的管理员，即从“sysDefRoleUser”中取出isValid为true
                        // 下的且role.roleId为ObjectId("5fa8c2004396e16e7e92dfba")即BU运营管理员下的roleUser清单。
                        List<IDbCondition> conds = new ArrayList<>();
                        conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId)
                                ,StringUtil.toObjectId(SysDefConstants.BUOMADMIN_DEF_ID)));
                        conds.add(new DC_E(DFN.sysDefRoleUser__defId,teAllotInfo.getProv().getCid()));
                        List<TeSysDefRoleUser> defRoleUsers = sysDefRoleUserDao.findByConds(conds, null);
                        if (!defRoleUsers.isEmpty()){
                            for (TeSysDefRoleUser defRoleUser : defRoleUsers) {
                                sb.append(",").append(defRoleUser.getRoleUser().getUserId().toHexString());
                            }
                            teAllotInfo.setToCheckUsers(sb.toString());
                        }
                    }
                } else {
                    if (null != teAllotInfo.getProv() && null != teAllotInfo.getProv().getCid()){
                        List<IDbCondition> conds = new ArrayList<>();
                        conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId)
                                ,StringUtil.toObjectId(SysDefConstants.ADMIN_DEF_ID)));
                        conds.add(new DC_E(DFN.sysDefRoleUser__defId,teAllotInfo.getProv().getCid()));
                        List<TeSysDefRoleUser> defRoleUsers = sysDefRoleUserDao.findByConds(conds, null);
                        if (!defRoleUsers.isEmpty()){
                            for (TeSysDefRoleUser defRoleUser : defRoleUsers) {
                                sb.append(",").append(defRoleUser.getRoleUser().getUserId().toHexString());
                            }
                            teAllotInfo.setToCheckUsers(sb.toString());
                        }
                    }
                }

            }
            if (total == 0){
                empAllot.setStatus(isApproved.trans2IdNameCn());
            }
            abpEmpAllotDao.updateById(empAllot.getId(),empAllot);
        }

    }

    @Override
    public List<TeSysAicc> queryCcByCcList(TeSysUser sysUser, List<String> ccList) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.sysAicc_ccId, ccList));
        return sysAiccDao.findByConds(conds,null);
    }

    @Override
    public void upDateEmpRecord(TeAbpEmpAllot abpEmpAllot,TeSysUser currentTeSysUser) {
        if (SysDefConstants.IS_INITIATED_DEF_ID.equals(abpEmpAllot.getStatus().getCid())){
            SysDef isEditing = sysDefService.getSysDefById(SysDefConstants.IS_EDITING_DEF_ID);
            abpEmpAllot.setStatus(isEditing.trans2IdNameCn());
            abpEmpAllot.setAddUser(currentTeSysUser.trans2User());
            abpEmpAllot.setAddTime(new Date());
        }

        TeIdNameCn region = abpEmpAllot.getRegion();
        if (null != region){
            SysDef regionDef = sysDefService.getSysDefById(region.getCid());
            SysDef bigRegionDef = sysDefService.getSysDefById(regionDef.getParentDefId());
            abpEmpAllot.setBigRegion(bigRegionDef.trans2IdNameCn());
        }
        abpEmpAllotDao.updateById(abpEmpAllot.getId(),abpEmpAllot,true);
    }

    @Override
    public void updateOtherAllotUnit(ObjectId id, ObjectId cid, double num,TeSysUser currentTeSysUser) {
        TeAbpEmpAllot abpEmpAllot = abpEmpAllotDao.findById(id);
        if (null == abpEmpAllot){
            throw BusinessException.initExc("id无效");
        }
        if (SysDefConstants.IS_INITIATED_DEF_ID.equals(abpEmpAllot.getStatus().getCid())){
            SysDef isEditing = sysDefService.getSysDefById(SysDefConstants.IS_EDITING_DEF_ID);
            abpEmpAllot.setStatus(isEditing.trans2IdNameCn());
            abpEmpAllot.setAddUser(currentTeSysUser.trans2User());
            abpEmpAllot.setAddTime(new Date());
        }
        SysDef abpOtherAllotUnit = sysDefService.getSysDefById(cid);
        List<TeAllotInfo> allotInfo = abpEmpAllot.getAllotInfo();
        TeAllotInfo teAllotInfo = null;
        boolean flag = allotInfo.stream().filter(info -> null != info.getOtherAllotUnit()
                        && info.getOtherAllotUnit().getCid().equals(abpOtherAllotUnit.getId()))
                .findFirst().isPresent();
        if (flag){
            teAllotInfo = allotInfo.stream().filter(info -> null != info.getOtherAllotUnit()
                            && info.getOtherAllotUnit().getCid().equals(abpOtherAllotUnit.getId()))
                    .findFirst().get();
        }
        if (null == teAllotInfo){
            teAllotInfo = new TeAllotInfo();
            teAllotInfo.setOtherAllotUnit(abpOtherAllotUnit.trans2IdNameCn());
            allotInfo.add(teAllotInfo);
        }
        teAllotInfo.setNum(num);
        abpEmpAllot.setAllotInfo(allotInfo);
        abpEmpAllotDao.updateById(id,abpEmpAllot);
    }

    @Override
    public List<TeSysDef> queryRegion(String sbuId) {
        return getTeSysDefs(sbuId,SysDefConstants.SRD_REGION);
    }

    @Override
    public List<TeSysDef> queryAllProv(String codeName, String sbuId) {
        return getTeSysDefs(sbuId,codeName);
    }

    @Override
    public void shroud(String ym, String sbuId) {
        List<IDbCondition> conds = new ArrayList<>();
        getConds(sbuId, ym, null,conds);
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.omsCheckRcd_isLocked, true));
        abpEmpAllotDao.updateByConds(conds,updates);
    }

    @Override
    public List<String> getColumnNames(Integer index,String sbuId,String name) {
        List<String> list = new ArrayList<>();
        if (StringUtil.isNotNull(name)){
            list.add(name+"的实习生");
            if (index == 2){
                list.add("合计");
                list.add("研发&公共");
            }
        } else if (1 == index){
            list.add("PSO人员盘点分析（正式/外包）");
        } else {
            list.add("SRD人员盘点分析");
            list.add("合计");
            list.add("研发&公共");
        }
        list.add("区域合计");
        List<TeSysDef> region = queryRegion(sbuId);
        for (TeSysDef sysDef : region) {
            list.add(sysDef.getDefName());
            List<SysDef> provDef = sysDefService.getSysDefByParentDefId(sysDef.getId());
            provDef = provDef.stream().sorted(Comparator.comparing(SysDef::getDefNo)).collect(Collectors.toList());
            for (SysDef def : provDef) {
                list.add(def.getDefName());
            }
        }
        if (StringUtil.isNull(name)){
            List<TeSysDef> bigRegion = queryAllProv("bigRegion",sbuId);
            for (TeSysDef teSysDef : bigRegion) {
                list.add(teSysDef.getDefName());
            }
        }
        return list;
    }

    private List<TeSysDef> getTeSysDefs(String sbuId,String codeName) {
        List<IDbCondition> conds = new ArrayList<>();
        getDefConds(sbuId, conds, codeName);
        return sysDefDao.findByConds(conds,Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n()));
    }

    @Override
    public List<AbpEmpAllotApprovalVo> queryApprovalEmpRecord(ObjectId userId, String ym, ObjectId regionId, ObjectId provId) {
        List<AbpEmpAllotApprovalVo> result = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.prjInfo__ym, ym));
        conds.add(new DC_E(DFN.biz_status.dot(DFN.common_cid), SysDefConstants.IS_PENDING_DEF_ID));
        conds.add(new DC_L(DFN.abpEmpAllot_allotInfo.dot(DFN.sysUserApply_toCheckUsers), userId.toHexString()));
        if (null != regionId){
            conds.add(new DC_E(DFN.omsFault_region.dot(DFN.common_cid), regionId));
        }
        if (null != provId){
            conds.add(new DC_E(DFN.omsFault_prov.dot(DFN.common_cid), provId));
        }
        List<TeAbpEmpAllot> abpEmpAllots = abpEmpAllotDao.findByConds(conds, Sort.by(Sort.Direction.ASC
                , DFN.omsFault_prov.dot(DFN.common_cid).getName()));
        for (TeAbpEmpAllot abpEmpAllot : abpEmpAllots) {
            int index = 0;
            List<TeAllotInfo> allotInfo = abpEmpAllot.getAllotInfo();
            if (null == abpEmpAllot){
                continue;
            }
            for (TeAllotInfo teAllotInfo : allotInfo) {
                if (null != teAllotInfo && null != teAllotInfo.getToCheckUsers() &&null == teAllotInfo.getCheckUser()
                        && teAllotInfo.getToCheckUsers().indexOf(userId.toHexString()) > 0){
                    AbpEmpAllotApprovalVo vo = new AbpEmpAllotApprovalVo();
                    vo.setId(abpEmpAllot.getId());
                    vo.setRegion(abpEmpAllot.getRegion());
                    vo.setProv(abpEmpAllot.getProv());
                    vo.setEmployeeType(abpEmpAllot.getEmployeeType());
                    vo.setCcId(abpEmpAllot.getCcId());
                    vo.setBigRegion(abpEmpAllot.getBigRegion());
                    vo.setManager(abpEmpAllot.getManager());
                    vo.setEmp(abpEmpAllot.getEmp());
                    vo.setPl(teAllotInfo.getPl());
                    vo.setCc(teAllotInfo.getCc());
                    vo.setNum(teAllotInfo.getNum());
                    vo.setIndex(index);
                    result.add(vo);
                }
                index++;
            }
        }
        return result;
    }

    @Override
    public void approval(TeSysUser sysUser,List<ObjectId> ids,String desc,List<Integer> indexes) {
        int idx = 0;
        SysDef isApproved = sysDefService.getSysDefById(SysDefConstants.IS_APPROVED_DEF_ID);
        for (ObjectId id : ids) {
            TeAbpEmpAllot allot = abpEmpAllotDao.findById(id);
            List<TeAllotInfo> allotInfo = allot.getAllotInfo();
            if (null != allot && null != allotInfo){
                TeAllotInfo teAllotInfo = allotInfo.get(indexes.get(idx));
                teAllotInfo.setCheckUser(sysUser.trans2User());
                teAllotInfo.setCheckTime(new Date());
                teAllotInfo.setCheckDesc(desc);
                teAllotInfo.setCheckResult(isApproved.trans2IdNameCn());
            }
            boolean isOk = true;
            double num = 0;
            for (TeAllotInfo teAllotInfo : allotInfo) {
                if (null != teAllotInfo.getOtherAllotUnit()){
                    continue;
                }
                num += teAllotInfo.getNum();
            }
            if (num < 1){
                isOk = false;
            }
            if (isOk){
                allot.setStatus(isApproved.trans2IdNameCn());
            }
            abpEmpAllotDao.save(allot);
            idx++;
        }
    }

    @Override
    public List<AbpEmpAllotApprovalVo> reject(TeSysUser sysUser, List<ObjectId> ids, String desc,List<Integer> indexes) {
        List<AbpEmpAllotApprovalVo> result = new ArrayList<>();
        int idx = 0;
        SysDef isFailed = sysDefService.getSysDefById(SysDefConstants.IS_FAILED_DEF_ID);
        for (ObjectId id : ids) {
            TeAbpEmpAllot allot = abpEmpAllotDao.findById(id);
            allot.setLocked(false);
            List<TeAllotInfo> allotInfo = allot.getAllotInfo();
            if (null != allot && null != allotInfo){
                TeAllotInfo teAllotInfo = allotInfo.get(indexes.get(idx));
                teAllotInfo.setCheckUser(sysUser.trans2User());
                teAllotInfo.setCheckTime(new Date());
                teAllotInfo.setCheckDesc(desc);
                teAllotInfo.setCheckResult(isFailed.trans2IdNameCn());

                AbpEmpAllotApprovalVo vo = new AbpEmpAllotApprovalVo();
                vo.setId(allot.getId());
                vo.setRegion(allot.getRegion());
                vo.setProv(allot.getProv());
                vo.setEmployeeType(allot.getEmployeeType());
                vo.setCcId(allot.getCcId());
                vo.setBigRegion(allot.getBigRegion());
                vo.setManager(allot.getManager());
                vo.setEmp(allot.getEmp());
                vo.setPl(teAllotInfo.getPl());
                vo.setCc(teAllotInfo.getCc());
                vo.setNum(teAllotInfo.getNum());
                vo.setIndex(indexes.get(idx));
                vo.setDesc(desc);
                result.add(vo);
            }
            SysDef isApproved = sysDefService.getSysDefById(SysDefConstants.IS_FAILED_DEF_ID);
            allot.setStatus(isApproved.trans2IdNameCn());

            TeAbpEmpAllot allot2 = new TeAbpEmpAllot();
            //再拷贝一份该用户的盘点记录，用于记录历史
            BeanUtils.copyProperties(allot,allot2);
            allot.setValid(false);
            allot.setSrcAllotId(allot.getId());
            allot.setId(null);
            abpEmpAllotDao.save(allot);

            List<TeAllotInfo> allotInfo2 = allot2.getAllotInfo();
            if (null != allotInfo2){
                for (TeAllotInfo teAllotInfo : allotInfo2) {
                    teAllotInfo.setCheckUser(null);
                    teAllotInfo.setCheckTime(null);
                }
            }
            abpEmpAllotDao.save(allot2);
            idx++;
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> exportEmpRecord(TeSysUser sysUser, String ym, String typeCodeName, Boolean isLocked,String employeeType) {
        List<AbpEmpAllotVo> list = queryEmpRecord(sysUser, ym, typeCodeName, isLocked,employeeType);
        if (null != list && !list.isEmpty()){
            List<Map<String, Object>> maps = convert(list);
            return maps;
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> call(List<String> columns, String ym,String sbuId,Boolean isEmployeeTag) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        List<TeSysDef> defs = sysDefService.getSysByDefTypeCodeNameAndSrcDefCodeName(SysDefTypeConstants.PRD_LINE_DEF_CODENAME, sbuId);
        List<IDbCondition> conds = new ArrayList<>();
        for (TeSysDef def : defs) {
            Map<String,Double> map = new HashMap<>(16);
            conds.clear();
            getConds(sbuId, ym, SysDefConstants.PROV_INVENTORY_CN,conds);
            conds.add(new DC_E(DFN.abpEmpAllot_allotInfo.dot(DFN.prjInfo__pl).dot(DFN.common_cid),def.getId()));
            if (null == isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_PRACTICE));
            } else if (isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_FORMAL));
            } else {
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE));
            }
            List<TeAbpEmpAllot> abpEmpAllots = abpEmpAllotDao.findByConds(conds, null);
            for (TeAbpEmpAllot abpEmpAllot : abpEmpAllots) {
                List<TeAllotInfo> allotInfo = abpEmpAllot.getAllotInfo();
                allotInfo = allotInfo.stream().filter(info -> null != info.getPl() && null != info.getPl().getCid()
                        && info.getPl().getCid().equals(def.getId())).collect(Collectors.toList());
                TeIdNameCn region = abpEmpAllot.getRegion();
                TeIdNameCn prov = abpEmpAllot.getProv();
                //TeIdNameCn bigRegion = abpEmpAllot.getBigRegion();

                getNumMap(map, allotInfo, region,true);
                getNumMap(map, allotInfo, prov,false);
                //getNumMap(map, allotInfo, bigRegion,false);

            }
            Map<String, Object> data = new HashMap<>(32);
            int index = 0;
            data.put(String.valueOf(index),def.getDefName());
            for (String column : columns) {
                if (index == 0){
                    index++;
                    continue;
                }
                if (null == map.get(column)){
                    data.put(String.valueOf(index),0D);
                } else {
                    data.put(String.valueOf(index),map.get(column));
                }
                index++;
            }
            double regionCount = map.get("regionTotal") == null ? 0D : map.get("regionTotal");
            data.put("1", regionCount);

            dataMap.add(data);
        }

        List<SysDef> sysDefs = sysDefService.getSysDefsBySrc(ABP_OTHER_ALLOT_UNIT, SysDefTypeCodeName.SYS_PARA_VALUE);
        sysDefs = sysDefs.stream().sorted(Comparator.comparing(SysDef::getDefNo)).collect(Collectors.toList());
        for (SysDef sysDef : sysDefs) {
            if (sysDef.getId().equals(DEV_PUBLIC)){
                continue;
            }
            if (isEmployeeTag == null && !sysDef.getId().equals(REGION)){
                continue;
            }
            Map<String,Double> map = new HashMap<>(4);

            conds.clear();
            getConds(sbuId, ym, SysDefConstants.PROV_INVENTORY_CN,conds);
            if (null == isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_PRACTICE));
            } else if (isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_FORMAL));
            } else {
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE));
            }
            conds.add(new DC_E(DFN.abpEmpAllot_allotInfo.dot(DbFieldName.abpEmpAllot_otherAllotUnit).dot(DFN.common_cid)
                    ,sysDef.getId()));

            List<TeAbpEmpAllot> empAllots = abpEmpAllotDao.findByConds(conds, null);
            for (TeAbpEmpAllot empAllot : empAllots) {
                List<TeAllotInfo> allotInfo = empAllot.getAllotInfo();
                TeIdNameCn region = empAllot.getRegion();
                TeIdNameCn prov = empAllot.getProv();
                TeIdNameCn bigRegion = empAllot.getBigRegion();

                if (CENTER.equals(sysDef.getId())){
                    getNumMap(map, allotInfo, bigRegion,sysDef,true);
                } else {
                    getNumMap(map, allotInfo, region,sysDef,true);
                    if (SALE.equals(sysDef.getId()) || PUBLIC.equals(sysDef.getId())){
                        getNumMap(map, allotInfo, prov,sysDef,false);
                    }
                }
            }
            Map<String, Object> data = new HashMap<>(32);
            int index = 0;
            data.put(String.valueOf(index),sysDef.getDefName());
            for (String column : columns) {
                if (index == 0){
                    index++;
                    continue;
                }
                if (null == map.get(column)){
                    data.put(String.valueOf(index),0D);
                } else {
                    data.put(String.valueOf(index),map.get(column));
                }
                index++;
            }
            double regionCount = map.get("regionTotal") == null ? 0D : map.get("regionTotal");
            data.put("1", regionCount);

            dataMap.add(data);

        }

        Map<String,Object> totalMap = new HashMap<>(16);
        if (null == isEmployeeTag){
            totalMap.put("0","合计");
        } else if (isEmployeeTag){
            totalMap.put("0","正式（合计）");
        } else {
            totalMap.put("0","外包（合计）");
        }

        for (Map<String, Object> map : dataMap) {
            for (String index : map.keySet()) {
                if (Integer.parseInt(index) == 0){
                    continue;
                }
                Object totalNum = totalMap.get(index);
                Object num = map.get(index);
                if (null == totalNum){
                    totalMap.put(index, num);
                } else {
                    totalMap.put(index, addDouble(Double.parseDouble(totalNum.toString()),Double.parseDouble(num.toString())));
                }
            }
        }
        dataMap.add(totalMap);
        return dataMap;
    }

    @Override
    public List<Map<String, Object>> input(List<String> columns, String ym, String sbuId,Boolean isEmployeeTag) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        List<TeSysDef> defs = sysDefService.getSysByDefTypeCodeNameAndSrcDefCodeName(SysDefTypeConstants.PRD_LINE_DEF_CODENAME, sbuId);
        List<IDbCondition> conds = new ArrayList<>();
        for (TeSysDef def : defs) {
            Map<String,Double> map = new HashMap<>(16);
            conds.clear();
            getConds(sbuId, ym, SysDefConstants.SRD_INVENTORY_CN,conds);
            conds.add(new DC_E(DFN.prjInfo__pl.dot(DFN.common_cid),def.getId()));
            if (null == isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_PRACTICE));
            } else if (isEmployeeTag){
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_FORMAL));
            } else {
                conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_OUTSOURCE));
            }
            List<TeAbpEmpAllot> abpEmpAllots = abpEmpAllotDao.findByConds(conds, null);
            Double totalR2dNum = 0D;
            for (TeAbpEmpAllot abpEmpAllot : abpEmpAllots) {
                List<TeAllotInfo> allotInfo = abpEmpAllot.getAllotInfo();
                for (TeAllotInfo teAllotInfo : allotInfo) {
                    TeIdNameCn region = teAllotInfo.getRegion();
                    TeIdNameCn prov = teAllotInfo.getProv();

                    getNumMap(map, teAllotInfo, region,true);
                    getNumMap(map, teAllotInfo, prov,false);
                }

                Double r2dNum = abpEmpAllot.getR2dNum();
                if (null == r2dNum){
                    r2dNum = 0D;
                }
                totalR2dNum = addDouble(totalR2dNum, r2dNum);
            }
            Map<String, Object> data = new HashMap<>(32);

            int index = 0;
            data.put(String.valueOf(index),def.getDefName());
            for (String column : columns) {
                if (index == 0){
                    index++;
                    continue;
                }
                if (null == map.get(column)){
                    data.put(String.valueOf(index),0D);
                } else {
                    data.put(String.valueOf(index),map.get(column));
                }
                index++;
            }
            double regionCount = map.get("regionTotal") == null ? 0D : map.get("regionTotal");
            data.put("3", regionCount);

            data.put("2",totalR2dNum);
            double totalCount = addDouble(totalR2dNum, regionCount);
            data.put("1", totalCount);
            dataMap.add(data);

        }

        Map<String,Object> totalMap = new HashMap<>(16);
        if (null == isEmployeeTag){
            totalMap.put("0","合计");
        }else if (isEmployeeTag){
            totalMap.put("0","正式（合计）");
        } else {
            totalMap.put("0","外包（合计）");
        }

        for (Map<String, Object> objectMap : dataMap) {
            for (String idx : objectMap.keySet()) {
                if (Integer.parseInt(idx) == 0){
                    continue;
                }
                Object totalNum = totalMap.get(idx);
                Object num = objectMap.get(idx);
                if (null == totalNum){
                    totalMap.put(idx, num);
                } else {
                    totalMap.put(idx, addDouble(Double.parseDouble(totalNum.toString()),Double.parseDouble(num.toString())));
                }
            }
        }
        dataMap.add(totalMap);
        return dataMap;
    }

    @Override
    public List<EmpRoleUserVo> queryRoleUser(String codeName, String sbuId) {
        List<EmpRoleUserVo>  list = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        if (SysDefTypeConstants.PRD_LINE_DEF_CODENAME.equals(codeName)){
            List<TeSysDef> defs = sysDefService.getSysByDefTypeCodeNameAndSrcDefCodeName(SysDefTypeConstants.PRD_LINE_DEF_CODENAME, sbuId);
            for (TeSysDef sysDef : defs) {
                EmpRoleUserVo vo = new EmpRoleUserVo();
                TeIdNameCn cn = SysDef.converSysDef(sysDef).trans2IdNameCn();
                List<TeSysDefRoleUser> roleUsers = getTeSysDefRoleUsers(sysDef.getId());
                vo.setCn(cn);
                vo.setRoleUsers(roleUsers);
                list.add(vo);
            }
        } else if (SysDefTypeConstants.REGION_DEF_CODENAME.equals(codeName)){
            conds.clear();
            getDefConds(sbuId,conds,SysDefTypeConstants.REGION_DEF_CODENAME);
            List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
            for (TeSysDef sysDef : sysDefs) {
                EmpRoleUserVo vo = new EmpRoleUserVo();
                TeIdNameCn cn = SysDef.converSysDef(sysDef).trans2IdNameCn();
                List<TeSysDefRoleUser> roleUsers = getTeSysDefRoleUsers(sysDef.getId());
                vo.setCn(cn);
                vo.setRoleUsers(roleUsers);
                list.add(vo);
            }
        } else if (SysDefTypeConstants.PROV_CODENAME.equals(codeName)){
            conds.clear();
            getDefConds(sbuId,conds,SysDefTypeConstants.PROV_CODENAME);
            List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
            for (TeSysDef sysDef : sysDefs) {
                EmpRoleUserVo vo = new EmpRoleUserVo();
                TeIdNameCn cn = SysDef.converSysDef(sysDef).trans2IdNameCn();
                List<TeSysDefRoleUser> roleUsers = getTeSysDefRoleUsers(sysDef.getId());
                vo.setCn(cn);
                vo.setRoleUsers(roleUsers);
                list.add(vo);
            }
        } else if (SysDefTypeConstants.BIG_REGION_DEF_CODENAME.equals(codeName)){
            conds.clear();
            getDefConds(sbuId,conds,SysDefTypeConstants.BIG_REGION_DEF_CODENAME);
            List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
            for (TeSysDef sysDef : sysDefs) {
                EmpRoleUserVo vo = new EmpRoleUserVo();
                TeIdNameCn cn = SysDef.converSysDef(sysDef).trans2IdNameCn();
                List<TeSysDefRoleUser> roleUsers = getTeSysDefRoleUsers(sysDef.getId());
                vo.setCn(cn);
                vo.setRoleUsers(roleUsers);
                list.add(vo);
            }
        } else {
            SysDef def = sysDefService.getSysDefByCodeName(sbuId, SysDefTypeCodeName.AI_BU);
            if (null != def){
                EmpRoleUserVo vo = new EmpRoleUserVo();
                TeIdNameCn cn = def.trans2IdNameCn();
                List<TeSysDefRoleUser> roleUsers = getTeSysDefRoleUsers(def.getId());
                vo.setCn(cn);
                vo.setRoleUsers(roleUsers);
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public void addRoleUser(ObjectId defId, ObjectId userId,String codeName, TeSysUser loginUser) {
        TeSysDefRoleUser teSysDefRoleUser = new TeSysDefRoleUser();
        //defType
        TeSysDefRoleUser2DefType defType = new TeSysDefRoleUser2DefType();
        TeSysDefType sysDefType ;
        if (SysDefTypeConstants.PRD_LINE_DEF_CODENAME.equals(codeName)){
            sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeConstants.PRD_LINE_DEF_CODENAME);
        } else if (SysDefTypeConstants.REGION_DEF_CODENAME.equals(codeName)
                || SysDefTypeConstants.PROV_CODENAME.equals(codeName)
                || SysDefTypeConstants.BIG_REGION_DEF_CODENAME.equals(codeName)){
            sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeConstants.DEPT_DEF_CODENAME);
        } else {
            sysDefType = sysDefTypeDao.getSysDefTypeByCodeName(SysDefTypeConstants.AI_BU_CODENAME);
        }
        defType.setDefTypeId(sysDefType.getId());
        defType.setDefTypeName(sysDefType.getDefTypeName());
        defType.setDefTypeCodeName(sysDefType.getCodeName());
        teSysDefRoleUser.setDefType(defType);
        //role成员角色
        List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
        TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
        SysDef roleDef = sysDefService.getSysDefById(StringUtil.toObjectId(SysDefConstants.BUOMADMIN_DEF_ID));
        role.setRoleId(roleDef.getId());
        role.setRoleCodeName(roleDef.getCodeName());
        role.setRoleName(roleDef.getDefName());
        roles.add(role);
        teSysDefRoleUser.setRole(roles);
        //addUser添加人信息
        TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
        addUser.setUserId(loginUser.getId());
        addUser.setLoginName(loginUser.getLoginName());
        addUser.setUserName(loginUser.getUserName());
        addUser.setJobCode(loginUser.getJobCode());
        teSysDefRoleUser.setAddUser(addUser);
        //sysDef
        teSysDefRoleUser.setDefId(defId);
        //roleUser
        TeSysUser member = sysUserService.findById(userId);
        TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
        roleUser.setUserId(member.getId());
        roleUser.setLoginName(member.getLoginName());
        roleUser.setUserName(member.getUserName());
        roleUser.setJobCode(member.getJobCode());
        teSysDefRoleUser.setRoleUser(roleUser);
        teSysDefRoleUser.setAddTime(new Date());
        teSysDefRoleUser.setIsValid(true);

        List<TeSysDefRoleUser> list = new ArrayList<TeSysDefRoleUser>();
        list.add(teSysDefRoleUser);
        sysDefRoleUserService.addTeSysDefRoleUserList(list);
    }

    @Override
    public Map<String, Object> importEmp(MultipartFile file, String ym) {
        Map<String, Object> result = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        getConds(CTC_ID, ym, SysDefConstants.PROV_INVENTORY_CN,conds);
        List<TeAbpEmpAllot> provEmp = abpEmpAllotDao.findByConds(conds, null);
        Map<String, TeAbpEmpAllot> provEmpMap = new HashMap<>(64);
        for (TeAbpEmpAllot allot : provEmp) {
            provEmpMap.put(allot.getEmp().getJobCode(),allot);
        }

        conds.clear();
        getConds(CTC_ID, ym, SysDefConstants.SRD_INVENTORY_CN,conds);
        List<TeAbpEmpAllot> srdEmp = abpEmpAllotDao.findByConds(conds, null);
        Map<String, TeAbpEmpAllot> srdEmpMap = new HashMap<>(64);
        for (TeAbpEmpAllot allot : srdEmp) {
            srdEmpMap.put(allot.getEmp().getJobCode(),allot);
        }

        Map<String, TeSysDef> regionMap = getDefStringMap(CTC_ID,SysDefTypeConstants.DEPT_DEF_CODENAME,null);
        Map<String, TeSysDef> plMap = getDefStringMap(CTC_ID,SysDefTypeConstants.PRD_LINE_DEF_CODENAME,null);
        plMap.putAll(regionMap);
        Map<String, TeSysAicc> ccMap = getCcMap(plMap,false);

        InputStream in = null;
        Workbook wb = null;
        Sheet sheet1 = null;
        Sheet sheet2 = null;
        try {
            in = file.getInputStream();
            wb = WorkbookFactory.create(in);
            sheet1 = wb.getSheetAt(0);
            sheet2 = wb.getSheetAt(1);
        }
        catch (IOException excelIOError) {
            result.put("result", "error");
            result.put("msg", "读取文件失败!");
            return result;
        }
        catch (InvalidFormatException excelChangeError) {
            result.put("result", "error");
            result.put("msg", "文件格式错误！");
            return result;
        }
        finally {
            try {
                in.close();
            }
            catch (IOException e) {
                throw new BaseException(" close file IO error ! ");
            }
        }

        int rowNum = sheet1.getPhysicalNumberOfRows();
        Row checkDataRow = null;
        for(int i=1; i<rowNum; i++){
            checkDataRow = sheet1.getRow(i);
            if(checkDataRow == null) {
                continue;
            }
            //员工编号
            String jobCode = confirmColumn(checkDataRow, 2).trim();
            if (StringUtil.isNull(jobCode)){
                continue;
            }
            TeAbpEmpAllot allot = provEmpMap.get(jobCode);
            if (null == allot){
                continue;
            }
            List<TeAllotInfo> allotInfo = allot.getAllotInfo();
            //区域
            String region = confirmColumn(checkDataRow, 6).trim();
            TeSysDef regionDef = regionMap.get(region);
            if (null != regionDef){
                allot.setRegion(SysDef.converSysDef(regionDef).trans2IdNameCn());
            }
            //省份
            String prov = confirmColumn(checkDataRow, 7).trim();
            TeSysDef provDef = regionMap.get(prov);
            if (null != provDef){
                allot.setProv(SysDef.converSysDef(provDef).trans2IdNameCn());
            }
            //产品线1
            String pl1 = confirmColumn(checkDataRow, 8).trim();
            //产品部1
            String cc1 = confirmColumn(checkDataRow, 9).trim();
            //人数1
            String num1 = confirmColumn(checkDataRow, 10).trim();
            TeSysDef plDef1 = plMap.get(pl1);
            if (null != plDef1){
                TeAllotInfo teAllotInfo = allotInfo.get(0);
                teAllotInfo.setPl(SysDef.converSysDef(plDef1).trans2IdNameCn());

                String[] split = cc1.split("-");
                TeSysAicc teSysAicc = ccMap.get(split[0]);
                if (null != teSysAicc){
                    TeIdNameCn cn = new TeIdNameCn();
                    cn.setCid(teSysAicc.getId());
                    cn.setCodeName(teSysAicc.getCcId());
                    cn.setName(cc1);
                    teAllotInfo.setCc(cn);
                }
                if (StringUtil.isNotNull(num1)){
                    teAllotInfo.setNum(Double.valueOf(num1));
                }
            }

            //产品线2
            String pl2 = confirmColumn(checkDataRow, 11).trim();
            //产品部2
            String cc2 = confirmColumn(checkDataRow, 12).trim();
            //人数2
            String num2 = confirmColumn(checkDataRow, 13).trim();
            TeSysDef plDef2 = plMap.get(pl2);
            if (null != plDef2){
                TeAllotInfo teAllotInfo = allotInfo.get(1);
                teAllotInfo.setPl(SysDef.converSysDef(plDef2).trans2IdNameCn());

                String[] split = cc2.split("-");
                TeSysAicc teSysAicc = ccMap.get(split[0]);
                if (null != teSysAicc){
                    TeIdNameCn cn = new TeIdNameCn();
                    cn.setCid(teSysAicc.getId());
                    cn.setCodeName(teSysAicc.getCcId());
                    cn.setName(cc2);
                    teAllotInfo.setCc(cn);
                }
                if (StringUtil.isNotNull(num2)){
                    teAllotInfo.setNum(Double.valueOf(num2));
                }
            }
            allot.setAllotInfo(allotInfo);
            abpEmpAllotDao.updateById(allot.getId(),allot);
        }

        int rowNum2 = sheet2.getPhysicalNumberOfRows();
        checkDataRow = null;
        for(int i=1; i<rowNum2; i++) {
            checkDataRow = sheet2.getRow(i);
            if (checkDataRow == null) {
                continue;
            }

            //员工编号
            String jobCode = confirmColumn(checkDataRow, 2).trim();
            if (StringUtil.isNull(jobCode)){
                continue;
            }
            TeAbpEmpAllot allot = srdEmpMap.get(jobCode);
            if (null == allot){
                continue;
            }
            List<TeAllotInfo> allotInfo = allot.getAllotInfo();
            //研发
            String r2dNum = confirmColumn(checkDataRow, 7).trim();
            //如果研发数为1，直接跳过
            if ("1".equals(r2dNum)){
                allot.setR2dNum(1D);
                abpEmpAllotDao.updateById(allot.getId(),allot);
                continue;
            } else {
                allot.setR2dNum(Double.valueOf(r2dNum));
            }
            //区域1
            String region1 = confirmColumn(checkDataRow, 8).trim();
            TeSysDef region1Def = regionMap.get(region1);
            if (null != region1Def){
                TeAllotInfo teAllotInfo = allotInfo.get(0);
                teAllotInfo.setRegion(SysDef.converSysDef(region1Def).trans2IdNameCn());
                String prov1 = confirmColumn(checkDataRow, 9).trim();
                String num1 = confirmColumn(checkDataRow, 10).trim();
                TeSysDef provDef1 = regionMap.get(prov1);
                if (null != provDef1){
                    teAllotInfo.setProv(SysDef.converSysDef(provDef1).trans2IdNameCn());
                }
                if (StringUtil.isNotNull(num1)){
                    teAllotInfo.setNum(Double.valueOf(num1));
                }
            }

            //区域1
            String region2 = confirmColumn(checkDataRow, 11).trim();
            TeSysDef region2Def = regionMap.get(region2);
            if (null != region2Def){
                TeAllotInfo teAllotInfo = allotInfo.get(1);
                teAllotInfo.setRegion(SysDef.converSysDef(region2Def).trans2IdNameCn());
                String prov2 = confirmColumn(checkDataRow, 12).trim();
                String num2 = confirmColumn(checkDataRow, 13).trim();
                TeSysDef provDef2 = regionMap.get(prov2);
                if (null != provDef2){
                    teAllotInfo.setProv(SysDef.converSysDef(provDef2).trans2IdNameCn());
                }
                if (StringUtil.isNotNull(num2)){
                    teAllotInfo.setNum(Double.valueOf(num2));
                }
            }
            abpEmpAllotDao.updateById(allot.getId(),allot);
        }
        result.put("result", "success");
        return result;
    }

    @Override
    public Map<String, Object> importEmpRecord(MultipartFile file, String ym,String typeCodeName,TeSysUser sysUser) {
        Map<String, Object> result = new HashMap<>();
        List<AbpEmpAllotVo> allotVoList = queryEmpRecord(sysUser, ym, typeCodeName, null, null);
        Map<String, TeAbpEmpAllot> provEmpMap = new HashMap<>(64);
        for (AbpEmpAllotVo vo : allotVoList) {
            TeAbpEmpAllot allot = new TeAbpEmpAllot();
            BeanUtils.copyProperties(vo,allot);
            provEmpMap.put(allot.getEmp().getJobCode(),allot);
        }

        Map<String, TeSysDef> regionMap = getDefStringMap(CTC_ID,SysDefTypeConstants.DEPT_DEF_CODENAME,null);
        Map<String, TeSysDef> plMap = getDefStringMap(CTC_ID,SysDefTypeConstants.PRD_LINE_DEF_CODENAME,null);
        plMap.putAll(regionMap);
        Map<String, TeSysAicc> ccMap = getCcMap(plMap,false);

        List<SysDef> defs = sysDefService.getSysDefsBySrc(ABP_OTHER_ALLOT_UNIT, SysDefTypeCodeName.SYS_PARA_VALUE);
        Map<String, SysDef> otherAllotUnitMap = defs.stream().collect(Collectors.toMap(SysDef::getDefName,def->def));

        InputStream in = null;
        Workbook wb = null;
        Sheet sheet1 = null;
        try {
            in = file.getInputStream();
            wb = WorkbookFactory.create(in);
            sheet1 = wb.getSheetAt(0);
        }
        catch (IOException excelIOError) {
            result.put("result", "error");
            result.put("msg", "读取文件失败!");
            return result;
        }
        catch (InvalidFormatException excelChangeError) {
            result.put("result", "error");
            result.put("msg", "文件格式错误！");
            return result;
        }
        finally {
            try {
                in.close();
            }
            catch (IOException e) {
                throw new BaseException(" close file IO error ! ");
            }
        }

            int rowNum = sheet1.getPhysicalNumberOfRows();
            Row checkDataRow = null;
            Row headDataRow = sheet1.getRow(0);
            for(int i=1; i<rowNum; i++){
                try {
                checkDataRow = sheet1.getRow(i);
                if(checkDataRow == null) {
                    continue;
                }
                //员工编号
                String jobCode = confirmColumn(checkDataRow, 3).trim();
                if (StringUtil.isNull(jobCode)){
                    continue;
                }
                TeAbpEmpAllot allot = provEmpMap.get(jobCode);
                if (null == allot){
                    continue;
                }
                List<TeAllotInfo> allotInfo = allot.getAllotInfo();
                if (SysDefConstants.PROV_INVENTORY_CN.equals(typeCodeName)){
                    //中心
                    String bigRegion = confirmColumn(checkDataRow, 9).trim();
                    TeSysDef bigRegionDef = regionMap.get(bigRegion);
                    if (null != bigRegionDef){
                        allot.setBigRegion(SysDef.converSysDef(bigRegionDef).trans2IdNameCn());
                    }
                    //区域
                    String region = confirmColumn(checkDataRow, 10).trim();
                    TeSysDef regionDef = regionMap.get(region);
                    if (null != regionDef){
                        allot.setRegion(SysDef.converSysDef(regionDef).trans2IdNameCn());
                    }
                    //省份
                    String prov = confirmColumn(checkDataRow, 11).trim();
                    TeSysDef provDef = regionMap.get(prov);
                    if (null != provDef){
                        allot.setProv(SysDef.converSysDef(provDef).trans2IdNameCn());
                    }
                    //产品线1
                    String pl1 = confirmColumn(checkDataRow, 12).trim();
                    //产品部1
                    String cc1 = confirmColumn(checkDataRow, 13).trim();
                    //人数1
                    String num1 = confirmColumn(checkDataRow, 14).trim();
                    TeSysDef plDef1 = plMap.get(pl1);
                    if (null != plDef1){
                        TeAllotInfo teAllotInfo = allotInfo.get(0);
                        teAllotInfo.setPl(SysDef.converSysDef(plDef1).trans2IdNameCn());

                        String[] split = cc1.split("-");
                        TeSysAicc teSysAicc = ccMap.get(split[0]);
                        if (null != teSysAicc){
                            TeIdNameCn cn = new TeIdNameCn();
                            cn.setCid(teSysAicc.getId());
                            cn.setCodeName(teSysAicc.getCcId());
                            cn.setName(cc1);
                            teAllotInfo.setCc(cn);
                        }
                        if (StringUtil.isNotNull(num1)){
                            teAllotInfo.setNum(Double.valueOf(num1));
                        }
                    } else {
                        TeAllotInfo teAllotInfo = allotInfo.get(0);
                        teAllotInfo.setPl(null);
                        teAllotInfo.setNum(0D);
                    }

                    //产品线2
                    String pl2 = confirmColumn(checkDataRow, 15).trim();
                    //产品部2
                    String cc2 = confirmColumn(checkDataRow, 16).trim();
                    //人数2
                    String num2 = confirmColumn(checkDataRow, 17).trim();
                    TeSysDef plDef2 = plMap.get(pl2);
                    if (null != plDef2){
                        TeAllotInfo teAllotInfo = allotInfo.get(1);
                        teAllotInfo.setPl(SysDef.converSysDef(plDef2).trans2IdNameCn());

                        String[] split = cc2.split("-");
                        TeSysAicc teSysAicc = ccMap.get(split[0]);
                        if (null != teSysAicc){
                            TeIdNameCn cn = new TeIdNameCn();
                            cn.setCid(teSysAicc.getId());
                            cn.setCodeName(teSysAicc.getCcId());
                            cn.setName(cc2);
                            teAllotInfo.setCc(cn);
                        }
                        if (StringUtil.isNotNull(num2)){
                            teAllotInfo.setNum(Double.valueOf(num2));
                        }
                    } else {
                        TeAllotInfo teAllotInfo = allotInfo.get(1);
                        teAllotInfo.setPl(null);
                        teAllotInfo.setNum(0D);
                    }

                    //otherAllotUnit
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,18);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,19);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,20);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,21);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,22);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,23);
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,24);

                    String role = confirmColumn(checkDataRow, 26).trim();
                    String desc = confirmColumn(checkDataRow, 27).trim();
                    allot.setRole(role);
                    allot.setDesc(desc);
                } else {
                    //研发
                    String r2dNum = confirmColumn(checkDataRow, 10).trim();
                    if (StringUtil.isNotNull(r2dNum)){
                        allot.setR2dNum(Double.valueOf(r2dNum));
                    }
                    //区域1
                    String region1 = confirmColumn(checkDataRow, 11).trim();
                    TeSysDef region1Def = regionMap.get(region1);
                    TeAllotInfo teAllotInfo = allotInfo.get(0);
                    if (null != region1Def){
                        teAllotInfo.setRegion(SysDef.converSysDef(region1Def).trans2IdNameCn());
                        String prov1 = confirmColumn(checkDataRow, 12).trim();
                        TeSysDef provDef1 = regionMap.get(prov1);
                        if (null != provDef1){
                            teAllotInfo.setProv(SysDef.converSysDef(provDef1).trans2IdNameCn());
                        }

                    } else {
                        teAllotInfo.setRegion(null);
                        teAllotInfo.setProv(null);
                    }
                    String num1 = confirmColumn(checkDataRow, 13).trim();
                    if (StringUtil.isNotNull(num1)){
                        teAllotInfo.setNum(Double.valueOf(num1));
                    }
                    //区域1
                    String region2 = confirmColumn(checkDataRow, 14).trim();
                    TeSysDef region2Def = regionMap.get(region2);
                    teAllotInfo = allotInfo.get(1);
                    if (null != region2Def){
                        teAllotInfo.setRegion(SysDef.converSysDef(region2Def).trans2IdNameCn());
                        String prov2 = confirmColumn(checkDataRow, 15).trim();
                        TeSysDef provDef2 = regionMap.get(prov2);
                        if (null != provDef2){
                            teAllotInfo.setProv(SysDef.converSysDef(provDef2).trans2IdNameCn());
                        }
                    } else {
                        teAllotInfo.setRegion(null);
                        teAllotInfo.setProv(null);
                    }
                    String num2 = confirmColumn(checkDataRow, 16).trim();
                    if (StringUtil.isNotNull(num2)){
                        teAllotInfo.setNum(Double.valueOf(num2));
                    }
                    //otherAllotUnit
                    setAllotInfo(otherAllotUnitMap, checkDataRow, headDataRow, allotInfo,17);

                    String role = confirmColumn(checkDataRow, 18).trim();
                    String desc = confirmColumn(checkDataRow, 19).trim();
                    allot.setRole(role);
                    allot.setDesc(desc);
                }


                if (SysDefConstants.IS_INITIATED_DEF_ID.equals(allot.getStatus().getCid())){
                    SysDef isEditing = sysDefService.getSysDefById(SysDefConstants.IS_EDITING_DEF_ID);
                    allot.setStatus(isEditing.trans2IdNameCn());
                    allot.setAddUser(sysUser.trans2User());
                    allot.setAddTime(new Date());
                }
                TeIdNameCn region = allot.getRegion();
                if (null != region){
                    SysDef regionDef = sysDefService.getSysDefById(region.getCid());
                    SysDef bigRegionDef = sysDefService.getSysDefById(regionDef.getParentDefId());
                    allot.setBigRegion(bigRegionDef.trans2IdNameCn());
                }
                allot.setAllotInfo(allotInfo);
                abpEmpAllotDao.updateById(allot.getId(),allot,true);
                } catch (Exception e){
                    result.put("result", "error");
                    result.put("msg", "第"+i+"行格式错误");
                    return result;
                }
            }

        result.put("result", "success");
        return result;
    }

    private void setAllotInfo(Map<String, SysDef> otherAllotUnitMap, Row checkDataRow, Row headDataRow, List<TeAllotInfo> allotInfo,int index) {
        String name = confirmColumn(headDataRow, index).trim();
        String num3 = confirmColumn(checkDataRow, index).trim();
        SysDef sysDef = otherAllotUnitMap.get(name);
        if (null == sysDef){
            return;
        }
        TeIdNameCn idNameCn = sysDef.trans2IdNameCn();
        boolean flag = false;
        for (TeAllotInfo teAllotInfo : allotInfo) {
            if (null != teAllotInfo.getOtherAllotUnit()
                    && teAllotInfo.getOtherAllotUnit().getCid().equals(idNameCn.getCid())) {
                flag = true;
                if (StringUtil.isNotNull(num3)){
                    teAllotInfo.setNum(Double.valueOf(num3));
                } else {
                    teAllotInfo.setNum(0D);
                }
            }
        }
        if (!flag){
            TeAllotInfo info = new TeAllotInfo();
            info.setOtherAllotUnit(idNameCn);
            if (StringUtil.isNotNull(num3)){
                info.setNum(Double.valueOf(num3));
            } else {
                info.setNum(0D);
            }
            allotInfo.add(info);
        }
    }

    private Map<String, TeSysAicc> getCcMap(Map<String, TeSysDef> plMap,boolean isCcId) {
        Map<String, TeSysAicc> ccMap = new HashMap<>(32);

        List<String> ccList = new ArrayList<>();
        for (String defName : plMap.keySet()) {
            TeSysDef teSysDef = plMap.get(defName);
            List<String> list = StringUtil.transIds2List(teSysDef.getCcList(), ",", String.class);
            if (CollectionUtils.isNotEmpty(list)){
                ccList.addAll(list);
            }
        }
        List<TeSysAicc> teSysAiccs = queryCcByCcList(null, ccList);
        if (isCcId){
            for (TeSysAicc teSysAicc : teSysAiccs) {
                ccMap.put(teSysAicc.getCcId(),teSysAicc);
            }
        } else {
            for (TeSysAicc teSysAicc : teSysAiccs) {
                ccMap.put(teSysAicc.getCcNameCn(),teSysAicc);
            }
        }
        return ccMap;
    }

    @Override
    public List<EmpTrackVo> empTrack(String ym, String typeCodeName,String statusCodeName, TeSysUser loginUser) {
        List<EmpTrackVo> list = new ArrayList<>();
        Map<String,EmpTrackVo> map = new HashMap<>();
        SysDef def = sysDefService.getSysDefByCodeName(loginUser.getSbuId(), SysDefTypeCodeName.AI_BU);
        TeSysDefRoleUser sysDefRoleUser = new TeSysDefRoleUser();
        sysDefRoleUser.setDefId(def.getId());
        List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
        TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
        role.setRoleCodeName(SysDefConstants.BUOMADMIN_CODENAME);
        roles.add(role);
        sysDefRoleUser.setRole(roles);
        TeSysDefRoleUser2User roleUser = new TeSysDefRoleUser2User();
        roleUser.setLoginName(loginUser.getLoginName());
        sysDefRoleUser.setRoleUser(roleUser);
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.querySysUserDistinctColl(sysDefRoleUser);

        Map<String, TeSysDef> deptMap = getDefMap(CTC_ID,SysDefTypeConstants.DEPT_DEF_CODENAME,null);
        Map<String, TeSysDef> prdMap = getDefMap(CTC_ID,SysDefTypeConstants.PRD_LINE_DEF_CODENAME,null);

        Map<String, TeSysDef> regionMap = getDefStringMap(CTC_ID,SysDefTypeConstants.DEPT_DEF_CODENAME,null);
        Map<String, TeSysDef> plMap = getDefStringMap(loginUser.getSbuId(),SysDefTypeConstants.PRD_LINE_DEF_CODENAME,null);
        plMap.putAll(regionMap);
        Map<String, TeSysAicc> ccMap = getCcMap(plMap,true);

        List<IDbCondition> conds = new ArrayList<>();
        getConds(loginUser.getSbuId(),ym,typeCodeName,conds);
        if (StringUtil.isNotNull(statusCodeName)){
            conds.add(new DC_E(DFN.flowTask_status.dot(DFN.common_cn), statusCodeName));
        }
        //有BU权限
        if (!roleUsers.isEmpty()){
            List<TeAbpEmpAllot> teAbpEmpAllots = abpEmpAllotDao.findByConds(conds, null);
            setEmpNumMap(map, ccMap, teAbpEmpAllots,deptMap,prdMap);
        } else {
            //如果没有BU级权限，再取出其它所有权限下的CC清单，先取出角色下的CC清单
            sysDefRoleUser = new TeSysDefRoleUser();
            sysDefRoleUser.setRole(roles);
            sysDefRoleUser.setRoleUser(roleUser);
            List<TeSysDefRoleUser> roleUserList = sysDefRoleUserDao.querySysUserDistinctColl(sysDefRoleUser);
            if (!roleUserList.isEmpty()){
                List<String> ccList = new ArrayList<>();
                List<ObjectId> defIds = new ArrayList<>();
                for (TeSysDefRoleUser teSysDefRoleUser : roleUserList) {
                    defIds.add(teSysDefRoleUser.getDefId());
                }
                List<TeSysDef> teSysDefs = sysDefDao.getSysDefsByIds(defIds);
                for (TeSysDef teSysDef : teSysDefs) {
                    List<String> cc = StringUtil.transIds2List(teSysDef.getCcList(), ",", String.class);
                    if (null != cc){
                        ccList.addAll(cc);
                    }
                }
                conds.add(new DC_I<>(DFN.sysUser_ccId, ccList));
                List<TeAbpEmpAllot> teAbpEmpAllots = abpEmpAllotDao.findByConds(conds, null);
                setEmpNumMap(map, ccMap, teAbpEmpAllots,deptMap,prdMap);
            }
        }
        for (String ccId : map.keySet()) {
            EmpTrackVo empTrackVo = map.get(ccId);
            list.add(empTrackVo);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> exportEmpTrack(String ym, String typeCodeName, String statusCodeName, TeSysUser loginUser) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        List<EmpTrackVo> list = empTrack(ym, typeCodeName, statusCodeName, loginUser);
        int no = 0;
        for (EmpTrackVo vo : list) {
            no++;
            Map<String, Object> map = BeanMapUtils.beanToMap(vo);
            map.put("no",no);
            if (null != vo.getCcOwner()){
                map.put("ccOwner",vo.getCcOwner().getUserName()+"/"+vo.getCcOwner().getLoginName());
            }
            dataMap.add(map);
        }
        return dataMap;
    }

    private void setEmpNumMap(Map<String, EmpTrackVo> map, Map<String, TeSysAicc> ccMap
            , List<TeAbpEmpAllot> teAbpEmpAllots,Map<String, TeSysDef> deptMap,Map<String, TeSysDef> prdMap) {
        for (TeAbpEmpAllot teAbpEmpAllot : teAbpEmpAllots) {
            TeIdNameCn status = teAbpEmpAllot.getStatus();
            String ccId = teAbpEmpAllot.getCcId();
            EmpTrackVo vo = map.get(ccId);
            if (null == vo){
                vo = new EmpTrackVo();
                vo.setCcId(ccId);
                if (null != deptMap.get(ccId)){
                    TeSysDef sysDef = deptMap.get(ccId);
                    vo.setAllotInfoName(sysDef.getDefType().getDefTypeName());
                    vo.setCcName(sysDef.getDefName());
                } else if (null != prdMap.get(ccId)){
                    TeSysDef sysDef = prdMap.get(ccId);
                    vo.setAllotInfoName(sysDef.getDefType().getDefTypeName());
                    vo.setCcName(sysDef.getDefName());
                }
                TeSysAicc teSysAicc = ccMap.get(ccId);
                if (null != teSysAicc){
                    vo.setCcOwner(teSysAicc.getCcOwner());
                }
                /*if (SysDefConstants.PROV_INVENTORY_CN.equals(teAbpEmpAllot.getType().getCodeName())){
                    StringBuilder sb = new StringBuilder();
                    TeIdNameCn prov = teAbpEmpAllot.getProv();
                    if (null != prov){
                        sb.append(prov.getName());
                        TeIdNameCn region = teAbpEmpAllot.getRegion();
                        if (null != region){
                            sb.append("/").append(region.getName());
                        }
                    } else {
                        TeIdNameCn region = teAbpEmpAllot.getRegion();
                        if (null != region){
                            sb.append(region.getName());
                        }
                    }
                    vo.setAllotInfoName(sb.toString());
                } else {
                    TeIdNameCn pl = teAbpEmpAllot.getPl();
                    if (null != pl){
                        vo.setAllotInfoName(pl.getName());
                    }
                }*/
                setStatusNum(vo, status);
                map.put(ccId,vo);
            } else {
                setStatusNum(vo, status);
                map.put(ccId,vo);
            }
        }
    }

    private void setStatusNum(EmpTrackVo vo, TeIdNameCn status) {
        if (IS_INITIATED.equals(status.getCodeName())){
            vo.setIsInitiated(vo.getIsInitiated()+1);
        } else if (IS_EDITING.equals(status.getCodeName())){
            vo.setIsEditing(vo.getIsEditing()+1);
        } else if (IS_PENDING.equals(status.getCodeName())){
            vo.setIsPending(vo.getIsPending()+1);
        } else if (IS_APPROVED.equals(status.getCodeName())){
            vo.setIsApproved(vo.getIsApproved()+1);
        } else {
            vo.setIsFailed(vo.getIsFailed()+1);
        }
        if (vo.getIsInitiated() > 0){
            vo.setStatusName("待盘点");
            return;
        } else if(vo.getIsEditing() > 0){
            vo.setStatusName("盘点中");
            return;
        } else if(vo.getIsFailed() > 0) {
            vo.setStatusName("已驳回");
            return;
        } else if(vo.getIsPending() > 0){
            vo.setStatusName("待审批");
            return;
        } else {
            vo.setStatusName("已审批");
        }
    }

    private List<TeSysDefRoleUser> getTeSysDefRoleUsers(ObjectId id) {
        TeSysDefRoleUser roleUser = new TeSysDefRoleUser();
        roleUser.setDefId(id);
        List<TeSysDefRoleUser2Role> roles = new ArrayList<>();
        TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
        role.setRoleCodeName(SysDefConstants.BUOMADMIN_CODENAME);
        roleUser.setRole(roles);
        return sysDefRoleUserDao.querySysUserDistinctColl(roleUser);
    }

    private void getSstConds(String ym, String sbuId, List<IDbCondition> conds, List<String> ccList,Boolean isEmployeeTag) {
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.omsOrder__sbu.dot(DFN.common_cn), sbuId));
        conds.add(new DC_E(DFN.prjMonthDoing_ym, ym));
        if (isEmployeeTag){
            conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_FORMAL));
        } else {
            conds.add(new DC_I<>(DFN.sysUser_employeeType,SysDefConstants.EMPLOYEE_TYPE_PRACTICE));
        }
        conds.add(new DC_I<>(DFN.sysUser_ccId, ccList));
    }

    private void getDefConds(String sbuId, List<IDbCondition> conds, String functionalDeptDefCodename) {
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), sbuId));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeConstants.DEPT_DEF_CODENAME));
        conds.add(new DC_E(DFN.sysDef__codeName, functionalDeptDefCodename));
    }

    private void getNumMap(Map<String, Double> map, List<TeAllotInfo> allotInfo, TeIdNameCn prov,boolean isRegion) {
        if (null != prov) {
            Double provNum = map.get(prov.getName());
            Double count = 0D;
            for (TeAllotInfo teAllotInfo : allotInfo) {
                if (null != teAllotInfo && null != teAllotInfo.getNum() && null == teAllotInfo.getOtherAllotUnit()) {
                    count += teAllotInfo.getNum();
                }
            }
            if (null != provNum) {
                double result = addDouble(provNum, count);
                map.put(prov.getName(), result);
            } else {
                map.put(prov.getName(), count);
            }
            if (isRegion){
                if (null != map.get("regionTotal")){
                    Double total = addDouble(map.get("regionTotal"), count);
                    map.put("regionTotal", total);
                } else {
                    map.put("regionTotal",count);
                }
            }
        }
    }

    private void getNumMap(Map<String, Double> map, List<TeAllotInfo> allotInfo, TeIdNameCn prov,SysDef sysDef,boolean isRegion) {
        if (null != prov) {
            Double provNum = map.get(prov.getName());
            Double count = 0D;
            for (TeAllotInfo teAllotInfo : allotInfo) {
                if (null != teAllotInfo && null != teAllotInfo.getNum() && null != teAllotInfo.getOtherAllotUnit()
                        && sysDef.getId().equals(teAllotInfo.getOtherAllotUnit().getCid())) {
                    count += teAllotInfo.getNum();
                }
            }
            if (null != provNum) {
                double result = addDouble(provNum, count);
                map.put(prov.getName(), result);
            } else {
                map.put(prov.getName(), count);
            }
            if (isRegion){
                if (null != map.get("regionTotal")){
                    Double total = addDouble(map.get("regionTotal"), count);
                    map.put("regionTotal", total);
                } else {
                    map.put("regionTotal",count);
                }
            }
        }
    }

    private void getNumMap(Map<String, Double> map, TeAllotInfo allotInfo, TeIdNameCn prov,boolean isRegion) {
        if (null != prov) {
            Double provNum = map.get(prov.getName());
            Double count = allotInfo.getNum();
            if (null != provNum) {
                double result = addDouble(provNum, count);
                map.put(prov.getName(), result);
            } else {
                map.put(prov.getName(), count);
            }
            if (isRegion){
                if (null != map.get("regionTotal")){
                    Double total = addDouble(map.get("regionTotal"), count);
                    map.put("regionTotal", total);
                } else {
                    map.put("regionTotal",count);
                }
            }
        }
    }

    private List<Map<String, Object>> convert(List<AbpEmpAllotVo> dataList) {
        List<Map<String, Object>> dataMap = new ArrayList<>();
        int no = 0;
        for (AbpEmpAllotVo vo : dataList) {
            no++;
            Map<String, Object> map = BeanMapUtils.beanToMap(vo);
            map.put("no",no);
            if (null != vo.getEmp()){
                map.put("userName",vo.getEmp().getUserName());
                map.put("jobCode",vo.getEmp().getJobCode());
            }
            if (null != vo.getCcOwner()){
                map.put("ccOwner",vo.getCcOwner().getUserName());
            }
            if (null != vo.getBigRegion()){
                map.put("ccName",vo.getBigRegion().getName());
            }
            if (null != vo.getManager()){
                map.put("managerUserName",vo.getManager().getUserName());
                map.put("managerJobCode",vo.getManager().getJobCode());
            }
            if (null != vo.getProv()){
                map.put("prov",vo.getProv().getName());
            }
            if (null != vo.getRegion()){
                map.put("region",vo.getRegion().getName());
            }
            if (null != vo.getPl()){
                map.put("pl",vo.getPl().getName());
            }
            if (vo.getLocked()){
                map.put("locked","已盘点");
            } else {
                map.put("locked","待盘点");
            }
            List<TeAllotInfo> allotInfo = vo.getAllotInfo();
            for (int i=0;i<allotInfo.size();i++){
                TeAllotInfo teAllotInfo = allotInfo.get(i);
                if (null != teAllotInfo.getPl() && null != teAllotInfo.getPl().getName()){
                    map.put("pl"+(i+1),teAllotInfo.getPl().getName());
                    map.put("num"+(i+1),teAllotInfo.getNum());
                }
                if (null != teAllotInfo.getCc() && null != teAllotInfo.getCc().getName()){
                    map.put("cc"+(i+1),teAllotInfo.getCc().getName());
                    map.put("num"+(i+1),teAllotInfo.getNum());
                }
                if (null != teAllotInfo.getRegion() && null != teAllotInfo.getRegion().getName()){
                    map.put("region"+(i+1),teAllotInfo.getRegion().getName());
                    map.put("num"+(i+1),teAllotInfo.getNum());
                }
                if (null != teAllotInfo.getProv() && null != teAllotInfo.getProv().getName()){
                    map.put("prov"+(i+1),teAllotInfo.getProv().getName());
                    map.put("num"+(i+1),teAllotInfo.getNum());
                }
                TeIdNameCn otherAllotUnit = teAllotInfo.getOtherAllotUnit();
                if (null != otherAllotUnit){
                    map.put(otherAllotUnit.getCid().toHexString(),teAllotInfo.getNum());
                }
            }
            dataMap.add(map);
        }
        return dataMap;
    }

    private Map<String, TeSysDef> getDefMap(String sbuId,String defTypeCodeName,String codeName) {
        Map<String, TeSysDef> ccMap = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), sbuId));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCodeName));
        conds.add(new DC_E(DFN.sysDef__ccList, null,true));
        if (StringUtil.isNotNull(codeName)){
            conds.add(new DC_E(DFN.sysDef__codeName, codeName));
        }
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
        for (TeSysDef sysDef : sysDefs) {
            List<String> cclist = StringUtil.transIds2List(sysDef.getCcList(), ",", String.class);
            if (CollectionUtils.isNotEmpty(cclist)){
                for (String ccId : cclist) {
                    ccMap.put(ccId,sysDef);
                }
            }
        }
        return ccMap;
    }

    private Map<String, TeSysDef> getDefStringMap(String sbuId,String defTypeCodeName,String codeName) {
        Map<String, TeSysDef> ccMap = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), sbuId));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), defTypeCodeName));
        conds.add(new DC_E(DFN.sysDef__ccList, null,true));
        if (StringUtil.isNotNull(codeName)){
            conds.add(new DC_E(DFN.sysDef__codeName, codeName));
        }
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
        for (TeSysDef sysDef : sysDefs) {
            ccMap.put(sysDef.getDefName(),sysDef);
        }
        return ccMap;
    }

    private void getConds(String sbuId, String ym, String typeCodeName,List<IDbCondition> conds) {
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.prjInfo__ym, ym));
        conds.add(new DC_E(DFN.prjHealth_sbu.dot(DFN.common_cn), sbuId));
        if (StringUtil.isNotNull(typeCodeName)){
            conds.add(new DC_E(DFN.prjInfo__type.dot(DFN.common_cn), typeCodeName));
        }
    }

    private double addDouble(double m1, double m2) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        return p1.add(p2).doubleValue();
    }

    private String confirmColumn(Row dataRow, Integer index) {
        String value = "";
        if (index != null) {
            value = ExcelUtils.getCellFormatValueWithDateTimeFormat(dataRow.getCell(index));
            if(StringUtil.isNotNull(value)) {
                value = value.trim();
            }
        }
        return value;
    }
}
