package com.linkus.sysuser.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.analysis.ExcelReadExecutor;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.linkus.base.constants.CommonConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.BatchCondsUpsert;
import com.linkus.base.db.base.DbJoin;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.DbLikeCondition;
import com.linkus.base.db.base.condition.impl.DbOrOperator;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.db.mongo.model.bo.MongoCountBo;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.shiro.LoginUserHolder;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeDefType;
import com.linkus.sys.model.po.TeSrcDef;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.bo.AnswerAnalysisReportInfoBo;
import com.linkus.sysuser.bo.AnswerAnalysisReportResultBo;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.dao.ISysUserAgentDao;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.dao.impl.SysAiccDaoImpl;
import com.linkus.sysuser.dto.QuestionParam;
import com.linkus.sysuser.model.*;
import com.linkus.sysuser.service.IOnlineExamService;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.sysuser.util.CellStyleHandler;
import com.linkus.sysuser.util.ExcelListener;
import com.linkus.sysuser.util.ReportUtil;
import com.linkus.sysuser.vo.*;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service("OnlineExamServiceImpl")
public class OnlineExamServiceImpl implements IOnlineExamService {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private SysDefDao sysDefDao;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysUserDao sysUserDao;
    @Resource
    private SysAiccDaoImpl sysAiccDao;
    @Resource
    private ISysUserAgentDao sysUserAgentDao;
    @Autowired
    private ISysDefRoleUserDao sysDefRoleUserDao;

    private static Logger logger = LoggerFactory.getLogger(OnlineExamServiceImpl.class);
    public static final List<String> EXAM_OPTION_CODES = Arrays.asList(SysDefConstants.EXAM_QUEST_OPTION_A, SysDefConstants.EXAM_QUEST_OPTION_B, SysDefConstants.EXAM_QUEST_OPTION_C,
            SysDefConstants.EXAM_QUEST_OPTION_D);

    @Override
    public PageBean getQuestionBankMgt(ExamParamVo examParam) {
        PageBean pageBean = new PageBean();
        TeSysUser loginUser = this.getLoginUser();
        if (null == loginUser) {
            return pageBean;
        }
        Pager pager = examParam.getPager();
        List<IDbCondition> conds = new ArrayList<>();
        if (StringUtil.isNotNull(examParam.getName())) {
            conds.add(new DC_L(DFN.sysDef__defName, examParam.getName()));
        }
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        String statusName = examParam.getStatusName();
        if (StringUtil.isNotNull(statusName)) {
            boolean flag = "未启用".equals(statusName) ? true : false;
            conds.add(new DC_E(DFN.sysDef_isInvisible, flag));
        }
        if (StringUtil.isNotNull(examParam.getAddTimeStart()) && StringUtil.isNotNull(examParam.getAddTimeEnd())) {
            List<IDbCondition> andConds = new ArrayList<>();
            andConds.add(new DC_GT(DFN.sysDef__addTime, DateUtil.parseDate(examParam.getAddTimeStart() + DateUtil.DAY_START), true));
            andConds.add(new DC_LT(DFN.sysDef__addTime, DateUtil.parseDate(examParam.getAddTimeEnd() + DateUtil.DAY_DEADLINE), true));
            conds.add(new DC_AO(andConds));
        }
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST_BANK));
        conds.add(new DC_E(DFN.common_addUser.dot(DFN.common_userId), loginUser.getId()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef__id);
        fieldNames.add(DbFieldName.sysDef__defName);
        fieldNames.add(DbFieldName.sysDef__codeName);
        fieldNames.add(DbFieldName.sysDef__defNo);
        fieldNames.add(DbFieldName.sysDef_cndtItems);
        fieldNames.add(DbFieldName.sysDef__addTime);
        fieldNames.add(DbFieldName.common_addUser);
        fieldNames.add(DFN.sysDef_isInvisible);
        // 根据addUser来取cc
        List<DbJoin> joins = new ArrayList<DbJoin>();
        List<IDbCondition> bgtConds = new ArrayList<>();
        if (StringUtil.isNotNull(examParam.getCcId())) {
            List<IDbCondition> orCondsForCodeName = new ArrayList<>();
            orCondsForCodeName.add(new DC_L(DbFieldName.join_sysUser.dot(DbFieldName.sysUser_ccId), examParam.getCcId()));
            orCondsForCodeName.add(new DC_L(DbFieldName.join_sysUser.dot(DbFieldName.sysUser_ccNameCn), examParam.getCcId()));
            bgtConds.add(new DC_OR(orCondsForCodeName));
        }
        // 关联条件
        joins.add(new DbJoin(DBT.SYS_USER, DbFieldName.sysUser_id, DFN.common_addUser.dot(DbFieldName.common_userId), bgtConds,
                DbFieldName.join_sysUser.n(), false));

        Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
        long count = sysDefDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count < 1) {
            return pageBean;
        }
        List<TeSysDef> questionBanks = sysDefDao.queryMultJoin(conds, joins, fieldNames, sort, pager, TeSysDef.class);
        List<ObjectId> addUserIdList = new ArrayList<>();
        List<ObjectId> questionBankIds = new ArrayList<>();
        for (TeSysDef questionBank : questionBanks) {
            TeUser addUser = questionBank.getAddUser();
            ObjectId userId = addUser.getUserId();
            addUserIdList.add(userId);
            questionBankIds.add(questionBank.getId());
        }
        conds.clear();
        fieldNames.clear();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_I<>(DFN.sysUser_id, addUserIdList));
        fieldNames.add(DFN.sysUser_id);
        fieldNames.add(DFN.sysUser_ccId);
        fieldNames.add(DFN.sysUser_ccName);
        fieldNames.add(DFN.sysUser_ccNameCn);
        List<TeSysUser> teSysUserList = sysUserDao.findByFieldAndConds(conds, fieldNames, null, null);
        if (CollectionUtils.isEmpty(teSysUserList)) {
            return pageBean;
        }
        conds.clear();
        fieldNames.clear();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST));
        conds.add(new DC_I<ObjectId>(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), questionBankIds));
        fieldNames.add(DFN.sysDef__srcDef);
        List<TeSysDef> examQuests = sysDefDao.findByFieldAndConds(conds, fieldNames);
        Map<ObjectId, Integer> questionBank2questNum = new HashMap<>();
        for (TeSysDef examQuest : examQuests) {
            TeSrcDef srcDef = examQuest.getSrcDef();
            if (StringUtil.isNotNull(srcDef) && StringUtil.isNotNull(srcDef.getSrcDefId())) {
                ObjectId srcDefId = srcDef.getSrcDefId();
                if (questionBank2questNum.get(srcDefId) == null) {
                    questionBank2questNum.put(srcDefId, 1);
                } else {
                    questionBank2questNum.put(srcDefId, 1 + questionBank2questNum.get(srcDefId));
                }
            }
        }
        Map<ObjectId, TeSysUser> ccInfoMap = new HashMap<>();
        for (TeSysUser teSysUser : teSysUserList) {
            ccInfoMap.put(teSysUser.getId(), teSysUser);
        }
        List<QuestionBankVo> bankVos = new ArrayList<>();
        for (TeSysDef sysDef : questionBanks) {
            QuestionBankVo vo = new QuestionBankVo();
            ObjectId sysDefId = sysDef.getId();
            vo.setId(sysDefId);
            if (questionBank2questNum.get(sysDefId) != null) {
                vo.setQuestNum(questionBank2questNum.get(sysDefId));
            } else {
                vo.setQuestNum(0);
            }
            vo.setName(sysDef.getDefName());
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            Boolean isInvisible = sysDef.getIsInvisible();
            String voStatusName = "";
            if (isInvisible == null || isInvisible) {
                voStatusName = "未启用";
            } else {
                voStatusName = "启用";
            }
            vo.setStatusName(voStatusName);
            Date addTime = sysDef.getAddTime();
            vo.setAddTime(DateUtil.formatDate2Str(addTime, DateUtil.DATE_FORMAT));
            if (!CollectionUtils.isEmpty(cndtItems)) {
                List<String> roleNames = new ArrayList<>();//cndtItems.stream().map(TeIdNameCn::getName).collect(Collectors.toList());
                List<ObjectId> roleIds = new ArrayList<>();
                for (TeIdNameCn teIdNameCn : cndtItems) {
                    roleNames.add(teIdNameCn.getName());
                    roleIds.add(teIdNameCn.getCid());
                }
                vo.setRole(String.join(",", roleNames));
                vo.setRoleIds(roleIds);
                vo.setRoles(cndtItems);
                vo.setCcId(ccInfoMap.get(sysDef.getAddUser().getUserId()).getCcId());
                vo.setCcName(ccInfoMap.get(sysDef.getAddUser().getUserId()).getCcName());
                vo.setCcNameCn(ccInfoMap.get(sysDef.getAddUser().getUserId()).getCcNameCn());
            }
            bankVos.add(vo);
        }
        pageBean.setObjectList(bankVos);
        return pageBean;
    }

    @Override
    public PageBean getQuestionMgt(ExamParamVo examParam) {
        PageBean pageBean = new PageBean();
        Map<String, Object> sqlAndcountSql = this.getSqlAndCountSql(examParam);
        List<org.bson.Document> sql = StringUtil.to(sqlAndcountSql.get("sql"), List.class);
        List<org.bson.Document> countSql = StringUtil.to(sqlAndcountSql.get("countSql"), List.class);
        com.mongodb.client.MongoCursor<org.bson.Document> countOutPut = this.mongoTemplate.getCollection("sysDef").aggregate(countSql)
                .cursor();
        Integer total = 0;
        if (null != countOutPut) {
            while (countOutPut.hasNext()) {
                org.bson.Document countObject = countOutPut.next();
                total = StringUtil.toInteger(countObject.get("count"));
            }
        }
        pageBean.setCount(total);
        if (total > 0) {
            String collectionName = mongoTemplate.getCollectionName(TeSysDef.class);
            com.mongodb.client.MongoCollection<org.bson.Document> applyCollection = mongoTemplate.getCollection(collectionName);
            com.mongodb.client.MongoCursor<org.bson.Document> cursor = applyCollection.aggregate(sql).allowDiskUse(true).cursor();
            List<QuestionVo> questionVos = this.getQuestionVos(cursor);
            pageBean.setObjectList(questionVos);
        }
        return pageBean;
    }

    private Map<String, Object> getSqlAndCountSql(ExamParamVo examParam) {
        Map<String, Object> sqlAndcountSql = new HashMap<String, Object>();
        List<org.bson.Document> sql = new ArrayList<org.bson.Document>();
        List<org.bson.Document> countSql = new ArrayList<org.bson.Document>();
        org.bson.Document mainConds = new org.bson.Document();
        mainConds.put(DFN.sysDef__isValid.n(), true);
        mainConds.put(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId).n(), SysDefConstants.DEF_ID_EXAM_QUEST);
        if (StringUtil.isNotNull(examParam.getId())) {//题库下的题目
            mainConds.put(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n(), examParam.getId());
        }
        if (StringUtil.isNotNull(examParam.getName())) {
            mainConds.put(DFN.sysDef__defName.n(), new org.bson.Document("$regex", examParam.getName()));
        }
        String joinAlias = DFN.join_sysDef.n();
        org.bson.Document lookUp = new org.bson.Document()
                .append("from", DBT.SYS_DEF.n())
                .append("localField", DFN.sysDef_id.n())
                .append("foreignField", DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n())
                .append("as", joinAlias);
        org.bson.Document optionConds = new org.bson.Document();
        optionConds.put(DFN.join_sysDef.dot(DFN.sysDef__isValid).n(), true);
        optionConds.put(DFN.join_sysDef.dot(DFN.sysDef__defType).dot(DFN.sysDef__defTypeId).n(), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION);

        sql.add(new org.bson.Document("$match", mainConds));
        sql.add(new org.bson.Document("$lookup", lookUp));
        sql.add(new org.bson.Document("$match", optionConds));
        sql.add(new org.bson.Document("$sort", new org.bson.Document(DFN.sysUserPfm_addTime.n(), -1)));//按时间降序
        Pager pager = examParam.getPager();
        if (null != pager) {
            org.bson.Document skip = new org.bson.Document("$skip", pager.getIndex() * pager.getSize());
            sql.add(skip);
            org.bson.Document limit = new org.bson.Document("$limit", pager.getSize());
            sql.add(limit);
        }
        countSql.add(new org.bson.Document("$match", mainConds));
        countSql.add(new org.bson.Document("$lookup", lookUp));
        countSql.add(new org.bson.Document("$match", optionConds));
        countSql.add(new org.bson.Document("$sort", new org.bson.Document(DFN.sysUserPfm_addTime.n(), -1)));
        org.bson.Document groupContent = new org.bson.Document();
        groupContent.append("_id", null);
        groupContent.append("count", new org.bson.Document("$sum", 1));
        countSql.add(new org.bson.Document("$group", groupContent));
        sqlAndcountSql.put("sql", sql);
        sqlAndcountSql.put("countSql", countSql);
        return sqlAndcountSql;
    }


    private List<QuestionVo> getQuestionVos(com.mongodb.client.MongoCursor<org.bson.Document> cursor) {
        List<QuestionVo> questionVos = new ArrayList<>();
        if (null != cursor) {
            while (cursor.hasNext()) {
                org.bson.Document output = cursor.next();
                QuestionVo questionVo = new QuestionVo();
                ObjectId questionId = StringUtil.to(output.get(DFN.sysDef_id.n()), ObjectId.class);
                questionVo.setId(questionId);
                questionVo.setName(StringUtil.to(output.get(DFN.sysDef__defName.n()), String.class));
                questionVo.setAnswer(StringUtil.to(output.get(DFN.sysDef__codeName.n()), String.class));
                questionVo.setQuestionBankName(getStringValue(output.get(DFN.sysDef__srcDef.n())));
                Date date = output.get(DFN.sysDef__addTime.n(), Date.class);
                questionVo.setAddTime(DateUtil.formatDate2Str(date, DateUtil.DATE_FORMAT));
                if (StringUtil.isNotNull(output.get(DFN.join_sysDef.n()))) {
                    List<Map<String, Object>> joinSysDefs = (List<Map<String, Object>>) output.get(DFN.join_sysDef.n());
                    if (!CollectionUtils.isEmpty(joinSysDefs)) {
                        for (Map<String, Object> joinSysDef : joinSysDefs) {
                            String option = StringUtil.to(joinSysDef.get(DFN.sysDef__codeName.n()), String.class);
                            if (SysDefConstants.EXAM_QUEST_OPTION_A.equals(option)) {
                                questionVo.setOptionAcontent(StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_B.equals(option)) {
                                questionVo.setOptionBcontent(StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_C.equals(option)) {
                                questionVo.setOptionCcontent(StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_D.equals(option)) {
                                questionVo.setOptionDcontent(StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                        }
                    }
                }
                questionVo.setAuthor(ReportUtil.custFieldFormatValue(output.get(DFN.common_addUser.n())));
                questionVos.add(questionVo);
            }
        }

        return questionVos;
    }

    private String getStringValue(Object obj) {
        if (obj instanceof TeSrcDef) {
            return ((TeSrcDef) obj).getSrcDefName();
        } else if (obj instanceof Map) {
            Map<?, ?> dbO = (Map<?, ?>) obj;
            if (dbO.containsKey("srcDefName")) {
                return dbO.get("srcDefName").toString();
            }
        }
        return "";
    }

    @Override
    public void addQuestionBank(String questionBankName, List<ObjectId> roleIds, Boolean status) {
        Assert.notNull(questionBankName, "题库名称不能为空！");
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new BaseException("角色人员为空！");
        }
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        conds.add(new DC_I(DFN.sysDef_id, roleIds));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), SysDefConstants.DEF_CODE_EXAM_ROLE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef__id);
        fieldNames.add(DbFieldName.sysDef__defName);
        fieldNames.add(DbFieldName.sysDef__codeName);
        List<TeSysDef> cndtItems = sysDefDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(cndtItems) || cndtItems.size() != roleIds.size()) {
            throw new BaseException("角色人员不存在！");
        }
        TeSysDef sysDef = new TeSysDef();
        TeDefType defType = new TeDefType();
        defType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST_BANK);
        defType.setDefTypeName(SysDefConstants.EXAM_QUEST_BANK_NAME);
        defType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_BANK_CODE_NAME);
        sysDef.setDefType(defType);
        sysDef.setIsStd(true);
        sysDef.setIsValid(true);
        sysDef.setDefName(questionBankName);
        sysDef.setIsInvisible(status);
        List<TeIdNameCn> roles = new ArrayList<>();
        for (TeSysDef roleUser : cndtItems) {
            TeIdNameCn role = new TeIdNameCn(roleUser.getId(), roleUser.getDefName(), roleUser.getCodeName());
            roles.add(role);
        }
        sysDef.setCndtItems(roles);
        TeSysUser loginUser = this.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("当前登陆用户不存在！");
        }
        Date nowDate = new Date();
        sysDef.setAddUser(loginUser.trans2User());
        sysDef.setAddTime(nowDate);
        sysDefDao.save(sysDef);
    }

    /**
     * 应该添加事务
     * 先插后删
     *
     * @param questionBankId
     * @param questionBankName
     * @param roleIds
     */
    @Override
    public void editQuestionBank(String questionBankId, String questionBankName, List<ObjectId> roleIds, Boolean status) {
        Assert.notNull(questionBankId, "该题库不存在！");
        TeSysDef old = sysDefDao.getSysDefById(StringUtil.to(questionBankId, ObjectId.class));
        Assert.notNull(old, "id为" + questionBankId + "题库不存在！");

        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        conds.add(new DC_I(DFN.sysDef_id, roleIds));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), SysDefConstants.DEF_CODE_EXAM_ROLE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef__id);
        fieldNames.add(DbFieldName.sysDef__defName);
        fieldNames.add(DbFieldName.sysDef__codeName);
        List<TeSysDef> cndtItems = sysDefDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(cndtItems) || cndtItems.size() != roleIds.size()) {
            throw new BaseException("角色人员不存在！");
        }
        List<TeIdNameCn> roles = new ArrayList<>();
        for (TeSysDef roleUser : cndtItems) {
            TeIdNameCn role = new TeIdNameCn(roleUser.getId(), roleUser.getDefName(), roleUser.getCodeName());
            roles.add(role);
        }
        if (CollectionUtils.isEmpty(roles)) {
            throw new BaseException("角色人员不存在！");
        }
        conds.clear();
        conds.add(new DC_E(DFN.sysDef_id, old.getId()));
        List<UpdataData> updates = new ArrayList<UpdataData>();
        updates.add(new UpdataData(DFN.sysDef__defName, questionBankName));
        updates.add(new UpdataData(DFN.sysDef_cndtItems, roles));
        updates.add(new UpdataData(DFN.sysDef_isInvisible, status));
        sysDefDao.updateByConds(conds, updates);

    }

    /**
     * 删除题库，删除题库下所有的题目及题目的选项
     *
     * @param questionBankIds
     */
    @Override
    public void deleteQuestionBank(List<ObjectId> questionBankIds) {
        if (CollectionUtils.isEmpty(questionBankIds)) {
            throw new BaseException("未选择题库");
        }
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        //删除题库
        conds.add(new DC_I(DFN.sysDef_id, questionBankIds));
        sysDefDao.deleteByConds(conds);
        //删除题库下所有的题目
        conds.clear();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST));
        conds.add(new DC_I(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), questionBankIds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef__id);
        List<TeSysDef> oldQuestions = sysDefDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(oldQuestions)) {
            return;
        }
        List<ObjectId> questionIds = oldQuestions.stream().map(TeSysDef::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(questionIds)) {
            return;
        }
        conds.clear();
        conds.add(new DC_I(DFN.sysDef_id, questionIds));
        sysDefDao.deleteByConds(conds);
        //删除题库下所有的题目的选项
        conds.clear();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION));
        conds.add(new DC_I(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), questionIds));
        sysDefDao.deleteByConds(conds);

    }

    /**
     * 应该添加事务
     *
     * @param questionParam
     */
    @Override
    public void addQuestion(QuestionParam questionParam) {
        Assert.notNull(questionParam.getQuestionBankId(), "题目所在题库不能为空！");
        TeSysDef oldQuestionBank = sysDefDao.getSysDefById(questionParam.getQuestionBankId());
        Assert.notNull(oldQuestionBank, "题目所在题库不存在！");
        Assert.notNull(questionParam.getQuestionName(), "题目名称不能为空！");
        Assert.notNull(questionParam.getAnswer(), "题目答案不能为空！");
        List<Map<String, String>> option2contents = questionParam.getOption2content();
        if (CollectionUtils.isEmpty(option2contents)) {
            throw new BaseException("选项内容不能为空！");
        }
        TeSysUser loginUser = this.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("当前登陆用户不存在！");
        }
        Date nowDate = new Date();
        //新增题目
        TeSysDef questionSysDef = new TeSysDef();
        TeDefType defType = new TeDefType();
        defType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST);
        defType.setDefTypeName(SysDefConstants.EXAM_QUEST_NAME);
        defType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_CODE_NAME);
        TeSrcDef srcDef = new TeSrcDef();
        srcDef.setSrcDefId(questionParam.getQuestionBankId());
        srcDef.setSrcDefName(oldQuestionBank.getDefName());
        srcDef.setSrcDefCodeName(oldQuestionBank.getCodeName());
        questionSysDef.setDefType(defType);
        questionSysDef.setSrcDef(srcDef);
        questionSysDef.setDefName(questionParam.getQuestionName());
        questionSysDef.setCodeName(questionParam.getAnswer());
        questionSysDef.setAddUser(loginUser.trans2User());
        questionSysDef.setAddTime(nowDate);
        questionSysDef.setIsStd(false);
        questionSysDef.setIsValid(true);
        sysDefDao.save(questionSysDef);
        //新增4个选项
        List<TeSysDef> optionBeans = new ArrayList<>();
        for (Map<String, String> map : option2contents) {
            String optionKey = "";
            String optionValue = "";
            for (Map.Entry<String, String> entry : map.entrySet()) {
                optionKey = entry.getKey();
                optionValue = entry.getValue();
            }
            if (StringUtil.isNull(optionKey) || StringUtil.isNull(optionValue)) {
                continue;
            }
            TeSysDef optionSysDef = new TeSysDef();
            TeDefType optionDefType = new TeDefType();
            optionDefType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST_OPTION);
            optionDefType.setDefTypeName(SysDefConstants.EXAM_QUEST_OPTION_NAME);
            optionDefType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_OPTION_CODE_NAME);
            TeSrcDef questionsrcDef = new TeSrcDef();
            questionsrcDef.setSrcDefId(questionSysDef.getId());
            questionsrcDef.setSrcDefName(questionSysDef.getDefName());
            questionsrcDef.setSrcDefCodeName(questionSysDef.getCodeName());
            optionSysDef.setDefType(optionDefType);
            optionSysDef.setSrcDef(questionsrcDef);
            optionSysDef.setCodeName(optionKey);
            optionSysDef.setDefName(optionValue);
            optionSysDef.setAddUser(loginUser.trans2User());
            optionSysDef.setAddTime(nowDate);
            optionSysDef.setIsStd(false);
            optionSysDef.setIsValid(true);
            optionBeans.add(optionSysDef);
        }
        if (CollectionUtils.isEmpty(optionBeans)) {
            return;
        }
        sysDefDao.batchSave(optionBeans);
    }

    @Override
    public void editQuestion(QuestionParam questionParam) {
        Assert.notNull(questionParam.getQuestionBankId(), "题目所在题库不能为空！");
        TeSysDef oldQuestionBank = sysDefDao.getSysDefById(questionParam.getQuestionBankId());
        Assert.notNull(oldQuestionBank, "题目所在题库不存在！");
        Assert.notNull(questionParam.getQuestionId(), "题目不存在！");
        TeSysDef oldQuestion = sysDefDao.getSysDefById(questionParam.getQuestionId());
        Assert.notNull(oldQuestion, "题目不存在！");
        Assert.notNull(questionParam.getQuestionName(), "题目名称不能为空！");
        Assert.notNull(questionParam.getAnswer(), "题目答案不能为空！");
        List<Map<String, String>> option2contents = questionParam.getOption2content();
        if (CollectionUtils.isEmpty(option2contents)) {
            throw new BaseException("选项内容不能为空！");
        }
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        conds.add(new DC_E(DFN.sysDef_id, oldQuestion.getId()));
        List<UpdataData> updates = new ArrayList<UpdataData>();
        updates.add(new UpdataData(DFN.sysDef__defName, questionParam.getQuestionName()));
        updates.add(new UpdataData(DFN.sysDef__codeName, questionParam.getAnswer()));
        sysDefDao.updateByConds(conds, updates);
        //找到原来题目所属的选项，将老的4个选项全部删除
        conds.clear();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), oldQuestion.getId()));
        sysDefDao.deleteByConds(conds);
        //新增4个选项
        TeSysUser loginUser = this.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("当前登陆用户不存在！");
        }
        Date nowDate = new Date();
        List<TeSysDef> optionBeans = new ArrayList<>();
        for (Map<String, String> map : option2contents) {
            String optionKey = "";
            String optionValue = "";
            for (Map.Entry<String, String> entry : map.entrySet()) {
                optionKey = entry.getKey();
                optionValue = entry.getValue();
            }
            if (StringUtil.isNull(optionKey) || StringUtil.isNull(optionValue)) {
                continue;
            }
            TeSysDef optionSysDef = new TeSysDef();
            TeDefType optionDefType = new TeDefType();
            optionDefType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST_OPTION);
            optionDefType.setDefTypeName(SysDefConstants.EXAM_QUEST_OPTION_NAME);
            optionDefType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_OPTION_CODE_NAME);
            TeSrcDef questionsrcDef = new TeSrcDef();
            questionsrcDef.setSrcDefId(oldQuestion.getId());
            questionsrcDef.setSrcDefName(oldQuestion.getDefName());
            questionsrcDef.setSrcDefCodeName(oldQuestion.getCodeName());
            optionSysDef.setDefType(optionDefType);
            optionSysDef.setSrcDef(questionsrcDef);
            optionSysDef.setCodeName(optionKey);
            optionSysDef.setDefName(optionValue);
            optionSysDef.setAddUser(loginUser.getManager());
            optionSysDef.setAddTime(nowDate);
            optionSysDef.setIsStd(false);
            optionSysDef.setIsValid(true);
            optionBeans.add(optionSysDef);
        }
        if (CollectionUtils.isEmpty(optionBeans)) {
            return;
        }
        sysDefDao.batchSave(optionBeans);
    }

    /**
     * 删除题目和删除题目所有的选项
     *
     * @param questionIds
     */
    @Override
    public void deleteQuestion(List<ObjectId> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            throw new BaseException("未选择任何题目！");
        }
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST));
        conds.add(new DC_I(DFN.sysDef_id, questionIds));
        sysDefDao.deleteByConds(conds);
        conds.clear();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION));
        conds.add(new DC_I(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), questionIds));
        sysDefDao.deleteByConds(conds);
    }

    @Override
    public Map<String, Object> importQuestion(ObjectId questionBankId, HttpServletRequest request, MultipartFile file) {
        Assert.notNull(questionBankId, "题库未选中！");
        TeSysDef oldQuestionBank = sysDefDao.getSysDefById(questionBankId);
        Assert.notNull(oldQuestionBank, "id为" + questionBankId + "题库不存在！");
        logger.info("导入结束：{},题库名称：", oldQuestionBank.getDefName());
        ExcelReader excelReader = null;
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "success";
        result.put("code", 200);
        result.put("msg", errorMsg);
        TeSysUser loginUser = this.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("当前登陆用户不存在！");
        }
        Date nowDate = new Date();
        try {
            excelReader = EasyExcel.read(file.getInputStream()).build();
            ExcelReadExecutor excelReadExecutor = excelReader.excelExecutor();
            List<ReadSheet> readSheets = excelReadExecutor.sheetList();
            if (CollectionUtils.isEmpty(readSheets)) {
                return result;
            }
            List<ReadSheet> readSheetList = new ArrayList<>();
            ExcelListener listener1 = new ExcelListener();
            ReadSheet readSheet = EasyExcel.readSheet(readSheets.get(0).getSheetNo()).headRowNumber(1).head(QuestionExcelVo.class)
                    .registerReadListener(listener1).build();
            readSheetList.add(readSheet);
            excelReader.read(readSheetList);
            List<QuestionExcelVo> questionExcelVos = listener1.getDataList();
            if (CollectionUtils.isEmpty(questionExcelVos)) {
                return result;
            }
            //校验答案code
            validExcelData(questionExcelVos);
            //批量新增题目，批量新增选项
            List<TeSysDef> questionBeans = new ArrayList<>();
            for (QuestionExcelVo vo : questionExcelVos) {
                TeSysDef questionBean = new TeSysDef();
                questionBean.setDefName(vo.getName());
                questionBean.setCodeName(vo.getAnswer());
                //其他信息
                TeDefType defType = new TeDefType();
                defType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST);
                defType.setDefTypeName(SysDefConstants.EXAM_QUEST_NAME);
                defType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_CODE_NAME);
                TeSrcDef srcDef = new TeSrcDef();
                srcDef.setSrcDefId(oldQuestionBank.getId());
                srcDef.setSrcDefName(oldQuestionBank.getDefName());
                srcDef.setSrcDefCodeName(oldQuestionBank.getCodeName());
                questionBean.setDefType(defType);
                questionBean.setSrcDef(srcDef);
                questionBean.setAddUser(loginUser.getManager());
                questionBean.setAddTime(nowDate);
                questionBean.setIsStd(false);
                questionBean.setIsValid(true);
                questionBeans.add(questionBean);

            }
            //业务储存数据
            Map<String, TeSysDef> questionName2BeansMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(questionBeans)) {
                sysDefDao.batchSave(questionBeans);//questionBeans是新生成的，有id
                for (TeSysDef sysDef : questionBeans) {
                    questionName2BeansMap.put(sysDef.getDefName(), sysDef);
                }
            }
            if (!CollectionUtils.isEmpty(questionName2BeansMap)) {
                String[] optionCodes = {SysDefConstants.EXAM_QUEST_OPTION_A, SysDefConstants.EXAM_QUEST_OPTION_B, SysDefConstants.EXAM_QUEST_OPTION_C,
                        SysDefConstants.EXAM_QUEST_OPTION_D};
                List<TeSysDef> optionBeans = new ArrayList<>();
                for (QuestionExcelVo vo : questionExcelVos) {
                    TeSysDef srcSysDef = questionName2BeansMap.get(vo.getName());//题目名称相同的不考虑
                    TeSrcDef questionsrcDef = new TeSrcDef();
                    questionsrcDef.setSrcDefId(srcSysDef.getId());
                    questionsrcDef.setSrcDefName(srcSysDef.getDefName());
                    questionsrcDef.setSrcDefCodeName(srcSysDef.getCodeName());
                    for (String optionCode : optionCodes) {
                        TeSysDef optionBean = new TeSysDef();
                        if (SysDefConstants.EXAM_QUEST_OPTION_A.equals(optionCode)) {
                            optionBean.setCodeName(SysDefConstants.EXAM_QUEST_OPTION_A);
                            optionBean.setDefName(vo.getOptionAcontent());
                        }
                        if (SysDefConstants.EXAM_QUEST_OPTION_B.equals(optionCode)) {
                            optionBean.setCodeName(SysDefConstants.EXAM_QUEST_OPTION_B);
                            optionBean.setDefName(vo.getOptionBcontent());
                        }
                        if (SysDefConstants.EXAM_QUEST_OPTION_C.equals(optionCode)) {
                            optionBean.setCodeName(SysDefConstants.EXAM_QUEST_OPTION_C);
                            optionBean.setDefName(vo.getOptionCcontent());
                        }
                        if (SysDefConstants.EXAM_QUEST_OPTION_D.equals(optionCode)) {
                            optionBean.setCodeName(SysDefConstants.EXAM_QUEST_OPTION_D);
                            optionBean.setDefName(vo.getOptionDcontent());
                        }
                        //其他信息
                        TeDefType optionDefType = new TeDefType();
                        optionDefType.setDefTypeId(SysDefConstants.DEF_ID_EXAM_QUEST_OPTION);
                        optionDefType.setDefTypeName(SysDefConstants.EXAM_QUEST_OPTION_NAME);
                        optionDefType.setDefTypeCodeName(SysDefConstants.EXAM_QUEST_OPTION_CODE_NAME);

                        optionBean.setDefType(optionDefType);
                        optionBean.setSrcDef(questionsrcDef);
                        optionBean.setAddUser(loginUser.getManager());
                        optionBean.setAddTime(nowDate);
                        optionBean.setIsStd(false);
                        optionBean.setIsValid(true);
                        optionBeans.add(optionBean);
                    }
                }
                sysDefDao.batchSave(optionBeans);
            }

        } catch (IOException e) {
            logger.error("导入失败", e);
            errorMsg = "导入失败，请联系管理员" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } catch (RuntimeException e) {
            logger.error("导入失败", e);
            errorMsg = "文档转换异常，请检查文档内容:" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } catch (Exception e) {
            logger.error("导入失败", e);
            errorMsg = "导入失败，请联系管理员" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
        }
        logger.info("导入结束：{}", DateUtil.formatDate2Str(DateUtil.now(), DateUtil.DATETIME_FORMAT));
        return result;
    }

    private void validExcelData(List<QuestionExcelVo> questionExcelVos) {
        for (QuestionExcelVo vo : questionExcelVos) {
            String answer = vo.getAnswer();
            String[] answers = answer.split(CommonConstants.ENGLISH_COMMA);
            for (String code : answers) {
                if (!EXAM_OPTION_CODES.contains(code)) {
                    throw new BaseException("答案code不在A,B,C,D范围内，请以英文逗号‘,’隔开");
                }
            }
        }
    }


    /**
     * 获取当前用户登录信息
     */
    private TeSysUser getLoginUser() {
        TeSysUser loginUser = sysUserService.queryByLoginName(LoginUserHolder.getLoginName());
        return loginUser;
    }

    /**
     * CC清单   族群和子族群
     * 从“sysUser”中取出isValid为true下的且manager.userId为被代理人下的所有人及其所有下级人员的剔重后的ccId清单
     */
    @Override
    public List<TeSysAicc> findSysAiccByCcIdList(ObjectId loginId, String value) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        ObjectId managerId = loginUser.getManager().getUserId();//new ObjectId("5a389f0510d00e056c73dce5");
        List<ObjectId> allUserIds = new ArrayList<>();
        allUserIds.clear();
        List<ObjectId> paramUserIds = getAgentedIds(loginId);//代理人
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(loginId);//自己本部门
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> ccIds = allUsers.stream().map(TeSysUser::getCcId).collect(Collectors.toSet());
        List<IDbCondition> conds = new ArrayList<IDbCondition>();
        List<IDbCondition> orConds = new ArrayList<>();
        orConds.add(new DbLikeCondition(DFN.sysAicc_ccId, value, true));
        orConds.add(new DbLikeCondition(DFN.sysAicc_ccName, value, true));
        orConds.add(new DbLikeCondition(DFN.sysAicc_ccNameCn, value, true));
        conds.add(new DC_I(DFN.sysAicc_ccId, new ArrayList<>(ccIds)));
        conds.add(new DbOrOperator(orConds));
        Sort sort = Sort.by(Sort.Direction.ASC, "ccId");
        Pager pager = new Pager(0, 20);
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef__id);
        fieldNames.add(DbFieldName.sysAicc_sbuId);
        fieldNames.add(DbFieldName.sysAicc_ccId);
        fieldNames.add(DbFieldName.sysAicc_ccName);
        fieldNames.add(DbFieldName.sysAicc_ccNameCn);
        List<TeSysAicc> sysAiccVos = sysAiccDao.findByFieldAndConds(conds, fieldNames, sort, pager);
        return sysAiccVos;
    }

    @Override
    public List<String> jobFamilys(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        List<ObjectId> allUserIds = new ArrayList<>();
        List<ObjectId> paramUserIds = getAgentedIds(loginId);
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(loginId);//加上自己本部门
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> jobFamilys = allUsers.stream().filter(s -> StringUtil.isNotNull(s.getJobFamily())).map(TeSysUser::getJobFamily).collect(Collectors.toSet());
        return new ArrayList<>(jobFamilys);
    }

    @Override
    public List<String> secondJobFamilys(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        List<ObjectId> allUserIds = new ArrayList<>();
        List<ObjectId> paramUserIds = getAgentedIds(loginId);
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(loginId);//加上自己本部门
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> secondJobFamilys = allUsers.stream().filter(s -> StringUtil.isNotNull(s.getSecondJobFamily())).map(TeSysUser::getSecondJobFamily).collect(Collectors.toSet());
        return new ArrayList<>(secondJobFamilys);
    }

    @Override
    public List<String> subSecondJobFamilys(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        List<ObjectId> allUserIds = new ArrayList<>();
        List<ObjectId> paramUserIds = getAgentedIds(loginId);
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(loginId);//加上自己本部门
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> secondJobFamilys = allUsers.stream().filter(s -> StringUtil.isNotNull(s.getSubSecondJobFamily())).map(TeSysUser::getSubSecondJobFamily).collect(Collectors.toSet());
        return new ArrayList<>(secondJobFamilys);
    }

    @Override
    public List<String> jobRoles(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        List<ObjectId> allUserIds = new ArrayList<>();
        List<ObjectId> paramUserIds = getAgentedIds(loginId);
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(loginId);//加上自己本部门
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> secondJobFamilys = allUsers.stream().filter(s -> StringUtil.isNotNull(s.getJobRole())).map(TeSysUser::getJobRole).collect(Collectors.toSet());
        return new ArrayList<>(secondJobFamilys);
    }

    public void getSubUsers(List<ObjectId> userIds, List<ObjectId> allUserIds) {
        allUserIds.addAll(userIds);
        List<TeSysUser> subSysUsers = sysUserService.getUsersByManagers(userIds);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(subSysUsers)) {
            List<ObjectId> subUserIds = subSysUsers.stream().map(TeSysUser::getId).collect(Collectors.toList());
            if (subUserIds != null && subUserIds.size() > 0) {
                getSubUsers(subUserIds, allUserIds);
            }
        }
    }

    /**
     * 角色清单
     * 当前登陆人自己创建的和当前登陆人的代理人创建的
     */
    @Override
    public List<TeSysDef> examRoles(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        List<ObjectId> agentIdAndagentedIds = new ArrayList<>();
        agentIdAndagentedIds.add(loginId);
        agentIdAndagentedIds.addAll(getAgentIds(loginId));
        agentIdAndagentedIds.add(loginUser.getManager().getUserId());
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        conds.add(new DC_I(DFN.common_addUser.dot(DFN.common_userId), agentIdAndagentedIds));
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), null);
        TeSysDef allROle = new TeSysDef();
        allROle.setDefName(SysDefConstants.DEF_NAME_ALL_ROLE);
        allROle.setId(SysDefConstants.DEF_ID_ALL_ROLE);
        sysDefs.add(allROle);
        TeSysDef noHaveRole = new TeSysDef();
        noHaveRole.setDefName(SysDefConstants.DEF_NAME_NO_HAVE_ROLE);
        noHaveRole.setId(SysDefConstants.DEF_ID_NO_HAVE_ROLE);
        sysDefs.add(noHaveRole);
//查询sysUser，传角色id,单选
        return sysDefs;
    }

    @Override
    public List<TeSysDef> questionBankMgtExamRoles(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        List<ObjectId> agentIdAndagentedIds = new ArrayList<>();
        agentIdAndagentedIds.add(loginId);
        agentIdAndagentedIds.addAll(getAgentIds(loginId));
        agentIdAndagentedIds.add(loginUser.getManager().getUserId());
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        conds.add(new DC_I(DFN.common_addUser.dot(DFN.common_userId), agentIdAndagentedIds));
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), null);
        return sysDefs;
    }

    /**
     * 当前登陆人作为被代理人找到自己的代理人，用在被代理人要看到代理人创建的信息
     *
     * @param loginId
     * @return
     */
    private List<ObjectId> getAgentIds(ObjectId loginId) {
        List<ObjectId> agentIds = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysUserAgent_user.dot(DFN.common_userId), loginId));
        List<TeSysUserAgent> agents = sysUserAgentDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()));
        if (CollectionUtils.isEmpty(agents)) {
            return agentIds;
        }
        agentIds = agents.stream().map(TeSysUserAgent::getAgent).map(TeUser::getUserId).collect(Collectors.toList());
        return agentIds;
    }

    /**
     * 当前登陆人作为代理人找到被代理对象，用在代理人要看到被代理人的部门人员信息
     *
     * @param loginId
     * @return
     */
    private List<ObjectId> getAgentedIds(ObjectId loginId) {
        List<ObjectId> agentIds = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysUserAgent_agent.dot(DFN.common_userId), loginId));
        List<TeSysUserAgent> agents = sysUserAgentDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()));
        if (CollectionUtils.isEmpty(agents)) {
            return agentIds;
        }
        agentIds = agents.stream().map(TeSysUserAgent::getUser).map(TeUser::getUserId).collect(Collectors.toList());
        return agentIds;
    }

    @Override
    public TeUser examRoleCreater(ObjectId loginId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser, "当前登陆用户不存在！");
        TeUser examRoleCreater = null;
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), loginUser.getId()));
        conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            return examRoleCreater;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByConds(conds, null);
        List<TeSysDefRoleUser2Role> oldRoleUsers = roleUsers.get(0).getRole();
        List<ObjectId> examRoleIds = oldRoleUsers.stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(examRoleIds)) {
            return examRoleCreater;
        }
        conds.clear();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_I(DFN.common__id, examRoleIds));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        List<TeSysDef> examRoleDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.biz_addTime.n()));
        if (CollectionUtils.isEmpty(examRoleDefs)) {
            return examRoleCreater;
        }
        List<TeUser> users = examRoleDefs.stream().map(TeSysDef::getAddUser).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(users)) {
            return examRoleCreater;
        }
        examRoleCreater = users.get(0);
        return examRoleCreater;
    }

    /**
     * 新增角色
     */
    @Override
    public CommonResult addExamRole(String defName, ObjectId loginId) {
        Assert.notNull(defName, "新角色名称不能为空！");
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        if (validRoleName(defName, loginUser)) {
            return CommonResult.fail("该考试角色已存在，请修改后提交！");
        }
        TeSysDef newRole = new TeSysDef();
        TeDefType defType = new TeDefType();
        defType.setDefTypeId(SysDefConstants.DEF_ID_SYS_PARA_VALUE);
        defType.setDefTypeName(SysDefConstants.DEF_NAME_SYS_PARA_VALUE);
        defType.setDefTypeCodeName(SysDefConstants.DEF_CODE_SYS_PARA_VALUE);
        TeSrcDef srcDef = new TeSrcDef();
        srcDef.setSrcDefId(SysDefConstants.DEF_ID_EXAM_ROLE);
        srcDef.setSrcDefName(SysDefConstants.DEF_NAME_EXAM_ROLE);
        srcDef.setSrcDefCodeName(SysDefConstants.DEF_CODE_EXAM_ROLE);
        newRole.setDefName(defName);
        newRole.setDefType(defType);
        newRole.setSrcDef(srcDef);
        newRole.setIsValid(true);
        newRole.setIsStd(true);
        newRole.setAddUser(loginUser.trans2User());//留痕是登陆人信息
        newRole.setAddTime(new Date());
        sysDefDao.save(newRole);
        return CommonResult.success();
    }

    private boolean validRoleName(String defName, TeSysUser loginUser) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysDef__defName, defName));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        conds.add(new DC_E(DFN.common_addUser.dot(DFN.common_userId), loginUser.getManager().getUserId()));
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), null);
        if (!CollectionUtils.isEmpty(sysDefs)) {
            return true;
        }
        return false;
    }


    /**
     * 角色分配  sysUser赋值examRole   multi
     * 适用前端2种场景：
     * 1，勾选多个分配角色（存在的更新，不存在的新增）；
     * 2，选中当前人1个，1)点+新增（前端将已有角色id一起传入后台）;2)勾选1个效果和选中一个效果一致
     * 存在的更新，相同角色要去重
     * 不存在的新增
     *
     * @param loginId
     * @param empIds
     * @param roleIds
     */
    @Override
    public void sysUserAddExamRole(ObjectId loginId, List<ObjectId> empIds, List<ObjectId> roleIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(empIds), "未选中任何员工！");
        List<TeSysUser> emps = sysUserService.queryUsersByIdListList(empIds);
        Assert.isTrue(!CollectionUtils.isEmpty(emps), "员工不存在！");
        Assert.isTrue(!CollectionUtils.isEmpty(roleIds), "未选中任何角色！");
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_I(DFN.sysDef_id, roleIds));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        List<TeSysDef> roleDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), null);
        if (CollectionUtils.isEmpty(roleDefs)) {
            return;
        }
        List<TeIdNameCn> examRoles = new ArrayList<>();
        List<TeSysDefRoleUser2Role> newRoles = new ArrayList<>();
        for (TeSysDef roleDef : roleDefs) {
            TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
            TeIdNameCn examRole = new TeIdNameCn();
            examRole.setCid(roleDef.getId());
            examRole.setName(roleDef.getDefName());
            examRole.setCodeName(roleDef.getCodeName());
            examRoles.add(examRole);
            role.setRoleId(roleDef.getId());
            role.setRoleName(roleDef.getDefName());
            role.setRoleCodeName(roleDef.getCodeName());
            newRoles.add(role);
        }
        TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
        srcDef.setSrcDefId(SysDefConstants.DEF_ID_EXAM_ROLE);
        srcDef.setSrcDefName(SysDefConstants.DEF_NAME_EXAM_ROLE);
        srcDef.setSrcDefCodeName(SysDefConstants.DEF_CODE_EXAM_ROLE);
        List<ObjectId> userIds = emps.stream().map(TeSysUser::getId).collect(Collectors.toList());
        //存在的更新
        Map<ObjectId, List<TeSysDefRoleUser2Role>> existUserId2ExamRolesMap = this.getOldSysDefRoleUser(userIds);
        Set<ObjectId> existRoleUserIds = existUserId2ExamRolesMap.keySet();
        //不存在的新增
        List<ObjectId> noExistRoleUserIds = new ArrayList<>();
        for (ObjectId userId : userIds) {
            if (!existRoleUserIds.contains(userId)) {
                noExistRoleUserIds.add(userId);
            }
        }
        logger.info("存在" + existRoleUserIds.size() + "人-更新角色");
        System.out.println("existRoleUserIds = " + existRoleUserIds);
        logger.info("不存在" + noExistRoleUserIds.size() + "人-新增角色");
        System.out.println("noExistRoleUserIds = " + noExistRoleUserIds);
        this.existRoleUserEmpsToUpdate(newRoles, existRoleUserIds, loginUser);
        List<TeSysUser> noExistRoleUserEmps = sysUserService.queryUsersByIdListList(noExistRoleUserIds);
        this.noExistRoleUserEmpsToAdd(newRoles, noExistRoleUserEmps, loginUser);
    }

    private void noExistRoleUserEmpsToAdd(List<TeSysDefRoleUser2Role> newRoles, List<TeSysUser> emps, TeSysUser loginUser) {
        if (CollectionUtils.isEmpty(emps)) {
            return;
        }
        TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
        srcDef.setSrcDefId(SysDefConstants.DEF_ID_EXAM_ROLE);
        srcDef.setSrcDefName(SysDefConstants.DEF_NAME_EXAM_ROLE);
        srcDef.setSrcDefCodeName(SysDefConstants.DEF_CODE_EXAM_ROLE);
        TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
        BeanUtils.copyProperties(loginUser.trans2User(), addUser);
        Date nowDay = new Date();
        List<TeSysDefRoleUser> roleUsers = new ArrayList<>();
        for (TeSysUser emp : emps) {
            TeSysDefRoleUser roleUser = new TeSysDefRoleUser();
            roleUser.setRole(newRoles);
            roleUser.setIsValid(true);
            roleUser.setSrcDef(srcDef);
            roleUser.setAddUser(addUser);//取被代理人下任一代理人
            roleUser.setAddTime(nowDay);
            TeSysDefRoleUser2User eRoleUser = new TeSysDefRoleUser2User();
            BeanUtils.copyProperties(emp.trans2User(), eRoleUser);
            roleUser.setRoleUser(eRoleUser);
            roleUsers.add(roleUser);
        }
        sysDefRoleUserDao.batchSave(roleUsers);

    }

    private void existRoleUserEmpsToUpdate(List<TeSysDefRoleUser2Role> newRoles,
                                           Set<ObjectId> existRoleUserIds,
                                           TeSysUser loginUser) {
        if (CollectionUtils.isEmpty(existRoleUserIds) || CollectionUtils.isEmpty(newRoles)) {
            return;
        }
        List<IDbCondition> conds = new ArrayList<>();
        TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
        srcDef.setSrcDefId(SysDefConstants.DEF_ID_EXAM_ROLE);
        srcDef.setSrcDefName(SysDefConstants.DEF_NAME_EXAM_ROLE);
        srcDef.setSrcDefCodeName(SysDefConstants.DEF_CODE_EXAM_ROLE);
        TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
        BeanUtils.copyProperties(loginUser.trans2User(), addUser);
        List<TeSysDefRoleUser> existDefRoleUsers = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_I(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), new ArrayList(existRoleUserIds)));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            return;
        }
        existDefRoleUsers = sysDefRoleUserDao.findByConds(conds, null);
        List<ObjectId> newRoleIds = newRoles.stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
        Map<ObjectId, TeSysDefRoleUser2Role> roleId2RoleMap = newRoles.stream().collect(Collectors.toMap(o -> o.getRoleId(), o -> o));
        List<UpdataData> updataDatas = new ArrayList<>();
        if (existRoleUserIds.size() > 1 && existDefRoleUsers.size() > 1) {
            updataDatas.add(new UpdataData(DFN.sysDefRoleUser__role, newRoles));
            sysDefRoleUserDao.updateByConds(conds, updataDatas);
        } else {
            TeSysDefRoleUser existDefRoleUser = existDefRoleUsers.get(0);
            List<TeSysDefRoleUser2Role> oldRoles = existDefRoleUser.getRole();
            if (null == oldRoles) {
                oldRoles = new ArrayList<>();
            }
            List<ObjectId> oldRoleIds = oldRoles.stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
            for (ObjectId newRoleId : newRoleIds) {
                if (CollectionUtils.isEmpty(oldRoleIds) || !oldRoleIds.contains(newRoleId)) {
                    oldRoles.add(roleId2RoleMap.get(newRoleId));
                }
            }
            updataDatas.add(new UpdataData(DFN.sysDefRoleUser__role, oldRoles));
            sysDefRoleUserDao.updateById(existDefRoleUser.getId(), updataDatas);
        }
    }


    private Map<ObjectId, List<TeSysDefRoleUser2Role>> getOldSysDefRoleUser(List<ObjectId> userIds) {
        Map<ObjectId, List<TeSysDefRoleUser2Role>> userId2ExamRolesMap = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return userId2ExamRolesMap;
        }
        List<IDbCondition> conds = new ArrayList<>();
        conds.clear();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_I(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), userIds));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            return userId2ExamRolesMap;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByConds(conds, null);
        userId2ExamRolesMap = roleUsers.stream().collect(Collectors.toMap(o -> o.getRoleUser().getUserId(), o -> o.getRole()));
        return userId2ExamRolesMap;

    }

    /**
     * @param empId
     * @param roleId
     */
    @Override
    public void sysUserRemoveExamRole(ObjectId empId, ObjectId roleId) {
        Assert.notNull(empId, "未选中任何员工！");
        Assert.notNull(roleId, "未选中任何要移除的角色！");
        TeSysUser emp = sysUserDao.findById(empId);
        Assert.notNull(emp, "不存在该员工！");
        List<IDbCondition> conds = new ArrayList<>();
        List<UpdataData> updataDatas = new ArrayList<UpdataData>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId), empId));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            throw new BaseException("角色不存在！");
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByConds(conds, null);
        List<TeSysDefRoleUser2Role> oldRoles = roleUsers.get(0).getRole();
        List<ObjectId> oldRoleIds = oldRoles.stream().map(TeSysDefRoleUser2Role::getRoleId).collect(Collectors.toList());
        if (!oldRoleIds.contains(roleId)) {
            throw new BaseException("角色不存在！");
        }
        List<TeSysDefRoleUser2Role> newExamRoles = new ArrayList<>();
        for (TeSysDefRoleUser2Role oleRole : oldRoles) {
            if (!roleId.equals(oleRole.getRoleId())) {
                newExamRoles.add(oleRole);
            }
        }
        if (!CollectionUtils.isEmpty(newExamRoles)) {
            updataDatas.add(new UpdataData(DFN.sysDefRoleUser__role, newExamRoles));
            sysDefRoleUserDao.updateByConds(conds, updataDatas);
        } else {
            sysDefRoleUserDao.deleteById(roleUsers.get(0).getId());
        }


    }

    @Override
    public void sysUserIfExamOpt(ObjectId loginId, List<ObjectId> empIds, String ifExam) {
        Assert.isTrue(!CollectionUtils.isEmpty(empIds), "未选中任何员工！");
        List<TeSysUser> emps = sysUserService.queryUsersByIdListList(empIds);
        Assert.isTrue(!CollectionUtils.isEmpty(emps), "员工不存在！");
        Assert.isTrue(StringUtil.isNotNull(ifExam), "未选中是否考试！");
        Assert.notNull(loginId, "当前登陆用户不存在！");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_I(DFN.sysUser_id, empIds));
        List<UpdataData> updataDatas = new ArrayList<UpdataData>();
        updataDatas.add(new UpdataData(DFN.sysUser_ifExam, SysDefConstants.DEF_YES.equals(ifExam) ? true : false));
        sysUserDao.updateByConds(conds, updataDatas);
    }


    private PageBean examUsersAndSysDefRoleUsers(ExamUserMgeParam examUserMgeParam, ObjectId empId, Pager pager) {
        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = new ArrayList<>();
        Set<ObjectId> subUserIds0 = new HashSet<>();
        TeSysUser emp = sysUserDao.findById(empId);
        Assert.notNull(emp, "不存在该员工！");
        Assert.notNull(emp.getManager(), "该员工上级经理不存在！");
        List<ObjectId> allUserIds = new ArrayList<>();
        List<ObjectId> paramUserIds = getAgentedIds(empId);//默认看到被代理人部门cc信息和自己部门cc信息
        this.getSubUsers(paramUserIds, allUserIds);
        allUserIds.add(empId);
        List<TeSysUser> allUsers = sysUserService.queryUsersByIdListList(allUserIds);//包括外包人员
        Set<String> ccIds = allUsers.stream().map(TeSysUser::getCcId).collect(Collectors.toSet());
        subUserIds0 = allUsers.stream().map(TeSysUser::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(subUserIds0)) {
            return pageBean;
        }
        if (StringUtil.isNotNull(examUserMgeParam.getCcid())) {
            conds.add(new DC_E(DFN.sysUser_ccId, examUserMgeParam.getCcid()));
        } else {
            conds.add(new DC_I(DFN.sysUser_ccId, new ArrayList(ccIds)));
        }
        List<ObjectId> roleUserIds = examUserMgeParam.getRoleUserIds();
        if (StringUtil.isNotNull(examUserMgeParam.getUserId())) {//前台选了人
            //递归下级人员不包含，直接返回;选了角色，角色人员不包含此人,直接返回
            if (!subUserIds0.contains(examUserMgeParam.getUserId())
                    || (!CollectionUtils.isEmpty(roleUserIds) && !roleUserIds.contains(examUserMgeParam.getUserId()))) {
                return pageBean;
            } else {
                conds.add(new DC_E(DFN.sysUser_id, examUserMgeParam.getUserId()));
            }
        } else {//前台没选人，选了角色，判断下级人员是否包含此角色，
            //newRoleSubUserIds0= 角色人员-不在下级人员
            List<ObjectId> newRoleSubUserIds0 = new ArrayList<>();
            if (!CollectionUtils.isEmpty(roleUserIds)) {
                for (ObjectId roleUserId : roleUserIds) {
                    if (subUserIds0.contains(roleUserId)) {
                        newRoleSubUserIds0.add(roleUserId);
                    }
                }
                if (CollectionUtils.isEmpty(newRoleSubUserIds0)) {//角色人员不在递归下级人员中，直接返回
                    return pageBean;
                }
                conds.add(new DC_I(DFN.sysUser_id, new ArrayList(newRoleSubUserIds0)));
            } else {//前台没选人，也没选角色，
                conds.add(new DC_I(DFN.sysUser_id, new ArrayList(subUserIds0)));
            }
        }
        if (StringUtil.isNotNull(examUserMgeParam.getJobFamily())) {
            conds.add(new DC_E(DFN.sysUser_jobFamily, examUserMgeParam.getJobFamily()));
        }
        if (StringUtil.isNotNull(examUserMgeParam.getSecondJobFamily())) {
            conds.add(new DC_E(DFN.sysUser_secondJobFamily, examUserMgeParam.getSecondJobFamily()));
        }
        if (StringUtil.isNotNull(examUserMgeParam.getSubSecondJobFamily())) {
            conds.add(new DC_E(DFN.sysUser_subSecondJobFamily, examUserMgeParam.getSubSecondJobFamily()));
        }
        if (StringUtil.isNotNull(examUserMgeParam.getJobRole())) {
            conds.add(new DC_E(DFN.sysUser_jobRole, examUserMgeParam.getJobRole()));
        }
        if (StringUtil.isNotNull(examUserMgeParam.getIfExam())) {//是否考试
            conds.add(new DC_E(DFN.sysUser_ifExam, examUserMgeParam.getIfExam()));
        }
        if (StringUtil.isNotNull(examUserMgeParam.getHireDateStart()) && StringUtil.isNotNull(examUserMgeParam.getHireDateEnd())) {
            conds.add(new DC_B(DFN.sysUser_hireDate, examUserMgeParam.getHireDateStart(), examUserMgeParam.getHireDateEnd(), false, false));
        }
        conds.add(new DC_E(DFN.sysUser_isValid, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysUser_id);
        fieldNames.add(DbFieldName.sysUser_jobFamily);
        fieldNames.add(DbFieldName.sysUser_secondJobFamily);
        fieldNames.add(DbFieldName.sysUser_subSecondJobFamily);
        fieldNames.add(DbFieldName.sysUser_hireDate);
        fieldNames.add(DbFieldName.sysUser_jobRole);
        fieldNames.add(DbFieldName.sysUser__loginName);
        fieldNames.add(DbFieldName.sysUser__userName);
        //sysUser_examRole 从sysDefRoleUser取
        fieldNames.add(DbFieldName.common_addTime);
        fieldNames.add(DbFieldName.sysUser_ccId);
        fieldNames.add(DbFieldName.sysUser_ccName);
        fieldNames.add(DbFieldName.sysUser_ccNameCn);
        fieldNames.add(DbFieldName.sysUser_sbuId);
        fieldNames.add(DbFieldName.sysUser_sbuName);
        fieldNames.add(DbFieldName.sysUser_ifExam);
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysUser_hireDate.n());
        long count = sysUserDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count < 1) {
            return pageBean;
        }
        List<TeSysUser> emps = sysUserDao.findByFieldAndConds(conds, fieldNames, sort, pager);
        Map<ObjectId, List<TeIdNameCn>> userId2ExamRolesFromSysDefroleUser = getUserId2ExamRolesFromSysDefroleUser();
        List<SysRoleUserVo> newEmps = new ArrayList<>();
        for (TeSysUser e : emps) {
            SysRoleUserVo vo = new SysRoleUserVo();
            BeanUtils.copyProperties(e, vo);
            if (MapUtils.isNotEmpty(userId2ExamRolesFromSysDefroleUser)) {
                vo.setExamRole(userId2ExamRolesFromSysDefroleUser.get(e.getId()));
            }
            newEmps.add(vo);
        }
        pageBean.setObjectList(newEmps);
        return pageBean;
    }

    private Map<ObjectId, List<TeIdNameCn>> getUserId2ExamRolesFromSysDefroleUser() {
        Map<ObjectId, List<TeIdNameCn>> userId2ExamRolesMap = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysUser_isValid, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), null, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefClickLog__userId), null, true));
        conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysUser_id);
        fieldNames.add(DbFieldName.sysDefRoleUser_srcDef);
        fieldNames.add(DbFieldName.sysDefRoleUser__roleUser);
        fieldNames.add(DbFieldName.sysDefRoleUser__role);
        fieldNames.add(DbFieldName.common_addTime);
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            return userId2ExamRolesMap;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames, sort);
        if (CollectionUtils.isEmpty(roleUsers)) {
            return userId2ExamRolesMap;
        }
        for (TeSysDefRoleUser roleUser : roleUsers) {
            ObjectId userId = roleUser.getRoleUser().getUserId();
            List<TeIdNameCn> examRoles = new ArrayList<>();
            for (TeSysDefRoleUser2Role teSysDefRoleUser2Role : roleUser.getRole()) {
                TeIdNameCn teIdNameCn = new TeIdNameCn();
                teIdNameCn.setCid(teSysDefRoleUser2Role.getRoleId());
                teIdNameCn.setName(teSysDefRoleUser2Role.getRoleName());
                examRoles.add(teIdNameCn);
            }
            userId2ExamRolesMap.put(userId, examRoles);
        }
        return userId2ExamRolesMap;
    }

    private List<ObjectId> getEaxmRoleIds() {
        List<ObjectId> roleUserIds = new ArrayList<>();
        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), null, true));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefClickLog__userId), null, true));
        conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysUser_id);
        fieldNames.add(DbFieldName.sysDefRoleUser__role);
        fieldNames.add(DbFieldName.sysDefRoleUser__roleUser);
        fieldNames.add(DbFieldName.common_addTime);
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
        long count = sysDefRoleUserDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count < 1) {
            return roleUserIds;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames, sort);
        if (CollectionUtils.isEmpty(roleUsers)) {
            return roleUserIds;
        }
        roleUserIds = roleUsers.stream().filter(e -> null != e.getRoleUser()).map(TeSysDefRoleUser::getRoleUser).map(TeSysDefRoleUser2User::getUserId).collect(Collectors.toList());
        return roleUserIds;
    }


    private List<TeIdNameCn> convertToExamRoles(Document output) {
        List<TeIdNameCn> examRoles = new ArrayList<>();
        if (output.get("_joinSysDefRoleUser_") instanceof Map) {
            Map<String, Object> map = (Map) output.get("_joinSysDefRoleUser_");
            if (map.get("role") instanceof List) {
                List dbList = (List) map.get("role");

                if (dbList.size() > 0) {
                    for (Object db : dbList) {
                        if (db instanceof Map) {
                            Map<String, Object> mapDb = (Map) db;
                            TeIdNameCn examRole = new TeIdNameCn();
                            examRole.setCid(StringUtil.to(mapDb.get("roleId"), ObjectId.class));
                            examRole.setName(StringUtil.to(mapDb.get("roleName"), String.class));
                            examRoles.add(examRole);
                        }
                    }
                }
            }
        }
        return examRoles;
    }

    /**
     * 1，具体（考试）角色-，sysDEfRoleUser中过滤具体角色得userIds,并组装成userId-List<ExamRole>和userIds;到sysUSer中取信息
     * 2，无(考试)角色- //从sysUser过滤出第一遍userIds1,拿出sysDefRoleUser考试角色userIds2,userIds1剔除userIds2中包含userIds的
     * 3，所有-不加sysDefRoleUser过滤，只取sysUser；用1的userId-List<ExamRole>和userIds;组装部分人员的角色信息
     *
     * @param examUserMgeParam
     * @param empId
     * @return
     */
    @Override
    public PageBean examUsers1(ExamUserMgeParam examUserMgeParam, ObjectId empId) {
        PageBean pageBean = new PageBean();
        Pager pager = examUserMgeParam.getPager();
        if (StringUtil.isNotNull(examUserMgeParam.getRoleId())) {
            if (SysDefConstants.DEF_ID_NO_HAVE_ROLE.equals(examUserMgeParam.getRoleId())) {//无角色
                PageBean pageBean0 = examUsersAndSysDefRoleUsers(examUserMgeParam, empId, null);
                List<SysRoleUserVo> emps = pageBean0.getObjectList();
                if (CollectionUtils.isEmpty(emps)) {
                    return pageBean;
                }
                List<ObjectId> userIds1 = emps.stream().map(SysRoleUserVo::getId).collect(Collectors.toList());
                List<ObjectId> roleUserIds2 = this.getEaxmRoleIds();
                List<ObjectId> noHaveExamRoleUserIds3 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(roleUserIds2)) {
                    for (ObjectId objectId : userIds1) {
                        if (!roleUserIds2.contains(objectId)) {
                            noHaveExamRoleUserIds3.add(objectId);
                        }
                    }
                }
                if (CollectionUtils.isEmpty(noHaveExamRoleUserIds3)) {
                    return pageBean;
                }
                examUserMgeParam.setRoleUserIds(noHaveExamRoleUserIds3);
                examUserMgeParam.setUserId(null);
                pageBean = examUsersAndSysDefRoleUsers(examUserMgeParam, empId, pager);
                return pageBean;
            }
            if (SysDefConstants.DEF_ID_ALL_ROLE.equals(examUserMgeParam.getRoleId())) {//所有
                pageBean = examUsersAndSysDefRoleUsers(examUserMgeParam, empId, pager);
                return pageBean;
            }
            List<ObjectId> eaxmRoleFilterUserIds = getEaxmRoleFilterUserIds(examUserMgeParam.getRoleId());
            if (CollectionUtils.isEmpty(eaxmRoleFilterUserIds)) {
                return pageBean;
            }
            examUserMgeParam.setRoleUserIds(eaxmRoleFilterUserIds);
            pageBean = examUsersAndSysDefRoleUsers(examUserMgeParam, empId, pager);
            return pageBean;
        } else {//所有
            pageBean = examUsersAndSysDefRoleUsers(examUserMgeParam, empId, pager);
            return pageBean;
        }
    }

    private List<ObjectId> getEaxmRoleFilterUserIds(ObjectId roleId) {
        List<ObjectId> roleUserIds = new ArrayList<>();
        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_I(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId), Arrays.asList(roleId)));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefClickLog__userId), null, true));
        conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysUser_id);
        fieldNames.add(DbFieldName.sysDefRoleUser__role);
        fieldNames.add(DbFieldName.sysDefRoleUser__roleUser);
        fieldNames.add(DbFieldName.common_addTime);
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
        long count = sysDefRoleUserDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count < 1) {
            return roleUserIds;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames, sort);
        if (CollectionUtils.isEmpty(roleUsers)) {
            return roleUserIds;
        }
        roleUserIds = roleUsers.stream().filter(e -> null != e.getRoleUser()).map(TeSysDefRoleUser::getRoleUser).map(TeSysDefRoleUser2User::getUserId).collect(Collectors.toList());
        return roleUserIds;
    }


    @Override
    public PageBean exportExamUsers(ExamUserMgeParam examUserMgeParam, ObjectId empId) {
        examUserMgeParam.setPager(new Pager(0, 65535));
        PageBean result = new PageBean();
        List<LinkedHashMap<String, Object>> data = new ArrayList<LinkedHashMap<String, Object>>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("cc", "CC");
        map.put("JobFamily", "族群");
        map.put("SecondJobFamily", "子族群");
        map.put("SubSecondJobFamily", "子类");
        map.put("JobRole", "岗位");
        map.put("UserName", "姓名");
        map.put("NT", "NT");
        map.put("hireDate", "入职时间");
        map.put("role", "角色");
        map.put("IfExam", "是否考试");
        data.add(map);
        List<SysRoleUserVo> list = this.examUsers1(examUserMgeParam, empId).getObjectList();
        if (!CollectionUtils.isEmpty(list)) {
            for (SysRoleUserVo reslutVo : list) {
                LinkedHashMap<String, Object> hashMap = new LinkedHashMap<String, Object>();
                hashMap.put("cc", reslutVo.getCcId() + "/" + reslutVo.getCcNameCn());
                hashMap.put("JobFamily", reslutVo.getJobFamily());
                hashMap.put("SecondJobFamily", reslutVo.getSecondJobFamily());
                hashMap.put("SubSecondJobFamily", reslutVo.getSubSecondJobFamily());
                hashMap.put("JobRole", reslutVo.getJobRole());
                hashMap.put("UserName", reslutVo.getUserName());
                hashMap.put("NT", reslutVo.getLoginName());
                hashMap.put("hireDate", reslutVo.getHireDate());
                List<String> list1 = new ArrayList<>();
                List<TeIdNameCn> examRoles = reslutVo.getExamRole();
                if (!CollectionUtils.isEmpty(examRoles)) {
                    for (TeIdNameCn teIdNameCn : examRoles) {
                        list1.add(teIdNameCn.getName());
                    }
                }
                hashMap.put("examRole", CollectionUtils.isEmpty(list1) ? "" : String.join(",", list1));//角色这里要根据改造之后的sysDefRoleUser重新取
                hashMap.put("IfExam", (null != reslutVo.getIfExam() && !!reslutVo.getIfExam()) ? "是" : "否");
                data.add(hashMap);
            }
        }
        result.setObjectList(data);
        result.setCount(!CollectionUtils.isEmpty(list) ? list.size() : 0);
        return result;
    }

    /**
     * 导入角色prometheus
     */
    @Override
    public Map<String, Object> importExamEmp(ObjectId loginId, HttpServletRequest request, MultipartFile file) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        TeSysUser loginUser = sysUserService.findById(loginId);
        Assert.notNull(loginUser.getManager(), "当前登陆用户上级经理不存在！");

        Map<String, Object> result = new HashMap<>();
        String errorMsg = "success";
        result.put("code", 200);
        result.put("msg", errorMsg);

        logger.info("导入开始：{},", " ");
        ExcelReader excelReader = null;
        if (loginUser == null) {
            throw new BaseException("当前登陆用户不存在！");
        }
        Date nowDate = new Date();
        try {
            excelReader = EasyExcel.read(file.getInputStream()).build();
            ExcelReadExecutor excelReadExecutor = excelReader.excelExecutor();
            List<ReadSheet> readSheets = excelReadExecutor.sheetList();
            if (CollectionUtils.isEmpty(readSheets)) {
                return result;
            }
            List<ReadSheet> readSheetList = new ArrayList<>();
            ExcelListener listener1 = new ExcelListener();
            ReadSheet readSheet = EasyExcel.readSheet(readSheets.get(0).getSheetNo()).headRowNumber(1).head(ExamEmpExcelVo.class)
                    .registerReadListener(listener1).build();
            readSheetList.add(readSheet);
            excelReader.read(readSheetList);
            List<ExamEmpExcelVo> examEmpExcelVos = listener1.getDataList();
            if (CollectionUtils.isEmpty(examEmpExcelVos)) {
                return result;
            }
            //校验NT账号 和 角色人员
            Map<String, TeSysDef> roleName2SysDefMap = new HashMap<>();
            Map<String, TeSysUser> nt2empMap = new HashMap<>();
            validExamUserExcel(examEmpExcelVos, roleName2SysDefMap, nt2empMap, loginUser);
            //批量更新sysUser的examRole
            List<ObjectId> userIds = new ArrayList<>();
            List<BatchCondsUpsert> batchCondsUpserts = new ArrayList<>();
            for (ExamEmpExcelVo vo : examEmpExcelVos) {
                String roleStr = vo.getRole();
                String[] roleArr = roleStr.split(CommonConstants.ENGLISH_COMMA);
                List<Map<String, Object>> examRoles = new ArrayList<>();
                for (String roleName : roleArr) {
                    TeSysDef rowDef = roleName2SysDefMap.get(roleName);
                    Map<String, Object> map = new HashMap<>();
                    map.put("cid", rowDef.getId());
                    map.put("name", rowDef.getDefName());
                    examRoles.add(map);
                }
                TeSysUser emp = nt2empMap.get(vo.getNt());
                userIds.add(emp.getId());
                List<IDbCondition> conds = new ArrayList<>();
                conds.add(new DC_E(DFN.sysUser_id, emp.getId()));
                List<UpdataData> updates = new ArrayList<>();
                //是否考试，不填默认是false
                updates.add(new UpdataData(DFN.sysUser_ifExam, SysDefConstants.DEF_YES.equals(vo.getIfExam()) ? true : false));
                BatchCondsUpsert batchCondsUpsert = new BatchCondsUpsert(conds, updates);
                batchCondsUpserts.add(batchCondsUpsert);

            }
            if (!CollectionUtils.isEmpty(batchCondsUpserts)) {
                sysUserDao.batchUpdate(batchCondsUpserts);
            }
            //更新sysDefRoleUser信息,代替原来的sysUser_examRole
            //存在的更新
            Map<ObjectId, List<TeSysDefRoleUser2Role>> existUserId2ExamRolesMap = this.getOldSysDefRoleUser(userIds);
            Set<ObjectId> existRoleUserIds = existUserId2ExamRolesMap.keySet();
            //不存在的新增
            List<ObjectId> noExistRoleUserIds = new ArrayList<>();
            for (ObjectId userId : userIds) {
                if (!existRoleUserIds.contains(userId)) {
                    noExistRoleUserIds.add(userId);
                }
            }
            logger.info("导入人员：存在" + existRoleUserIds.size() + "人-更新角色");
            System.out.println("existRoleUserIds = " + existRoleUserIds);
            logger.info("导入人员：不存在" + noExistRoleUserIds.size() + "人-新增角色");
            System.out.println("noExistRoleUserIds = " + noExistRoleUserIds);
            batchCondsUpserts.clear();
            List<ExamEmpExcelVo> existRoleUserExamEmpExcelVos = examEmpExcelVos.stream().filter(vo -> existRoleUserIds.contains(nt2empMap.get(vo.getNt()).getId()))
                    .collect(Collectors.toList());
            for (ExamEmpExcelVo vo : existRoleUserExamEmpExcelVos) {
                TeSysUser emp = nt2empMap.get(vo.getNt());
                String roleStr = vo.getRole();
                String[] roleArr = roleStr.split(CommonConstants.ENGLISH_COMMA);
                List<TeSysDefRoleUser2Role> newRoles = new ArrayList<>();
                for (String roleName : roleArr) {
                    TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
                    TeSysDef rowDef = roleName2SysDefMap.get(roleName);
                    role.setRoleId(rowDef.getId());
                    role.setRoleName(rowDef.getDefName());
                    role.setRoleCodeName(rowDef.getCodeName());
                    newRoles.add(role);
                }
                List<IDbCondition> conds = new ArrayList<>();
                conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), emp.getId()));
                List<UpdataData> updates = new ArrayList<>();
                updates.add(new UpdataData(DFN.sysDefRoleUser__role, newRoles));
                BatchCondsUpsert batchCondsUpsert = new BatchCondsUpsert(conds, updates);
                batchCondsUpserts.add(batchCondsUpsert);
            }
            if (!CollectionUtils.isEmpty(batchCondsUpserts)) {
                sysDefRoleUserDao.batchUpdate(batchCondsUpserts);
            }
            //不存在的新增角色人员
            List<ExamEmpExcelVo> noExistRoleUserExamEmpExcelVos = examEmpExcelVos.stream().filter(vo -> noExistRoleUserIds.contains(nt2empMap.get(vo.getNt()).getId()))
                    .collect(Collectors.toList());
            TeSysDefRoleUser2SysDef srcDef = new TeSysDefRoleUser2SysDef();
            srcDef.setSrcDefId(SysDefConstants.DEF_ID_EXAM_ROLE);
            srcDef.setSrcDefName(SysDefConstants.DEF_NAME_EXAM_ROLE);
            srcDef.setSrcDefCodeName(SysDefConstants.DEF_CODE_EXAM_ROLE);
            TeSysDefRoleUser2User addUser = new TeSysDefRoleUser2User();
            BeanUtils.copyProperties(loginUser.trans2User(), addUser);
            List<TeSysDefRoleUser> roleUsers = new ArrayList<>();
            for (ExamEmpExcelVo vo : noExistRoleUserExamEmpExcelVos) {
                TeSysUser emp = nt2empMap.get(vo.getNt());
                String roleStr = vo.getRole();
                String[] roleArr = roleStr.split(CommonConstants.ENGLISH_COMMA);
                List<TeSysDefRoleUser2Role> newRoles = new ArrayList<>();
                for (String roleName : roleArr) {
                    TeSysDefRoleUser2Role role = new TeSysDefRoleUser2Role();
                    TeSysDef rowDef = roleName2SysDefMap.get(roleName);
                    role.setRoleId(rowDef.getId());
                    role.setRoleName(rowDef.getDefName());
                    role.setRoleCodeName(rowDef.getCodeName());
                    newRoles.add(role);
                }
                TeSysDefRoleUser roleUser = new TeSysDefRoleUser();
                roleUser.setRole(newRoles);
                roleUser.setIsValid(true);
                roleUser.setSrcDef(srcDef);
                roleUser.setAddUser(addUser);//取被代理人下任一代理人
                roleUser.setAddTime(nowDate);
                TeSysDefRoleUser2User eRoleUser = new TeSysDefRoleUser2User();
                BeanUtils.copyProperties(emp.trans2User(), eRoleUser);
                roleUser.setRoleUser(eRoleUser);
                roleUsers.add(roleUser);
            }
            if (!CollectionUtils.isEmpty(roleUsers)) {
                sysDefRoleUserDao.batchSave(roleUsers);
            }
        } catch (IOException e) {
            logger.error("导入失败", e);
            errorMsg = "导入失败，请联系管理员" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } catch (RuntimeException e) {
            logger.error("导入失败", e);
            errorMsg = "文档转换异常，请检查文档内容:" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } catch (Exception e) {
            logger.error("导入失败", e);
            errorMsg = "导入失败，请联系管理员" + e.getMessage();
            result.put("code", 500);
            result.put("msg", errorMsg);
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
        }
        logger.info("导入结束：{}", DateUtil.formatDate2Str(DateUtil.now(), DateUtil.DATETIME_FORMAT));
        return result;
    }


    private void validExamUserExcel(List<ExamEmpExcelVo> examEmpExcelVos, Map<String, TeSysDef> roleName2SysDefMap,
                                    Map<String, TeSysUser> nt2empMap, TeSysUser loginUser) {
        List<String> nts = new ArrayList<>();
        List<String> roleNames = new ArrayList<>();
        for (ExamEmpExcelVo vo : examEmpExcelVos) {
            if (!nts.contains(vo.getNt().trim())) {
                nts.add(vo.getNt().trim());
            }
            String roleStr = vo.getRole();
            String[] roleArr = roleStr.split(CommonConstants.ENGLISH_COMMA);
            for (String roleName : roleArr) {
                if (!roleNames.contains(roleName.trim())) {
                    roleNames.add(roleName.trim());
                }
            }
        }
        Map<String, List<TeSysDef>> nt2RoleDefMap = new HashMap<>();
        Map<String, List<String>> nt2RoleStrsMap = new HashMap<>();
        if (CollectionUtils.isEmpty(nts) || CollectionUtils.isEmpty(roleNames)) {
            throw new BaseException("NT账号、人员角色不能为空！");
        }
        List<ObjectId> agentIdAndagentedIds = new ArrayList<>();
        agentIdAndagentedIds.add(loginUser.getId());
        agentIdAndagentedIds.addAll(getAgentIds(loginUser.getId()));
        agentIdAndagentedIds.add(loginUser.getManager().getUserId());
        //校验角色是否存在  不存在报错，存在获取sysDef
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_I(DFN.sysDef__defName, roleNames));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeCodeName.SYS_PARA_VALUE.getValue()));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        conds.add(new DC_I(DFN.common_addUser.dot(DFN.common_userId), agentIdAndagentedIds));
        List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), null);
        if (CollectionUtils.isEmpty(sysDefs)) {
            throw new BaseException("角色不存在！");
        }
        //避免角色重名
        for (TeSysDef sysDef : sysDefs) {
            if (!roleName2SysDefMap.containsKey(sysDef.getDefName())) {
                roleName2SysDefMap.put(sysDef.getDefName(), sysDef);
            }
        }
        if (CollectionUtils.isEmpty(roleName2SysDefMap)) {
            throw new BaseException("角色不存在！");
        }
        List<String> noExistRoleName = new ArrayList<>();
        for (String roleName : roleNames) {
            if (!roleName2SysDefMap.containsKey(roleName) || null == roleName2SysDefMap.get(roleName)) {
                noExistRoleName.add(roleName);
            }
        }
        if (!CollectionUtils.isEmpty(noExistRoleName)) {
            throw new BaseException("角色:" + noExistRoleName.toString() + "不存在！");
        }
        //校验NT账号是否存在  不存在报错，存在获取sysUser
        conds.clear();
        conds.add(new DC_I(DFN.sysUser__loginName, nts));
        List<TeSysUser> sysUsers = sysUserDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()));
        if (CollectionUtils.isEmpty(sysUsers)) {
            throw new BaseException("NT账号对应的人员不存在！");
        }
        //避免nt重名
        for (TeSysUser sysUser : sysUsers) {
            if (!nt2empMap.containsKey(sysUser.getLoginName())) {
                nt2empMap.put(sysUser.getLoginName(), sysUser);
            }
        }
        if (CollectionUtils.isEmpty(nt2empMap)) {
            throw new BaseException("NT账号对应的人员不存在！");
        }
        List<String> noExistNts = new ArrayList<>();
        for (String nt : nts) {
            if (!nt2empMap.containsKey(nt) || null == nt2empMap.get(nt)) {
                noExistNts.add(nt);
            }
        }
        if (!CollectionUtils.isEmpty(noExistNts)) {
            throw new BaseException("NT账号:" + noExistNts.toString() + "不存在！");
        }
    }

    @Override
    public CommonResult addAgent(ObjectId loginId, ObjectId agentUserId, ObjectId agentedUserId, ObjectId typeId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        Assert.notNull(agentUserId, "代理人不能为空！");
        Assert.notNull(agentedUserId, "被代理人不能为空！");
        Assert.isTrue(SysDefConstants.DEF_ID_LOGIN_SYS_EXAM_MGE_VALUE.equals(typeId), "代理类型不是登录系统考试管理");
        TeSysUser loginUser = sysUserService.findById(loginId);
        TeSysUser agentUser = sysUserService.findById(agentUserId);
        TeSysUser agentedUser = sysUserService.findById(agentedUserId);
        Assert.notNull(loginUser, "当前登陆用户不存在！");
        Assert.notNull(agentUser, "代理人用户不存在！");
        Assert.notNull(agentedUser, "被代理人用户不存在！");
        if (validAgentedOthers(agentUserId)) {
            return CommonResult.fail("该代理人已代理了其他人，一个代理人不能同时代理多个人，请修改后提交！");
        }
        TeSysUserAgent newAgent = new TeSysUserAgent();
        newAgent.setIsValid(true);
        newAgent.setAddTime(new Date());
        newAgent.setType(new TeIdNameCn(SysDefConstants.DEF_ID_LOGIN_SYS_EXAM_MGE_VALUE, SysDefConstants.DEF_NAME_LOGIN_SYS_EXAM_MGE_VALUE, null));
        newAgent.setAgent(agentUser.trans2User());
        newAgent.setUser(agentedUser.trans2User());
        newAgent.setAddUser(loginUser.trans2User());
        sysUserAgentDao.save(newAgent);
        return CommonResult.success();
    }

    private boolean validAgentedOthers(ObjectId agentUserId) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysUserAgent_isValid, true));
        conds.add(new DC_E(DFN.sysUserAgent_type.dot(DFN.common_cid), SysDefConstants.DEF_ID_LOGIN_SYS_EXAM_MGE_VALUE));
        conds.add(new DC_E(DFN.sysUserAgent_agent.dot(DFN.common_userId), agentUserId));
        long count = sysUserAgentDao.countByConds(conds);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public CommonResult editAgent(ObjectId loginId, ObjectId agentId, ObjectId agentUserId, ObjectId agentedUserId, ObjectId typeId) {
        Assert.notNull(loginId, "当前登陆用户不存在！");
        Assert.notNull(agentId, "当前登陆用户不存在！");
        TeSysUserAgent agent = sysUserAgentDao.findById(agentId);
        Assert.notNull(agent, "当前代理关系不存在！");
        sysUserAgentDao.deleteById(agent.getId());
        return addAgent(loginId, agentUserId, agentedUserId, typeId);
    }

    @Override
    public void deleteAgentRelations(ObjectId loginId, List<ObjectId> agentIds) {
        Assert.notNull(agentIds, "当前登陆用户不存在！");
        Assert.isTrue(!CollectionUtils.isEmpty(agentIds), "代理id不能为空！");
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysUserAgent_isValid, true));
        conds.add(new DC_E(DFN.sysUserAgent_type.dot(DFN.common_cid), SysDefConstants.DEF_ID_LOGIN_SYS_EXAM_MGE_VALUE));
        conds.add(new DC_I(DFN.sysUserAgent_id, agentIds));
        long count = sysUserAgentDao.countByConds(conds);
        if (count < 1) {
            throw new BaseException("代理关系不存在！");
        }
        sysUserAgentDao.deleteByConds(conds);
    }

    /**
     * 代理关系视图
     */
    @Override
    public PageBean agentRelationView(ObjectId agentUserId, ObjectId agentedUserId, Pager pager) {
        PageBean pageBean = new PageBean();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysUserAgent_isValid, true));
        conds.add(new DC_E(DFN.sysUserAgent_type.dot(DFN.common_cid), SysDefConstants.DEF_ID_LOGIN_SYS_EXAM_MGE_VALUE));
        if (null != agentUserId) {
            conds.add(new DC_E(DFN.sysUserAgent_agent.dot(DFN.common_userId), agentUserId));
        }
        if (null != agentedUserId) {
            conds.add(new DC_E(DFN.sysUserAgent_user.dot(DFN.common_userId), agentedUserId));
        }
        long count = sysUserAgentDao.countByConds(conds);
        pageBean.setCount((int) count);
        if (count < 1) {
            return pageBean;
        }
        List<TeSysUserAgent> agentRelations = sysUserAgentDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()), pager);
        if (CollectionUtils.isEmpty(agentRelations)) {
            return pageBean;
        }
        List<ObjectId> agentUserIds = agentRelations.stream().map(TeSysUserAgent::getAgent).map(TeUser::getUserId).collect(Collectors.toList());
        List<ObjectId> agentUseredIds = agentRelations.stream().map(TeSysUserAgent::getUser).map(TeUser::getUserId).collect(Collectors.toList());
        Set<ObjectId> allUserIds = new HashSet<>();
        allUserIds.addAll(agentUserIds);
        allUserIds.addAll(agentUseredIds);
        conds.clear();
        conds.add(new DC_E(DFN.sysUserAgent_isValid, true));
        conds.add(new DC_I(DFN.sysUser_id, new ArrayList(allUserIds)));
        List<TeSysUser> sysUsers = sysUserDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()));
        if (CollectionUtils.isEmpty(sysUsers)) {
            return pageBean;
        }
        Map<ObjectId, TeSysUser> empId2TeSysUserMap = sysUsers.stream().collect(Collectors.toMap(emp -> emp.getId(), emp -> emp));
        if (CollectionUtils.isEmpty(sysUsers)) {
            return pageBean;
        }
        List<Map<String, Object>> views = new ArrayList<>();
        for (TeSysUserAgent agentRelation : agentRelations) {
            if (null == agentRelation.getAgent() || null == agentRelation.getAgent().getUserId()
                    || null == agentRelation.getUser() || null == agentRelation.getUser().getUserId()) {
                continue;
            }
            TeSysUser agentUser = empId2TeSysUserMap.get(agentRelation.getAgent().getUserId());
            TeSysUser agentedUser = empId2TeSysUserMap.get(agentRelation.getUser().getUserId());
            Map<String, Object> agentRelationMap = new HashMap<>();
            agentRelationMap.put("id", agentRelation.getId());
            agentRelationMap.put("agentCc", null != agentUser ? (agentUser.getCcId() + "/" + agentUser.getCcNameCn()) : "");
            agentRelationMap.put("agentUser", agentUser);
            agentRelationMap.put("agentedUser", agentedUser);
            agentRelationMap.put("agentedCc", null != agentedUser ? (agentedUser.getCcId() + "/" + agentedUser.getCcNameCn()) : "");
            agentRelationMap.put("agentType", agentRelation.getType());
            views.add(agentRelationMap);
        }
        pageBean.setObjectList(views);
        return pageBean;
    }

    /**
     * 代理人创建的考试角色人员
     *
     * @param agentId
     * @return
     */
    private List<ObjectId> getAgentRoleUserIds(ObjectId agentId) {
        List<ObjectId> userIds = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDefRoleUser_isValid, true));
        conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId), SysDefConstants.DEF_ID_EXAM_ROLE));
        conds.add(new DC_E(DFN.common_addUser.dot(DFN.common_userId), agentId));
        long count = sysDefRoleUserDao.countByConds(conds);
        if (count < 1) {
            return userIds;
        }
        List<TeSysDefRoleUser> roleUsers = sysDefRoleUserDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.common_addTime.n()));
        userIds = roleUsers.stream().map(TeSysDefRoleUser::getRoleUser).map(TeSysDefRoleUser2User::getUserId).collect(Collectors.toList());
        return userIds;
    }

    @Override
    public PageBean queryCorrectAnswersList(CorrectAnswersListParam param) {
        PageBean result = new PageBean();
        //先查sysUserPfm表查出答题记录然后关联sysUser表捞出人员角色,最后根据题目id查出对应答过的题库
        List<ObjectId> userIds = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        List<DbFieldName> fieldNames = new ArrayList<>();
        List<ObjectId> userIdsFromManager = new ArrayList<>();
        if (StringUtil.isNotNull(param.getRespId())) {
            conds.add(new DC_E(DFN.sysUser_manager.dot(DFN.common_userId), param.getRespId()));
            fieldNames.add(DFN.sysUser_id);
            List<TeSysUser> sysUsers = sysUserDao.findByFieldAndConds(conds, fieldNames);
            if (!CollectionUtils.isEmpty(sysUsers)) {
                userIdsFromManager = sysUsers.stream().map(TeSysUser::getId).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(userIdsFromManager) && !userIdsFromManager.contains(param.getRespId())) {
                userIdsFromManager.add(param.getRespId());
            }
            if (CollectionUtils.isEmpty(userIdsFromManager)) {
                return null;
            }
        }
        List<ObjectId> roleUserIdsFromAgent = new ArrayList<>();
        if (StringUtil.isNotNull(param.getAgentId())) {
            roleUserIdsFromAgent = this.getAgentRoleUserIds(param.getAgentId());
            if (CollectionUtils.isEmpty(roleUserIdsFromAgent)) {
                return null;
            }
        }
        //取负责人下和代理人下的交集
        //（选了负责人、 负责人下人员不为空）且（选了代理人、代理人下角色人员不为空 ）,取2个交集
        if ((StringUtil.isNotNull(param.getAgentId()) && StringUtil.isNotNull(param.getRespId())) &&
                !CollectionUtils.isEmpty(userIdsFromManager) && !CollectionUtils.isEmpty(roleUserIdsFromAgent)) {
            for (ObjectId objectId : userIdsFromManager) {
                if (roleUserIdsFromAgent.contains(objectId)) {
                    userIds.add(objectId);
                }
            }
        }
        if (StringUtil.isNotNull(param.getAgentId()) && StringUtil.isNull(param.getRespId())) {
            userIds.addAll(roleUserIdsFromAgent);
        }
        if (StringUtil.isNull(param.getAgentId()) && StringUtil.isNotNull(param.getRespId())) {
            userIds.addAll(userIdsFromManager);
        }
        Document sysUerPfmMatch = new Document();
        {
            sysUerPfmMatch.append(DFN.sysUserPfm_type.dot(DFN.common_cid).n(), SysDefConstants.DEF_ID_LOGIN_TEST_RCD);
            sysUerPfmMatch.append(DFN.sysUserPfm_isValid.n(), true);
        }
        Document sysUerPfmLookUp = new Document();
        {
            sysUerPfmLookUp.append("from", DBT.SYS_USER.n());
            sysUerPfmLookUp.append("localField", DFN.sysUserPfm_emp.dot(DFN.common_userId).n());
            sysUerPfmLookUp.append("foreignField", DFN.common__id.n());
            sysUerPfmLookUp.append("as", DFN.join_sysUser.n());
        }
        Document sysUserPfmUnwind = new Document();
        {
            sysUserPfmUnwind.append("path", DFN.join_sysUser.$n());
        }
        Document macthDocument = new Document();
        {
            if (StringUtil.isNotNull(param.getRoleName())) {
                if (!SysDefConstants.DEF_ID_ALL_ROLE.equals(param.getRoleName())) {
                    macthDocument.append(DFN.join_sysUser.dot(DFN.sysUser_examRole.dot(DFN.common_name)).n(), new Document("$regex", param.getRoleName()));
                }
            }
            if (StringUtil.isNotNull(param.getCcKey())) {
                Document ccCodeDocument = new Document();
                Document ccNameDocument = new Document();
                Document ccNameCnDocument = new Document();
                ccCodeDocument.append(DFN.join_sysUser.dot(DFN.sysUser_ccId).n(), new Document("$regex", param.getCcKey()));
                ccNameDocument.append(DFN.join_sysUser.dot(DFN.sysUser_ccName).n(), new Document("$regex", param.getCcKey()));
                ccNameCnDocument.append(DFN.join_sysUser.dot(DFN.sysUser_ccNameCn).n(), new Document("$regex", param.getCcKey()));
                macthDocument.append("$or", Arrays.asList(ccCodeDocument, ccNameDocument, ccNameCnDocument));
            }
            if (StringUtil.isNotNull(param.getAnswerTimeStart()) && StringUtil.isNotNull(param.getAnswerTimeEnd())) {
                Document startTimeDocument = new Document();
                Document endDocument = new Document();
                startTimeDocument.append(DFN.sysUserPfm_addTime.n(), new Document("$gte", DateUtil.parseDate(param.getAnswerTimeStart() + DateUtil.DAY_START)));
                endDocument.append(DFN.sysUserPfm_addTime.n(), new Document("$lte", DateUtil.parseDate(param.getAnswerTimeEnd() + DateUtil.DAY_DEADLINE)));
                macthDocument.append("$and", Arrays.asList(startTimeDocument, endDocument));
            }
            if (!CollectionUtils.isEmpty(userIds)) {
                //选的人员在负责人底下的情况
                if (StringUtil.isNotNull(param.getUserId()) && userIds.contains(param.getUserId())) {
                    macthDocument.append(DFN.sysUserPfm_emp.dot(DFN.common_userId).n(), param.getUserId());
                }
                //选的人员不在负责人底下的情况
                if (StringUtil.isNotNull(param.getUserId()) && !userIds.contains(param.getUserId())) {
                    return null;
                }
                //只选了负责人的情况
                if (!CollectionUtils.isEmpty(userIds) && StringUtil.isNull(param.getUserId())) {
                    macthDocument.append(DFN.sysUserPfm_emp.dot(DFN.common_userId).n(), new Document("$in", userIds));
                }
            }
            //只选了人员的情况
            if (StringUtil.isNotNull(param.getUserId()) && CollectionUtils.isEmpty(userIds)) {
                macthDocument.append(DFN.sysUserPfm_emp.dot(DFN.common_userId).n(), param.getUserId());
            }
        }
        Document groupDocument = new Document();
        {
            Document idDocument = new Document();
            idDocument.append("user", DFN.sysUserPfm_emp.$n());
            idDocument.append(DFN.sysUserPfm_ccCode.n(), DFN.join_sysUser.dot(DFN.sysUser_ccId).$n());
            idDocument.append(DFN.sysUserPfm_ccName.n(), DFN.join_sysUser.dot(DFN.sysUser_ccNameCn).$n());
            idDocument.append("role", DFN.join_sysUser.dot(DFN.sysUser_examRole).$n());
            idDocument.append("manager", DFN.join_sysUser.dot(DFN.sysUser_manager).$n());
            groupDocument.append(DFN.common__id.n(), idDocument);
            groupDocument.append(DFN.sysUserPfm_itemInfo.n(), new Document("$push", DFN.sysUserPfm_itemInfo.$n()));
            groupDocument.append("answerCount", new Document("$sum", 1));
        }
        Document groupContent = new Document();
        groupContent.append("_id", null);
        groupContent.append("count", new Document("$sum", 1));
        List<Document> countAggregate = new ArrayList<>();
        countAggregate.add(new Document("$match", sysUerPfmMatch));
        countAggregate.add(new Document("$lookup", sysUerPfmLookUp));
        countAggregate.add(new Document("$unwind", sysUserPfmUnwind));
        if (Objects.nonNull(macthDocument) && macthDocument.size() > 0) {
            countAggregate.add(new Document("$match", macthDocument));
        }
        countAggregate.add(new Document("$group", groupDocument));
        countAggregate.add(new Document("$group", groupContent));
        MongoCursor<Document> outPutCount = this.mongoTemplate.getCollection(DBT.SYS_USER_PFM.n()).aggregate(countAggregate)
                .allowDiskUse(true).cursor();
        Integer total = 0;
        if (outPutCount.hasNext()) {
            org.bson.Document next = outPutCount.next();
            total = (Integer) next.get("count");
        }
        if (total == 0) {
            return null;
        }
        result.setCount(total);
        List<Document> aggregate = new ArrayList<>();
        aggregate.add(new Document("$lookup", sysUerPfmLookUp));
        aggregate.add(new Document("$match", sysUerPfmMatch));
        aggregate.add(new Document("$unwind", sysUserPfmUnwind));
        if (Objects.nonNull(macthDocument) && macthDocument.size() > 0) {
            aggregate.add(new Document("$match", macthDocument));
        }
        aggregate.add(new Document("$group", groupDocument));
        if (param.getIndex() != null && param.getSize() != null) {
            Integer index = param.getIndex();
            Integer size = param.getSize();
            aggregate.add(new Document("$skip", index * size));
            aggregate.add(new Document("$limit", size));
        }
        MongoCursor<Document> cursor = mongoTemplate.getCollection(DBT.SYS_USER_PFM.n())
                .aggregate(aggregate).allowDiskUse(true).cursor();
        List<CorrectAnswersVo> list = new ArrayList<>();
        Set<ObjectId> itemIdSet = new HashSet<>();
        if (cursor != null) {
            while (cursor.hasNext()) {
                CorrectAnswersVo vo = new CorrectAnswersVo();
                Document dbObject = cursor.next();
                Document idObject = ((Document) dbObject.get("_id"));
                vo.setAnswerCount(StringUtil.to(dbObject.get("answerCount"),Integer.class));
                List itemInfoDocument = ((List) dbObject.get("itemInfo"));
                Document user = ((Document) idObject.get("user"));
                List<Document> roleList = ((List) idObject.get("role"));
                String loginName = StringUtil.to(user.get("loginName"), String.class);
                String userName = StringUtil.to(user.get("userName"), String.class);
                ObjectId userId = StringUtil.toObjectId(user.get("userId"));
                String ccCode = StringUtil.to(idObject.get("ccCode"), String.class);
                String ccName = StringUtil.to(idObject.get("ccName"), String.class);
                String roleName = "";
                Document resp = ((Document) idObject.get("manager"));
                String respLoginName = StringUtil.to(resp.get("loginName"), String.class);
                String respUserName = StringUtil.to(resp.get("userName"), String.class);
                if (!CollectionUtils.isEmpty(roleList)) {
                    for (Document role : roleList) {
                        roleName += StringUtil.to(role.get("name"), String.class) + ",";
                    }
                    roleName = roleName.substring(0, roleName.length() - 1);
                }
                vo.setRespName(getString(respUserName, respLoginName));
                vo.setRoleName(roleName);
                vo.setUserName(getString(userName, loginName));
                vo.setCc(getString(ccCode, ccName));
                vo.setUserId(userId);
                if (!CollectionUtils.isEmpty(itemInfoDocument)) {
                    Integer totalTimes = 0;
                    Integer correctTimes = 0;
                    Integer errorTimes = 0;
                    List<ObjectId> itemIdList = new ArrayList<>();
                    for (int i = 0; i < itemInfoDocument.size(); i++) {
                        List<Document> itemInfoList = ((List<Document>) itemInfoDocument.get(i));
                        if (!CollectionUtils.isEmpty(itemInfoList)) {
                            totalTimes += itemInfoList.size();
                            for (Document itemInfo : itemInfoList) {
                                Document item = ((Document) itemInfo.get("item"));
                                ObjectId itemId = StringUtil.toObjectId(item.get("cid"));
                                itemIdSet.add(itemId);
                                itemIdList.add(itemId);
                                Double score = ((Double) itemInfo.get("score"));
                                if (Double.doubleToLongBits(0.0) == Double.doubleToLongBits(score)) {
                                    errorTimes++;
                                } else {
                                    correctTimes++;
                                }
                            }
                        }
                    }
                    vo.setItemIdList(itemIdList);
                    int accuracy = ((int) BigDecimalUtils.divideDouble(correctTimes.doubleValue() * 100, totalTimes.doubleValue(), 0));
                    vo.setTotalTimes(totalTimes);
                    vo.setErrorTimes(errorTimes);
                    vo.setCorrectTimes(correctTimes);
                    vo.setAccuracy(accuracy + "%");
                    vo.setAccuracySort(accuracy);
                } else {
                    vo.setCorrectTimes(0);
                    vo.setErrorTimes(0);
                    vo.setTotalTimes(0);
                    vo.setAccuracy("0%");
                    vo.setAccuracySort(0);
                }
                list.add(vo);
            }
        }
        conds.clear();
        fieldNames.clear();
        //通过答题记录里的题目id拿到所属题库
        List<TeSysDef> sysDefs = sysDefDao.getSysDefsByIds(new ArrayList<>(itemIdSet));
        Map<ObjectId, String> map = new HashMap<>();
        for (TeSysDef sysDef : sysDefs) {
            ObjectId sysDefId = sysDef.getId();
            TeSrcDef srcDef = sysDef.getSrcDef();
            String srcDefName = srcDef.getSrcDefName();
            map.put(sysDefId, srcDefName);
        }
        list = list.stream().sorted(Comparator.comparing(CorrectAnswersVo::getAccuracySort).reversed()).collect(Collectors.toList());
        for (CorrectAnswersVo vo : list) {
            List<ObjectId> itemIdList = vo.getItemIdList();
            String questionBanks = "";
            for (ObjectId objectId : itemIdList) {
                String questionBank = map.get(objectId);
                if (StringUtil.isNotNull(questionBank) && !questionBanks.contains(questionBank)) {
                    questionBanks += questionBank + ";";
                }
            }
            if (StringUtil.isNotNull(questionBanks)) {
                questionBanks = questionBanks.substring(0, questionBanks.length() - 1);
            }
            vo.setQuestionBank(questionBanks);
        }
        result.setObjectList(list);
        return result;
    }

    @Override
    public PageBean exportCorrectAnswersList(CorrectAnswersListParam param) {
        PageBean result = new PageBean();
        List<LinkedHashMap<String, Object>> data = new ArrayList<LinkedHashMap<String, Object>>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("cc", "CC");
        map.put("userName", "人员");
        map.put("respName", "负责人");
        map.put("roleName", "角色");
        map.put("questionBank", "题库");
        map.put("answerCount", "答题天数");
        map.put("totalTimes", "总次数");
        map.put("correctTimes", "正确次数");
        map.put("errorTimes", "错误次数");
        map.put("accuracy", "正确率");
        data.add(map);
        List<CorrectAnswersVo> list = this.queryCorrectAnswersList(param).getObjectList();
        if (!CollectionUtils.isEmpty(list)) {
            for (CorrectAnswersVo reslutVo : list) {
                LinkedHashMap<String, Object> hashMap = new LinkedHashMap<String, Object>();
                hashMap.put("cc", reslutVo.getCc());
                hashMap.put("userName", reslutVo.getUserName());
                hashMap.put("respName", reslutVo.getRespName());
                hashMap.put("roleName", reslutVo.getRoleName());
                hashMap.put("questionBank", reslutVo.getQuestionBank());
                hashMap.put("answerCount", reslutVo.getAnswerCount());
                hashMap.put("totalTimes", reslutVo.getTotalTimes());
                hashMap.put("correctTimes", reslutVo.getCorrectTimes());
                hashMap.put("errorTimes", reslutVo.getErrorTimes());
                hashMap.put("accuracy", reslutVo.getAccuracy());
                data.add(hashMap);
            }
        }
        result.setObjectList(data);
        result.setCount(list.size());
        return result;
    }

    @Override
    public PageBean queryWrongQuestionDetails(ObjectId userId, Integer index, Integer size, String answerTimeStart, String answerTimeEnd) {
        PageBean result = new PageBean();
        Document sysUerPfmMatch = new Document();
        {
            sysUerPfmMatch.append(DFN.sysUserPfm_emp.dot(DFN.common_userId).n(), userId);
            sysUerPfmMatch.append(DFN.sysUserPfm_type.dot(DFN.common_cid).n(), SysDefConstants.DEF_ID_LOGIN_TEST_RCD);
            sysUerPfmMatch.append(DFN.sysUserPfm_isValid.n(), true);
            if (StringUtil.isNotNull(answerTimeStart) && StringUtil.isNotNull(answerTimeEnd)) {
                Document startTimeDocument = new Document();
                Document endDocument = new Document();
                startTimeDocument.append(DFN.sysUserPfm_addTime.n(), new Document("$gte", DateUtil.parseDate(answerTimeStart + DateUtil.DAY_START)));
                endDocument.append(DFN.sysUserPfm_addTime.n(), new Document("$lte", DateUtil.parseDate(answerTimeEnd + DateUtil.DAY_DEADLINE)));
                sysUerPfmMatch.append("$and", Arrays.asList(startTimeDocument, endDocument));
            }
        }
        Document sysUerPfmLookUp = new Document();
        {
            sysUerPfmLookUp.append("from", DBT.SYS_USER.n());
            sysUerPfmLookUp.append("localField", DFN.sysUserPfm_emp.dot(DFN.common_userId).n());
            sysUerPfmLookUp.append("foreignField", DFN.common__id.n());
            sysUerPfmLookUp.append("as", DFN.join_sysUser.n());
        }
        Document sysUserPfmUnwind = new Document();
        {
            sysUserPfmUnwind.append("path", DFN.join_sysUser.$n());
        }
        Document unwind = new Document();
        {
            unwind.append("path", DFN.sysUserPfm_itemInfo.$n());
        }
        Document match = new Document();
        {
            match.append("itemInfo.score", 0.0);
        }
        List<Document> aggregate = new ArrayList<>();
        List<Document> countAggregate = new ArrayList<>();
        countAggregate.add(new Document("$match", sysUerPfmMatch));
        countAggregate.add(new Document("$lookup", sysUerPfmLookUp));
        countAggregate.add(new Document("$unwind", sysUserPfmUnwind));
        countAggregate.add(new Document("$unwind", unwind));
        countAggregate.add(new Document("$unwind", unwind));
        Document groupContent = new Document();
        groupContent.append("_id", null);
        groupContent.append("count", new Document("$sum", 1));
        countAggregate.add(new Document("$match", match));
        countAggregate.add(new Document("$group", groupContent));
        MongoCursor<Document> outPutCount = this.mongoTemplate.getCollection(DBT.SYS_USER_PFM.n()).aggregate(countAggregate)
                .allowDiskUse(true).cursor();
        Integer total = 0;
        if (outPutCount.hasNext()) {
            org.bson.Document next = outPutCount.next();
            total = (Integer) next.get("count");
        }
        if (total == 0) {
            return null;
        }
        result.setCount(total);
        aggregate.add(new Document("$match", sysUerPfmMatch));
        aggregate.add(new Document("$lookup", sysUerPfmLookUp));
        aggregate.add(new Document("$unwind", sysUserPfmUnwind));
        aggregate.add(new Document("$unwind", unwind));
        aggregate.add(new Document("$unwind", unwind));
        aggregate.add(new Document("$match", match));
        aggregate.add(new Document("$sort", new Document("addTime", -1)));
        if (index != null && size != null) {
            aggregate.add(new Document("$skip", index * size));
            aggregate.add(new Document("$limit", size));
        }
        MongoCursor<Document> cursor = mongoTemplate.getCollection(DBT.SYS_USER_PFM.n())
                .aggregate(aggregate).allowDiskUse(true).cursor();
        List<WrongQuestionDetailsVo> list = new ArrayList<>();
        Set<ObjectId> itemIdSet = new HashSet();
        if (cursor != null) {
            while (cursor.hasNext()) {
                WrongQuestionDetailsVo vo = new WrongQuestionDetailsVo();
                Document document = cursor.next();
                Date addTime = StringUtil.to(document.get("addTime"), Date.class);
                Document itemInfo = ((Document) document.get("itemInfo"));
                Document item = ((Document) itemInfo.get("item"));
                ObjectId itemId = StringUtil.toObjectId(item.get("cid"));
                String name = StringUtil.to(item.get("name"), String.class);
                String answer = StringUtil.to(itemInfo.get("answer"), String.class);
                String stdAnswer = StringUtil.to(itemInfo.get("stdAnswer"), String.class);
                vo.setItemId(itemId);
                itemIdSet.add(itemId);
                vo.setAnswerTime(DateUtil.formatDate2Str(addTime, DateUtil.DATE_FORMAT));
                vo.setTitle(name);
                vo.setUserAnswer(answer);
                vo.setAnswer("【正确答案：" + stdAnswer + "，您选的是：" + answer + "】");
                Document sysUserDocument = ((Document) document.get(DFN.join_sysUser.n()));
                String ccCode = StringUtil.to(sysUserDocument.get("ccId"), String.class);
                String ccName = StringUtil.to(sysUserDocument.get("ccNameCn"), String.class);
                if (StringUtil.isNull(ccName)) {
                    ccName = StringUtil.to(sysUserDocument.get("ccName"), String.class);
                }
                Document emp = ((Document) document.get("emp"));
                String userName = StringUtil.to(emp.get("userName"), String.class);
                String loginName = StringUtil.to(emp.get("loginName"), String.class);
                vo.setUserName(getString(userName, loginName));
                vo.setCc(getString(ccCode, ccName));
                List<Document> roleList = ((List) sysUserDocument.get("examRole"));
                if (!CollectionUtils.isEmpty(roleList)) {
                    String roleName = "";
                    for (Document role : roleList) {
                        roleName += StringUtil.to(role.get("name"), String.class) + ";";
                    }
                    roleName = roleName.substring(0, roleName.length() - 1);
                    vo.setRoleName(roleName);
                }
                list.add(vo);
            }
        }
        //通过答题记录里的题目id拿到所属题库
        List<TeSysDef> examQuests = sysDefDao.getSysDefsByIds(new ArrayList<>(itemIdSet));
        Map<ObjectId, String> idToExamQuestBank = new HashMap<>();
        for (TeSysDef examQuest : examQuests) {
            ObjectId sysDefId = examQuest.getId();
            TeSrcDef srcDef = examQuest.getSrcDef();
            String srcDefName = srcDef.getSrcDefName();
            idToExamQuestBank.put(sysDefId, srcDefName);
        }
        List<IDbCondition> conds = new ArrayList<>();
        List<DbFieldName> fieldNames = new ArrayList<>();
        //拿到选项
        conds.add(new DC_E(DFN.sysDef__isValid, true));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION));
        conds.add(new DC_I<ObjectId>(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId), new ArrayList<>(itemIdSet)));
        fieldNames.add(DFN.sysDef__srcDef);
        fieldNames.add(DFN.sysDef__defName);
        fieldNames.add(DFN.sysDef__codeName);
        List<TeSysDef> examQuestOptions = sysDefDao.findByFieldAndConds(conds, fieldNames);
        Map<ObjectId, Map> idToOptions = new HashMap<>();
        for (TeSysDef optionDef : examQuestOptions) {
            TeSrcDef srcDef = optionDef.getSrcDef();
            ObjectId srcDefId = srcDef.getSrcDefId();
            String defName = optionDef.getDefName();
            String codeName = optionDef.getCodeName();
            if (idToOptions.get(srcDefId) == null) {
                Map<String, String> optionToName = new HashMap<>();
                optionToName.put(codeName, defName);
                idToOptions.put(srcDefId, optionToName);
            } else {
                Map<String, String> optionToName = idToOptions.get(srcDefId);
                optionToName.put(codeName, defName);
                idToOptions.put(srcDefId, optionToName);
            }
        }
        for (WrongQuestionDetailsVo vo : list) {
            ObjectId itemId = vo.getItemId();
            String examQuestBank = "";
            if (idToExamQuestBank.get(itemId) != null) {
                examQuestBank = idToExamQuestBank.get(itemId);
            }
            String optionToA = "";
            String optionToB = "";
            String optionToC = "";
            String optionToD = "";
            if (idToOptions.get(itemId) != null) {
                Map<String, String> map = idToOptions.get(itemId);
                optionToA = "A" + map.get("A") + ";";
                optionToB = "B" + map.get("B") + ";";
                optionToC = "C" + map.get("C") + ";";
                optionToD = "D" + map.get("D");
            }

            String title = vo.getTitle();
            String answer = vo.getAnswer();
            String wrongQuestionDetail = title + "    " + optionToA + optionToB + optionToC + optionToD + answer;
            vo.setWrongQuestionDetail(wrongQuestionDetail);
            vo.setQuestionBank(examQuestBank);
        }
        result.setObjectList(list);
        return result;
    }

    @Override
    public PageBean exportWrongQuestionDetails(ObjectId userId, String answerTimeStart, String answerTimeEnd) {
        PageBean result = new PageBean();
        List<LinkedHashMap<String, Object>> data = new ArrayList<LinkedHashMap<String, Object>>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("cc", "CC");
        map.put("userName", "人员");
        map.put("roleName", "角色");
        map.put("questionBank", "题库");
        map.put("wrongQuestionDetail", "题目");
        map.put("answer", "用户选的");
        map.put("answerTime", "答题时间");
        data.add(map);
        List<WrongQuestionDetailsVo> list = this.queryWrongQuestionDetails(userId, null, null, answerTimeStart, answerTimeEnd).getObjectList();
        if (!CollectionUtils.isEmpty(list)) {
            for (WrongQuestionDetailsVo reslutVo : list) {
                LinkedHashMap<String, Object> hashMap = new LinkedHashMap<String, Object>();
                hashMap.put("cc", reslutVo.getCc());
                hashMap.put("userName", reslutVo.getUserName());
                hashMap.put("roleName", reslutVo.getRoleName());
                hashMap.put("questionBank", reslutVo.getQuestionBank());
                hashMap.put("wrongQuestionDetail", reslutVo.getWrongQuestionDetail());
                hashMap.put("answer", reslutVo.getUserAnswer());
                hashMap.put("answerTime", reslutVo.getAnswerTime());
                data.add(hashMap);
            }
        }
        result.setObjectList(data);
        result.setCount(list.size());
        return result;
    }

    @Override
    public LinkedHashMap<String, List<LinkedHashMap<String, Object>>> exportQuestionBankMgt(ExamParamVo examParam) {
        examParam.setPager(null);
        PageBean data = getQuestionBankMgt(examParam);
        List<QuestionBankVo> reportList = data.getObjectList();
        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> excelMap = new LinkedHashMap<String, List<LinkedHashMap<String, Object>>>();
        List<LinkedHashMap<String, Object>> list = new ArrayList<LinkedHashMap<String, Object>>();
        LinkedHashMap<String, Object> titleMap = new LinkedHashMap<String, Object>();
        int index = 0;
        titleMap.put("index", "序");
        titleMap.put("name", "题库名称");
        titleMap.put("role", "角色");
        titleMap.put("cc", "所属CC");
        titleMap.put("statusName", "状态");
        titleMap.put("addTime", "创建时间");
        titleMap.put("questNum", "题目数量");
        list.add(titleMap);
        List<ObjectId> questionBankIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reportList)) {
            for (QuestionBankVo questionBankVo : reportList) {
                questionBankIds.add(questionBankVo.getId());
                LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<String, Object>();
                linkedMap.put("index", ++index);
                linkedMap.put("name", questionBankVo.getName());
                linkedMap.put("role", questionBankVo.getRole());
                String ccId = questionBankVo.getCcId();
                String ccNameCn = questionBankVo.getCcNameCn();
                String name = "";
                if (StringUtil.isNotNull(ccId)) {
                    name = ccId + "/" + ccNameCn;
                } else {
                    name = ccNameCn;
                }
                linkedMap.put("cc", name);
                linkedMap.put("statusName", questionBankVo.getStatusName());
                linkedMap.put("addTime", questionBankVo.getAddTime());
                linkedMap.put("questNum", questionBankVo.getQuestNum());
                list.add(linkedMap);
            }
        }
        excelMap.put("题库查询", list);
        LinkedHashMap<String, Object> titleMap2 = new LinkedHashMap<>();
        titleMap2.put("index", "序");
        titleMap2.put("questionBankName", "所属题库名称");
        titleMap2.put("name", "题目名称");
        titleMap2.put("answer", "答案");
        titleMap2.put("optionAcontent", "选项A");
        titleMap2.put("optionBcontent", "选项B");
        titleMap2.put("optionCcontent", "选项C");
        titleMap2.put("optionDcontent", "选项D");
        titleMap2.put("author", "出题人");
        titleMap2.put("addtime", "创建时间");
        List<LinkedHashMap<String, Object>> list2 = new ArrayList<>();
        list2.add(titleMap2);
        List<Document> sql = new ArrayList<>();
        Document mainConds = new Document();
        mainConds.put(DFN.sysDef__isValid.n(), true);
        mainConds.put(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId).n(), SysDefConstants.DEF_ID_EXAM_QUEST);
        //题库下的题目
        mainConds.put(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n(), new Document("$in", questionBankIds));
        String joinAlias = DFN.join_sysDef.n();
        org.bson.Document lookUp = new org.bson.Document()
                .append("from", DBT.SYS_DEF.n())
                .append("localField", DFN.sysDef_id.n())
                .append("foreignField", DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n())
                .append("as", joinAlias);
        org.bson.Document optionConds = new org.bson.Document();
        optionConds.put(DFN.join_sysDef.dot(DFN.sysDef__isValid).n(), true);
        optionConds.put(DFN.join_sysDef.dot(DFN.sysDef__defType).dot(DFN.sysDef__defTypeId).n(), SysDefConstants.DEF_ID_EXAM_QUEST_OPTION);
        sql.add(new org.bson.Document("$match", mainConds));
        sql.add(new org.bson.Document("$lookup", lookUp));
        sql.add(new org.bson.Document("$match", optionConds));
        sql.add(new org.bson.Document("$sort", new org.bson.Document(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId).n(), 1)));
        String collectionName = mongoTemplate.getCollectionName(TeSysDef.class);
        MongoCollection<Document> applyCollection = mongoTemplate.getCollection(collectionName);
        MongoCursor<Document> cursor = applyCollection.aggregate(sql).allowDiskUse(true).cursor();
        index = 0;
        if (null != cursor) {
            while (cursor.hasNext()) {
                LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<>();
                org.bson.Document output = cursor.next();
                linkedMap.put("index", ++index);
                linkedMap.put("questionBankName", getStringValue(output.get(DFN.sysDef__srcDef.n())));
                linkedMap.put("name", StringUtil.to(output.get(DFN.sysDef__defName.n()), String.class));
                linkedMap.put("answer", StringUtil.to(output.get(DFN.sysDef__codeName.n()), String.class));

                if (StringUtil.isNotNull(output.get(DFN.join_sysDef.n()))) {
                    List<Map<String, Object>> joinSysDefs = (List<Map<String, Object>>) output.get(DFN.join_sysDef.n());
                    if (!CollectionUtils.isEmpty(joinSysDefs)) {
                        for (Map<String, Object> joinSysDef : joinSysDefs) {
                            String option = StringUtil.to(joinSysDef.get(DFN.sysDef__codeName.n()), String.class);
                            if (SysDefConstants.EXAM_QUEST_OPTION_A.equals(option)) {
                                linkedMap.put("optionAcontent", StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_B.equals(option)) {
                                linkedMap.put("optionBcontent", StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_C.equals(option)) {
                                linkedMap.put("optionCcontent", StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                            if (SysDefConstants.EXAM_QUEST_OPTION_D.equals(option)) {
                                linkedMap.put("optionDcontent", StringUtil.to(joinSysDef.get(DFN.sysDef__defName.n()), String.class));
                            }
                        }
                    }
                }
                linkedMap.put("author", ReportUtil.custFieldFormatValue(output.get(DFN.common_addUser.n())));
                Date date = output.get(DFN.sysDef__addTime.n(), Date.class);
                linkedMap.put("addtime", DateUtil.formatDate2Str(date, DateUtil.DATE_FORMAT));
                list2.add(linkedMap);
            }
        }
        excelMap.put("题目查询", list2);
        return excelMap;
    }

    @Override
    public PageBean queryAnswerAnalysisReport(AnswerAnalysisReportParam param) {
        PageBean result = new PageBean();
        List<AnswerAnalysisReportVo> resultList = new ArrayList<>();
        List<AggregationOperation> aggList = listAggregation(param);
        Aggregation dateAgg = Aggregation.newAggregation(aggList).withOptions(new AggregationOptions.Builder().allowDiskUse(true).build());
        AggregationResults<AnswerAnalysisReportInfoBo> aggregate = mongoTemplate.aggregate(dateAgg, DBT.SYS_USER_PFM.n(), AnswerAnalysisReportInfoBo.class);
        List<AnswerAnalysisReportInfoBo> resultVoList = aggregate.getMappedResults();
        if (CollectionUtils.isEmpty(resultVoList)) {
            return result;
        }
        AnswerAnalysisReportInfoBo resultBo = resultVoList.get(0);
        List<MongoCountBo> countList = resultBo.getCount();
        if (CollectionUtils.isEmpty(countList)) {
            return result;
        }
        MongoCountBo mongoCountBo = countList.get(0);
        long total = mongoCountBo.getCount() == null ? 0L : mongoCountBo.getCount();
        if (total == 0) {
            return result;
        }
        result.setCount((int) total);
        List<AnswerAnalysisReportResultBo> dataList = resultBo.getData();
        int index = 0;
        for (AnswerAnalysisReportResultBo bo : dataList) {
            AnswerAnalysisReportVo vo = new AnswerAnalysisReportVo(bo);
            vo.setIndex(++index);
            resultList.add(vo);
        }
        result.setObjectList(resultList);
        return result;
    }

    @Override
    public void exportAnswerAnalysisReport(HttpServletResponse response, AnswerAnalysisReportParam param) throws IOException {
        PageBean data = queryAnswerAnalysisReport(param);
        List<AnswerAnalysisReportVo> excelList = data.getObjectList();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("题目分析.xlsx", "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "题目分析")
                .head(AnswerAnalysisReportVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(excelList, sheet);
        excelWriter.finish();
    }

    private List<AggregationOperation> listAggregation(AnswerAnalysisReportParam param) {
        String answerTimeStart = param.getAnswerTimeStart();
        String answerTimeEnd = param.getAnswerTimeEnd();
        ObjectId authorId = param.getAuthorId();
        String questionBank = param.getQuestionBank();
        String questionItem = param.getQuestionItem();
        List<AggregationOperation> aggList = new ArrayList<>();
        Criteria match1 = Criteria.where("isValid").is(true).and("type.cid").is(SysDefConstants.DEF_ID_LOGIN_TEST_RCD);
        Criteria match2 = Criteria.where("isValid").is(true);
        if (StringUtil.isNotNull(answerTimeStart) && StringUtil.isNotNull(answerTimeEnd)) {
            match2.and("addTime").gte(answerTimeStart).lte(answerTimeEnd);
        }
        if (StringUtil.isNotNull(authorId)) {
            match2.and("addUser.userId").is(authorId);
        }
        if (StringUtil.isNotNull(questionBank)) {
            match2.and("questionBank").regex(questionBank, "i");
        }
        if (StringUtil.isNotNull(questionItem)) {
            match2.and("item").regex(questionItem, "i");
        }
        aggList.add(Aggregation.match(match1));
        aggList.add(Aggregation.unwind("itemInfo", true));
        aggList.add(Aggregation.lookup("sysDef", "itemInfo.item.cid", "_id", "item"));
        aggList.add(Aggregation.unwind("item", true));
        aggList.add(Aggregation.project()
                .andInclude("emp", "addTime", "itemInfo", "isValid")
                .and("$item.addUser").as("addUser")
                .and("$item.srcDef.srcDefId").as("questionBankId"));
        aggList.add(Aggregation.lookup("sysDef", "questionBankId", "_id", "questionBank"));
        aggList.add(Aggregation.unwind("questionBank"));
        aggList.add(Aggregation.project().andInclude("emp", "addUser", "isValid", "itemInfo")
                .andExpression("{\"$dateToString\":{\"date\":\"$addTime\",\"format\":\"%Y-%m-%d\"}}").as("addTime")
                .and("$questionBank.defName").as("questionBank")
                .and("$itemInfo.item.name").as("item")
                .and("$itemInfo.item.cid").as("itemId"));
        aggList.add(Aggregation.match(match2));
        aggList.add(Aggregation.group("itemId").
                count().as("count").
                sum(ConditionalOperators.when(ComparisonOperators.Eq.valueOf("$itemInfo.score").equalToValue(1.0)).then(1).otherwise(0)).as("scoreCount").
                first("addUser").as("addUser").
                first("item").as("item").
                first("questionBank").as("questionBank").
                addToSet("emp.userId").as("emp"));
        aggList.add(Aggregation.project().andInclude("addUser", "questionBank", "count", "scoreCount", "item").
                andExpression("{\"$size\":\"$emp\"}").as("empCount"));
        aggList.add(Aggregation.addFields()
                .addFieldWithValue("accuracy", new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$round", Arrays.asList(
                                new Document("$multiply", Arrays.asList(
                                        new Document("$divide", Arrays.asList("$scoreCount", "$count")),
                                        100
                                )),
                                2
                        ));
                    }
                }).build());
        aggList.add(Aggregation.sort(Sort.Direction.ASC, "accuracy"));
        CountOperation count = Aggregation.count().as("count");
        if (param.getPageNo() != null && param.getPageSize() != null) {
            SkipOperation skip = Aggregation.skip((long) param.getPageNo() * param.getPageSize());
            LimitOperation limit = Aggregation.limit(param.getPageSize());
            aggList.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        } else {
            SkipOperation skip = Aggregation.skip(0L);
            aggList.add(Aggregation.facet(count).as("count").and(skip).as("data"));
        }
        return aggList;
    }

    private String getString(String str1, String str2) {
        if (StringUtil.isNull(str1) && StringUtil.isNull(str2)) {
            return "";
        }
        if (StringUtil.isNull(str1)) {
            return str2;
        }
        if (StringUtil.isNull(str2)) {
            return str1;
        }
        return str1 + "/" + str2;
    }


}
